@echo off
chcp 65001 >nul
title النظام المتكامل والمتطور - AdenLink اليافعي

echo.
echo ================================================================
echo 🚀 النظام المتكامل والمتطور لإدارة الاشتراكات السحابية
echo ================================================================
echo 💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
echo 🏢 شركة AdenLink - اليافعي
echo ================================================================
echo.

echo 📋 التحقق من متطلبات النظام...
echo.

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python 3.8 أو أحدث
    echo 🔗 رابط التحميل: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
echo.

echo 📦 تثبيت المكتبات المطلوبة...
echo.

REM تثبيت المكتبات
pip install flask flask-sqlalchemy flask-login werkzeug >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المكتبات
    echo 🔧 يرجى تشغيل الأمر التالي يدوياً:
    echo pip install flask flask-sqlalchemy flask-login werkzeug
    pause
    exit /b 1
)

echo ✅ تم تثبيت جميع المكتبات بنجاح
echo.

echo 🔥 بدء تشغيل النظام المتكامل...
echo.
echo ================================================================
echo 🌐 معلومات الوصول:
echo ================================================================
echo 🔗 الرابط: http://localhost:4020
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: 123456
echo ================================================================
echo 🎮 الميزات المتاحة:
echo ================================================================
echo 📊 • مخططات تفاعلية متطورة مع Chart.js
echo 📋 • إدارة الاشتراكات الشاملة مع أقسام متفرعة
echo 🧾 • إدارة الفواتير المتقدمة مع حساب تلقائي
echo 📧 • مركز التواصل مع نظام رسائل متطور
echo 👥 • إدارة المستخدمين مع صلاحيات متدرجة
echo ☁️ • إدارة مزودي الخدمة السحابية
echo 💳 • إدارة طرق الدفع المتعددة
echo 📈 • تقارير وتحليلات شاملة
echo 🎨 • واجهة متجاوبة مع تأثيرات متطورة
echo 🔐 • نظام أمان وصلاحيات متقدم
echo ================================================================
echo 🚀 النظام جاهز للاستخدام!
echo ================================================================
echo.

REM تشغيل النظام
python ultimate_advanced_system.py

echo.
echo ================================================================
echo 🛑 تم إيقاف النظام
echo ================================================================
echo 💡 لإعادة التشغيل، قم بتشغيل هذا الملف مرة أخرى
echo 📞 للدعم التقني: محمد ياسر الجبوري
echo 🏢 شركة AdenLink - اليافعي
echo ================================================================
pause
