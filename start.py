#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط
"""

from app import app, db, User

print("🚀 تشغيل نظام إدارة الاشتراكات")
print("="*50)

try:
    with app.app_context():
        # إنشاء قاعدة البيانات
        db.create_all()
        print("✅ تم إنشاء قاعدة البيانات")
        
        # إنشاء المستخدم الافتراضي
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام',
                role='admin'
            )
            admin_user.set_password('123456')
            db.session.add(admin_user)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي")
        else:
            print("ℹ️ المستخدم الافتراضي موجود")
    
    print("\n🌐 معلومات الوصول:")
    print("   الرابط: http://localhost:5000")
    print("   المستخدم: admin")
    print("   كلمة المرور: 123456")
    print("="*50)
    print("🚀 جاري تشغيل الخادم...")
    
    # تشغيل التطبيق
    app.run(debug=True, host='0.0.0.0', port=5000)
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
