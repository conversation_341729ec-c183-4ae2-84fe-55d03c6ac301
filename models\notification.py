#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج الإشعار
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy

# استيراد db من app.py سيتم لاحقاً
db = None

class Notification(db.Model):
    """نموذج الإشعار"""
    
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # معلومات أساسية
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(20), nullable=False)  # info, warning, error, success
    category = db.Column(db.String(30), nullable=False)  # subscription, invoice, system, security
    
    # الحالة
    is_read = db.Column(db.<PERSON>, default=False, nullable=False)
    is_sent = db.Column(db.<PERSON><PERSON>, default=False, nullable=False)
    
    # معلومات إضافية
    action_url = db.Column(db.String(255))  # رابط للإجراء المطلوب
    action_text = db.Column(db.String(50))  # نص الإجراء
    priority = db.Column(db.String(10), default='medium')  # low, medium, high, urgent
    
    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    read_at = db.Column(db.DateTime)
    sent_at = db.Column(db.DateTime)
    expires_at = db.Column(db.DateTime)  # تاريخ انتهاء صلاحية الإشعار
    
    # العلاقات
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    def __init__(self, **kwargs):
        super(Notification, self).__init__(**kwargs)
    
    def mark_as_read(self):
        """تحديد الإشعار كمقروء"""
        self.is_read = True
        self.read_at = datetime.utcnow()
    
    def mark_as_sent(self):
        """تحديد الإشعار كمرسل"""
        self.is_sent = True
        self.sent_at = datetime.utcnow()
    
    def is_expired(self):
        """التحقق من انتهاء صلاحية الإشعار"""
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False
    
    def get_type_icon(self):
        """الحصول على أيقونة النوع"""
        icons = {
            'info': 'fas fa-info-circle',
            'warning': 'fas fa-exclamation-triangle',
            'error': 'fas fa-times-circle',
            'success': 'fas fa-check-circle'
        }
        return icons.get(self.type, 'fas fa-bell')
    
    def get_type_color(self):
        """الحصول على لون النوع"""
        colors = {
            'info': 'info',
            'warning': 'warning',
            'error': 'danger',
            'success': 'success'
        }
        return colors.get(self.type, 'primary')
    
    def get_priority_color(self):
        """الحصول على لون الأولوية"""
        colors = {
            'low': 'secondary',
            'medium': 'primary',
            'high': 'warning',
            'urgent': 'danger'
        }
        return colors.get(self.priority, 'primary')
    
    def get_category_text(self):
        """الحصول على نص الفئة بالعربية"""
        categories = {
            'subscription': 'اشتراك',
            'invoice': 'فاتورة',
            'system': 'نظام',
            'security': 'أمان'
        }
        return categories.get(self.category, 'عام')
    
    def get_type_text(self):
        """الحصول على نص النوع بالعربية"""
        types = {
            'info': 'معلومات',
            'warning': 'تحذير',
            'error': 'خطأ',
            'success': 'نجح'
        }
        return types.get(self.type, 'عام')
    
    def get_priority_text(self):
        """الحصول على نص الأولوية بالعربية"""
        priorities = {
            'low': 'منخفضة',
            'medium': 'متوسطة',
            'high': 'عالية',
            'urgent': 'عاجلة'
        }
        return priorities.get(self.priority, 'متوسطة')
    
    def to_dict(self):
        """تحويل البيانات إلى قاموس"""
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'type': self.type,
            'type_text': self.get_type_text(),
            'type_icon': self.get_type_icon(),
            'type_color': self.get_type_color(),
            'category': self.category,
            'category_text': self.get_category_text(),
            'is_read': self.is_read,
            'is_sent': self.is_sent,
            'action_url': self.action_url,
            'action_text': self.action_text,
            'priority': self.priority,
            'priority_text': self.get_priority_text(),
            'priority_color': self.get_priority_color(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_expired': self.is_expired()
        }
    
    @staticmethod
    def create_subscription_expiry_notification(user, subscription):
        """إنشاء إشعار انتهاء اشتراك"""
        days_left = subscription.days_until_expiry()
        
        if days_left <= 0:
            title = f"انتهى اشتراك {subscription.name}"
            message = f"انتهت صلاحية اشتراك {subscription.name} في {subscription.provider}"
            type_name = 'error'
        elif days_left <= 3:
            title = f"اشتراك {subscription.name} ينتهي خلال {days_left} أيام"
            message = f"سينتهي اشتراك {subscription.name} في {subscription.provider} خلال {days_left} أيام"
            type_name = 'warning'
        else:
            title = f"تذكير: اشتراك {subscription.name} ينتهي قريباً"
            message = f"سينتهي اشتراك {subscription.name} في {subscription.provider} خلال {days_left} أيام"
            type_name = 'info'
        
        return Notification(
            user_id=user.id,
            title=title,
            message=message,
            type=type_name,
            category='subscription',
            priority='high' if days_left <= 3 else 'medium',
            action_url=f'/subscriptions/{subscription.id}',
            action_text='عرض الاشتراك'
        )
    
    @staticmethod
    def create_invoice_due_notification(user, invoice):
        """إنشاء إشعار استحقاق فاتورة"""
        days_left = invoice.days_until_due()
        
        if days_left <= 0:
            title = f"فاتورة متأخرة: {invoice.invoice_number}"
            message = f"الفاتورة {invoice.invoice_number} متأخرة بـ {abs(days_left)} أيام"
            type_name = 'error'
            priority = 'urgent'
        elif days_left <= 3:
            title = f"فاتورة تستحق خلال {days_left} أيام"
            message = f"الفاتورة {invoice.invoice_number} بمبلغ {invoice.total_amount} {invoice.currency} تستحق خلال {days_left} أيام"
            type_name = 'warning'
            priority = 'high'
        else:
            title = f"تذكير: فاتورة تستحق قريباً"
            message = f"الفاتورة {invoice.invoice_number} بمبلغ {invoice.total_amount} {invoice.currency} تستحق خلال {days_left} أيام"
            type_name = 'info'
            priority = 'medium'
        
        return Notification(
            user_id=user.id,
            title=title,
            message=message,
            type=type_name,
            category='invoice',
            priority=priority,
            action_url=f'/invoices/{invoice.id}',
            action_text='عرض الفاتورة'
        )
    
    def __repr__(self):
        return f'<Notification {self.title} - {self.type}>'
