<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الاشتراكات{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    
    <!-- Matrix Background Effect -->
    <div id="matrix-bg" class="matrix-background"></div>
    
    <!-- Navigation -->
    {% if current_user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark glass-nav fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand neon-text" href="{{ url_for('dashboard') }}">
                <i class="fas fa-rocket me-2"></i>
                نظام إدارة الاشتراكات
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-subscription me-1"></i>
                            الاشتراكات
                        </a>
                        <ul class="dropdown-menu glass-dropdown">
                            <li><a class="dropdown-item" href="/subscriptions">عرض الاشتراكات</a></li>
                            <li><a class="dropdown-item" href="/subscriptions/add">إضافة اشتراك</a></li>
                            <li><a class="dropdown-item" href="/subscription_analytics">التحليلات</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-file-invoice me-1"></i>
                            الفواتير
                        </a>
                        <ul class="dropdown-menu glass-dropdown">
                            <li><a class="dropdown-item" href="/invoices">عرض الفواتير</a></li>
                            <li><a class="dropdown-item" href="/invoices/add">إضافة فاتورة</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/send_email">
                            <i class="fas fa-envelope me-1"></i>
                            مركز التواصل
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            {{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu glass-dropdown">
                            <li><a class="dropdown-item" href="/profile">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="/settings">الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Main Content -->
    <main class="{% if current_user.is_authenticated %}main-content{% endif %}">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="container-fluid mt-3">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show glass-alert" role="alert">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' if category == 'success' else 'exclamation-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    {% if current_user.is_authenticated %}
    <footer class="glass-footer text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0 neon-text-small">
                <i class="fas fa-heart text-danger me-1"></i>
                مطور بواسطة: المهندس محمد ياسر الجبوري
                <i class="fas fa-code ms-2 me-1"></i>
                الإصدار 1.0.0
            </p>
        </div>
    </footer>
    {% endif %}
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
