#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام إدارة الاشتراكات المتجاوب والمتميز
AdenLink - العراق
النسخة المتجاوبة مع تنسيق مثالي لجميع أحجام الشاشات
"""

print("🌟 بدء تشغيل نظام إدارة الاشتراكات المتجاوب والمتميز...")

try:
    from flask import Flask, render_template_string, request, redirect, url_for, flash, jsonify
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
    from werkzeug.security import generate_password_hash, check_password_hash
    from datetime import datetime, date, timedelta
    import uuid
    import os
    import json
    
    print("✅ تم تحميل المكتبات بنجاح")
    
    # إعداد التطبيق
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'responsive-perfect-subscription-system-2024-adenlink'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///responsive_perfect_subscriptions.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # إعداد قاعدة البيانات
    db = SQLAlchemy(app)
    
    # إعداد نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # نماذج قاعدة البيانات المحسنة
    class User(UserMixin, db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        password_hash = db.Column(db.String(200), nullable=False)
        role = db.Column(db.String(20), default='user')
        company = db.Column(db.String(200))
        phone = db.Column(db.String(20))
        avatar_url = db.Column(db.String(500))
        is_active = db.Column(db.Boolean, default=True)
        last_login = db.Column(db.DateTime)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        def set_password(self, password):
            self.password_hash = generate_password_hash(password)
        
        def check_password(self, password):
            return check_password_hash(self.password_hash, password)

    class ServiceProvider(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(100), nullable=False)
        code = db.Column(db.String(20), unique=True, nullable=False)
        description = db.Column(db.Text)
        website = db.Column(db.String(200))
        logo_url = db.Column(db.String(500))
        api_endpoint = db.Column(db.String(200))
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.now)

    class PaymentMethod(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(100), nullable=False)
        code = db.Column(db.String(20), unique=True, nullable=False)
        description = db.Column(db.Text)
        processing_fee = db.Column(db.Float, default=0.0)
        fee_type = db.Column(db.String(20), default='percentage')
        is_active = db.Column(db.Boolean, default=True)
        icon_class = db.Column(db.String(50))
        created_at = db.Column(db.DateTime, default=datetime.now)

    class Currency(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(50), nullable=False)
        code = db.Column(db.String(3), unique=True, nullable=False)
        symbol = db.Column(db.String(10), nullable=False)
        exchange_rate = db.Column(db.Float, default=1.0)
        is_default = db.Column(db.Boolean, default=False)
        created_at = db.Column(db.DateTime, default=datetime.now)

    class Customer(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        phone = db.Column(db.String(20))
        company = db.Column(db.String(200))
        address = db.Column(db.Text)
        country = db.Column(db.String(100))
        city = db.Column(db.String(100))
        postal_code = db.Column(db.String(20))
        tax_id = db.Column(db.String(50))
        preferred_currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'))
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        preferred_currency = db.relationship('Currency', backref='customers')

    class Service(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        description = db.Column(db.Text)
        category = db.Column(db.String(100))
        provider_id = db.Column(db.Integer, db.ForeignKey('service_provider.id'), nullable=False)
        base_price = db.Column(db.Float, nullable=False)
        currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'), nullable=False)
        billing_cycle = db.Column(db.String(20), default='monthly')
        setup_fee = db.Column(db.Float, default=0.0)
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        provider = db.relationship('ServiceProvider', backref='services')
        currency = db.relationship('Currency', backref='services')

    class Subscription(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        subscription_id = db.Column(db.String(50), unique=True, nullable=False)
        customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
        service_id = db.Column(db.Integer, db.ForeignKey('service.id'), nullable=False)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        
        status = db.Column(db.String(20), default='active')
        priority = db.Column(db.String(20), default='normal')
        billing_cycle = db.Column(db.String(20), default='monthly')
        
        start_date = db.Column(db.Date, nullable=False)
        end_date = db.Column(db.Date, nullable=False)
        next_billing_date = db.Column(db.Date)
        last_payment_date = db.Column(db.Date)
        
        price = db.Column(db.Float, nullable=False)
        setup_fee = db.Column(db.Float, default=0.0)
        discount_percentage = db.Column(db.Float, default=0.0)
        currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'), nullable=False)
        
        server_name = db.Column(db.String(200))
        server_ip = db.Column(db.String(45))
        server_port = db.Column(db.Integer)
        api_key = db.Column(db.String(500))
        api_secret = db.Column(db.String(500))
        control_panel_url = db.Column(db.String(500))
        
        notes = db.Column(db.Text)
        tags = db.Column(db.String(500))
        
        created_at = db.Column(db.DateTime, default=datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
        
        customer = db.relationship('Customer', backref='subscriptions')
        service = db.relationship('Service', backref='subscriptions')
        user = db.relationship('User', backref='subscriptions')
        currency = db.relationship('Currency', backref='subscriptions')

    class Invoice(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        invoice_number = db.Column(db.String(50), unique=True, nullable=False)
        customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
        subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'))
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        
        subtotal = db.Column(db.Float, nullable=False)
        tax_rate = db.Column(db.Float, default=0.0)
        tax_amount = db.Column(db.Float, default=0.0)
        discount_amount = db.Column(db.Float, default=0.0)
        total_amount = db.Column(db.Float, nullable=False)
        currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'), nullable=False)
        
        issue_date = db.Column(db.Date, default=date.today)
        due_date = db.Column(db.Date, nullable=False)
        paid_date = db.Column(db.Date)
        
        status = db.Column(db.String(20), default='pending')
        payment_method_id = db.Column(db.Integer, db.ForeignKey('payment_method.id'))
        payment_reference = db.Column(db.String(200))
        
        description = db.Column(db.Text)
        notes = db.Column(db.Text)
        
        created_at = db.Column(db.DateTime, default=datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
        
        customer = db.relationship('Customer', backref='invoices')
        subscription = db.relationship('Subscription', backref='invoices')
        user = db.relationship('User', backref='invoices')
        currency = db.relationship('Currency', backref='invoices')
        payment_method = db.relationship('PaymentMethod', backref='invoices')

    class Notification(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        title = db.Column(db.String(200), nullable=False)
        message = db.Column(db.Text, nullable=False)
        type = db.Column(db.String(20), default='info')
        priority = db.Column(db.String(20), default='normal')
        
        is_read = db.Column(db.Boolean, default=False)
        read_at = db.Column(db.DateTime)
        action_url = db.Column(db.String(500))
        action_text = db.Column(db.String(100))
        
        related_subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'))
        related_invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'))
        
        created_at = db.Column(db.DateTime, default=datetime.now)
        expires_at = db.Column(db.DateTime)
        
        user = db.relationship('User', backref='notifications')
        related_subscription = db.relationship('Subscription', backref='notifications')
        related_invoice = db.relationship('Invoice', backref='notifications')

    class ActivityLog(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        action = db.Column(db.String(100), nullable=False)
        description = db.Column(db.Text)
        ip_address = db.Column(db.String(45))
        user_agent = db.Column(db.String(500))
        
        related_table = db.Column(db.String(50))
        related_id = db.Column(db.Integer)
        
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        user = db.relationship('User', backref='activity_logs')

    print("✅ تم إعداد النماذج المحسنة بنجاح")

    # دوال مساعدة محسنة
    def generate_subscription_id():
        return f"SUB-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"

    def generate_invoice_number():
        return f"INV-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"

    def log_activity(action, description, related_table=None, related_id=None):
        try:
            if current_user.is_authenticated:
                activity = ActivityLog(
                    user_id=current_user.id,
                    action=action,
                    description=description,
                    ip_address=request.remote_addr if request else None,
                    user_agent=request.user_agent.string if request else None,
                    related_table=related_table,
                    related_id=related_id
                )
                db.session.add(activity)
                db.session.commit()
        except Exception as e:
            print(f"خطأ في تسجيل النشاط: {e}")

    def get_dashboard_stats():
        try:
            stats = {
                'total_customers': Customer.query.filter_by(is_active=True).count(),
                'total_subscriptions': Subscription.query.count(),
                'active_subscriptions': Subscription.query.filter_by(status='active').count(),
                'suspended_subscriptions': Subscription.query.filter_by(status='suspended').count(),
                'expired_subscriptions': Subscription.query.filter_by(status='expired').count(),
                'total_invoices': Invoice.query.count(),
                'pending_invoices': Invoice.query.filter_by(status='pending').count(),
                'paid_invoices': Invoice.query.filter_by(status='paid').count(),
                'overdue_invoices': Invoice.query.filter_by(status='overdue').count(),
                'monthly_revenue': db.session.query(db.func.sum(Invoice.total_amount)).filter(
                    Invoice.status == 'paid',
                    Invoice.paid_date >= date.today().replace(day=1)
                ).scalar() or 0,
                'yearly_revenue': db.session.query(db.func.sum(Invoice.total_amount)).filter(
                    Invoice.status == 'paid',
                    Invoice.paid_date >= date.today().replace(month=1, day=1)
                ).scalar() or 0,
                'expiring_soon': Subscription.query.filter(
                    Subscription.status == 'active',
                    Subscription.end_date <= date.today() + timedelta(days=30),
                    Subscription.end_date > date.today()
                ).count(),
                'expiring_this_week': Subscription.query.filter(
                    Subscription.status == 'active',
                    Subscription.end_date <= date.today() + timedelta(days=7),
                    Subscription.end_date > date.today()
                ).count(),
                'total_providers': ServiceProvider.query.filter_by(is_active=True).count(),
                'total_services': Service.query.filter_by(is_active=True).count(),
                'unread_notifications': Notification.query.filter_by(
                    user_id=current_user.id if current_user.is_authenticated else 1,
                    is_read=False
                ).count() if current_user.is_authenticated else 0,
                'monthly_subscriptions': Subscription.query.filter_by(
                    billing_cycle='monthly', status='active'
                ).count(),
                'semi_annual_subscriptions': Subscription.query.filter_by(
                    billing_cycle='semi_annual', status='active'
                ).count(),
                'annual_subscriptions': Subscription.query.filter_by(
                    billing_cycle='annual', status='active'
                ).count(),
                'high_priority_subscriptions': Subscription.query.filter_by(
                    priority='high', status='active'
                ).count(),
                'critical_priority_subscriptions': Subscription.query.filter_by(
                    priority='critical', status='active'
                ).count(),
            }

            avg_subscription_value = db.session.query(db.func.avg(Subscription.price)).filter(
                Subscription.status == 'active'
            ).scalar()
            stats['avg_subscription_value'] = round(avg_subscription_value or 0, 2)

            last_month_subscriptions = Subscription.query.filter(
                Subscription.created_at >= date.today().replace(day=1) - timedelta(days=30),
                Subscription.created_at < date.today().replace(day=1)
            ).count()

            current_month_subscriptions = Subscription.query.filter(
                Subscription.created_at >= date.today().replace(day=1)
            ).count()

            if last_month_subscriptions > 0:
                growth_rate = ((current_month_subscriptions - last_month_subscriptions) / last_month_subscriptions) * 100
                stats['monthly_growth_rate'] = round(growth_rate, 1)
            else:
                stats['monthly_growth_rate'] = 0

            return stats

        except Exception as e:
            print(f"خطأ في حساب الإحصائيات: {e}")
            return {
                'total_customers': 0,
                'total_subscriptions': 0,
                'active_subscriptions': 0,
                'total_invoices': 0,
                'pending_invoices': 0,
                'monthly_revenue': 0,
                'expiring_soon': 0,
                'monthly_growth_rate': 0,
                'avg_subscription_value': 0
            }

    print("✅ تم إعداد الدوال المساعدة المحسنة بنجاح")

    # قالب تسجيل الدخول المتجاوب والمتميز
    LOGIN_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.5, maximum-scale=3.0">
        <title>🚀 تسجيل الدخول - نظام إدارة الاشتراكات المتجاوب</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
        <style>
            :root {
                --neon-blue: #00d4ff;
                --neon-purple: #b537f2;
                --neon-pink: #ff006e;
                --neon-green: #39ff14;
                --glass-bg: rgba(255, 255, 255, 0.1);
                --glass-border: rgba(255, 255, 255, 0.2);

                /* متغيرات التجاوب */
                --container-max-width: 450px;
                --container-padding: 3rem;
                --logo-size: 80px;
                --title-size: 1.8rem;
                --subtitle-size: 1rem;
                --input-padding: 1rem 1.5rem;
                --button-padding: 1rem 2rem;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            html {
                font-size: 16px;
                scroll-behavior: smooth;
            }

            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
                min-height: 100vh;
                min-height: 100dvh; /* دعم الشاشات الحديثة */
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 1rem;
                position: relative;
                overflow-x: hidden;
            }

            /* خلفية متحركة متجاوبة */
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(181, 55, 242, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(255, 0, 110, 0.3) 0%, transparent 50%);
                z-index: -2;
                animation: backgroundPulse 8s ease-in-out infinite;
            }

            @keyframes backgroundPulse {
                0%, 100% {
                    transform: scale(1) rotate(0deg);
                    opacity: 0.8;
                }
                50% {
                    transform: scale(1.1) rotate(2deg);
                    opacity: 1;
                }
            }

            /* حاوي تسجيل الدخول المتجاوب */
            .login-container {
                background: var(--glass-bg);
                backdrop-filter: blur(25px);
                border: 2px solid var(--glass-border);
                border-radius: 30px;
                padding: var(--container-padding);
                width: 100%;
                max-width: var(--container-max-width);
                box-shadow:
                    0 25px 50px rgba(0, 0, 0, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
                position: relative;
                overflow: hidden;
                animation: slideInUp 1s ease-out;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(50px) scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            .login-container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
                background-size: 200% 100%;
                animation: gradientShift 3s ease-in-out infinite;
            }

            @keyframes gradientShift {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            /* قسم الشعار المتجاوب */
            .logo-section {
                text-align: center;
                margin-bottom: 2.5rem;
            }

            .logo-icon {
                width: var(--logo-size);
                height: var(--logo-size);
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1.5rem;
                color: white;
                font-size: calc(var(--logo-size) * 0.4);
                box-shadow:
                    0 20px 40px rgba(0, 212, 255, 0.3),
                    0 0 30px rgba(181, 55, 242, 0.4);
                animation: logoFloat 3s ease-in-out infinite;
                position: relative;
            }

            .logo-icon::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: -3px;
                bottom: -3px;
                background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
                border-radius: 50%;
                z-index: -1;
                animation: rotate 4s linear infinite;
                opacity: 0.7;
            }

            @keyframes logoFloat {
                0%, 100% { transform: translateY(0px) scale(1); }
                50% { transform: translateY(-8px) scale(1.05); }
            }

            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .system-title {
                color: white;
                font-weight: 800;
                font-size: var(--title-size);
                margin-bottom: 0.5rem;
                text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
                animation: titleGlow 2s ease-in-out infinite alternate;
                line-height: 1.2;
            }

            @keyframes titleGlow {
                from { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
                to { text-shadow: 0 0 30px rgba(0, 212, 255, 0.8); }
            }

            .system-subtitle {
                color: rgba(255, 255, 255, 0.8);
                font-size: var(--subtitle-size);
                font-weight: 500;
                margin-bottom: 0;
                line-height: 1.4;
            }

            /* نماذج الإدخال المتجاوبة */
            .form-group {
                margin-bottom: 1.5rem;
                position: relative;
            }

            .form-label {
                color: white;
                font-weight: 600;
                font-size: 1rem;
                margin-bottom: 0.5rem;
                display: flex;
                align-items: center;
                text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
            }

            .form-label i {
                margin-left: 0.5rem;
                color: var(--neon-blue);
                text-shadow: 0 0 10px var(--neon-blue);
                font-size: 1.1rem;
            }

            .form-control {
                background: var(--glass-bg);
                backdrop-filter: blur(10px);
                border: 2px solid var(--glass-border);
                border-radius: 20px;
                padding: var(--input-padding);
                font-size: 1rem;
                color: white;
                transition: all 0.3s ease;
                width: 100%;
                font-family: 'Cairo', sans-serif;
            }

            .form-control::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }

            .form-control:focus {
                outline: none;
                border-color: var(--neon-blue);
                box-shadow:
                    0 0 0 0.2rem rgba(0, 212, 255, 0.25),
                    0 0 20px rgba(0, 212, 255, 0.4);
                background: rgba(255, 255, 255, 0.15);
                transform: translateY(-2px);
            }

            /* زر تسجيل الدخول المتجاوب */
            .btn-login {
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                border: none;
                border-radius: 20px;
                padding: var(--button-padding);
                font-weight: 700;
                font-size: 1.1rem;
                width: 100%;
                color: white;
                transition: all 0.3s ease;
                margin-top: 1rem;
                position: relative;
                overflow: hidden;
                text-transform: uppercase;
                letter-spacing: 1px;
                box-shadow: 0 15px 30px rgba(0, 212, 255, 0.3);
                font-family: 'Cairo', sans-serif;
                cursor: pointer;
            }

            .btn-login::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                transition: left 0.5s ease;
            }

            .btn-login:hover::before {
                left: 100%;
            }

            .btn-login:hover {
                transform: translateY(-3px);
                box-shadow:
                    0 20px 40px rgba(0, 212, 255, 0.4),
                    0 0 30px rgba(181, 55, 242, 0.3);
                background: linear-gradient(135deg, var(--neon-purple), var(--neon-pink));
            }

            .btn-login:active {
                transform: translateY(-1px);
            }

            /* تنبيهات متجاوبة */
            .alert {
                background: var(--glass-bg);
                backdrop-filter: blur(15px);
                border: 2px solid var(--glass-border);
                border-radius: 15px;
                margin-bottom: 1.5rem;
                padding: 1rem 1.5rem;
                color: white;
                animation: alertSlide 0.5s ease-out;
                font-size: 0.9rem;
                line-height: 1.4;
            }

            @keyframes alertSlide {
                from {
                    opacity: 0;
                    transform: translateX(50px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            .alert-success {
                border-left: 4px solid var(--neon-green);
                box-shadow: 0 0 20px rgba(57, 255, 20, 0.3);
            }

            .alert-danger {
                border-left: 4px solid var(--neon-pink);
                box-shadow: 0 0 20px rgba(255, 0, 110, 0.3);
            }

            /* معلومات التجربة المتجاوبة */
            .login-info {
                background: var(--glass-bg);
                backdrop-filter: blur(15px);
                border: 2px solid var(--glass-border);
                border-radius: 20px;
                padding: 1.5rem;
                margin-top: 2rem;
                text-align: center;
                color: white;
                position: relative;
                overflow: hidden;
            }

            .login-info::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple));
                animation: infoGlow 2s ease-in-out infinite;
            }

            @keyframes infoGlow {
                0%, 100% { opacity: 0.5; }
                50% { opacity: 1; }
            }

            .login-info code {
                background: rgba(0, 212, 255, 0.2);
                color: var(--neon-blue);
                padding: 0.3rem 0.6rem;
                border-radius: 8px;
                font-weight: 600;
                text-shadow: 0 0 10px var(--neon-blue);
                font-size: 0.9rem;
            }

            /* ✅ استجابة للشاشات المختلفة */

            /* شاشات كبيرة جداً (4K وما فوق) */
            @media (min-width: 2560px) {
                :root {
                    --container-max-width: 600px;
                    --container-padding: 4rem;
                    --logo-size: 120px;
                    --title-size: 2.5rem;
                    --subtitle-size: 1.3rem;
                    --input-padding: 1.5rem 2rem;
                    --button-padding: 1.5rem 2.5rem;
                }

                html {
                    font-size: 20px;
                }
            }

            /* شاشات كبيرة (Desktop) */
            @media (min-width: 1920px) and (max-width: 2559px) {
                :root {
                    --container-max-width: 550px;
                    --container-padding: 3.5rem;
                    --logo-size: 100px;
                    --title-size: 2.2rem;
                    --subtitle-size: 1.2rem;
                }

                html {
                    font-size: 18px;
                }
            }

            /* شاشات متوسطة (Laptop) */
            @media (min-width: 1200px) and (max-width: 1919px) {
                :root {
                    --container-max-width: 500px;
                    --container-padding: 3rem;
                    --logo-size: 90px;
                    --title-size: 2rem;
                    --subtitle-size: 1.1rem;
                }
            }

            /* شاشات صغيرة (Tablet) */
            @media (min-width: 768px) and (max-width: 1199px) {
                :root {
                    --container-max-width: 450px;
                    --container-padding: 2.5rem;
                    --logo-size: 80px;
                    --title-size: 1.8rem;
                    --subtitle-size: 1rem;
                    --input-padding: 1rem 1.5rem;
                    --button-padding: 1rem 2rem;
                }

                body {
                    padding: 1.5rem;
                }
            }

            /* شاشات صغيرة جداً (Mobile) */
            @media (max-width: 767px) {
                :root {
                    --container-max-width: 100%;
                    --container-padding: 2rem;
                    --logo-size: 70px;
                    --title-size: 1.6rem;
                    --subtitle-size: 0.9rem;
                    --input-padding: 0.9rem 1.2rem;
                    --button-padding: 0.9rem 1.5rem;
                }

                html {
                    font-size: 14px;
                }

                body {
                    padding: 1rem;
                }

                .login-container {
                    border-radius: 25px;
                }

                .form-control {
                    font-size: 16px; /* منع التكبير في iOS */
                }

                .btn-login {
                    font-size: 1rem;
                }

                .login-info {
                    padding: 1.2rem;
                    font-size: 0.85rem;
                }
            }

            /* شاشات صغيرة جداً (Small Mobile) */
            @media (max-width: 480px) {
                :root {
                    --container-padding: 1.5rem;
                    --logo-size: 60px;
                    --title-size: 1.4rem;
                    --subtitle-size: 0.8rem;
                    --input-padding: 0.8rem 1rem;
                    --button-padding: 0.8rem 1.2rem;
                }

                html {
                    font-size: 13px;
                }

                body {
                    padding: 0.5rem;
                }

                .login-container {
                    border-radius: 20px;
                }

                .logo-section {
                    margin-bottom: 2rem;
                }

                .form-group {
                    margin-bottom: 1.2rem;
                }

                .login-info {
                    padding: 1rem;
                    margin-top: 1.5rem;
                    font-size: 0.8rem;
                }
            }

            /* شاشات عمودية طويلة */
            @media (max-height: 600px) and (orientation: landscape) {
                body {
                    padding: 0.5rem;
                }

                .login-container {
                    padding: 1.5rem;
                }

                .logo-section {
                    margin-bottom: 1.5rem;
                }

                .login-info {
                    margin-top: 1rem;
                    padding: 1rem;
                }
            }

            /* دعم الشاشات عالية الكثافة */
            @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
                .logo-icon {
                    box-shadow:
                        0 25px 50px rgba(0, 212, 255, 0.4),
                        0 0 40px rgba(181, 55, 242, 0.5);
                }

                .btn-login {
                    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.4);
                }
            }

            /* دعم الوضع المظلم */
            @media (prefers-color-scheme: dark) {
                :root {
                    --glass-bg: rgba(255, 255, 255, 0.08);
                    --glass-border: rgba(255, 255, 255, 0.15);
                }
            }

            /* تحسين الأداء للشاشات البطيئة */
            @media (prefers-reduced-motion: reduce) {
                * {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h1 class="system-title">نظام إدارة الاشتراكات المتجاوب</h1>
                <p class="system-subtitle">AdenLink - العراق</p>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }}">
                            <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user"></i>اسم المستخدم
                    </label>
                    <input type="text" class="form-control" name="username" required
                           placeholder="أدخل اسم المستخدم" autocomplete="username">
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-lock"></i>كلمة المرور
                    </label>
                    <input type="password" class="form-control" name="password" required
                           placeholder="أدخل كلمة المرور" autocomplete="current-password">
                </div>

                <button type="submit" class="btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </button>
            </form>

            <div class="login-info">
                <div style="font-weight: 600; margin-bottom: 1rem;">
                    <i class="fas fa-info-circle me-2" style="color: var(--neon-blue);"></i>
                    <strong>بيانات التجربة المتجاوبة</strong>
                </div>
                <div style="margin-bottom: 0.5rem;">
                    👤 المستخدم: <code>admin</code>
                </div>
                <div style="margin-bottom: 1rem;">
                    🔑 كلمة المرور: <code>123456</code>
                </div>
                <div style="font-size: 0.9rem; opacity: 0.8;">
                    🎯 النظام المتجاوب مع جميع أحجام الشاشات
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // تأثير الكتابة للعنوان
                const title = document.querySelector('.system-title');
                const originalText = title.textContent;
                title.textContent = '';

                let i = 0;
                const typeWriter = () => {
                    if (i < originalText.length) {
                        title.textContent += originalText.charAt(i);
                        i++;
                        setTimeout(typeWriter, 100);
                    }
                };

                setTimeout(typeWriter, 1000);

                // تأثيرات تفاعلية للحقول
                const inputs = document.querySelectorAll('.form-control');
                inputs.forEach(input => {
                    input.addEventListener('focus', function() {
                        this.parentElement.style.transform = 'scale(1.02)';
                        this.parentElement.style.transition = 'transform 0.3s ease';
                    });

                    input.addEventListener('blur', function() {
                        this.parentElement.style.transform = 'scale(1)';
                    });
                });

                // تحسين الأداء للأجهزة المحمولة
                if ('ontouchstart' in window) {
                    document.body.classList.add('touch-device');
                }

                // تحديد نوع الجهاز
                function detectDevice() {
                    const width = window.innerWidth;
                    const height = window.innerHeight;

                    if (width >= 2560) {
                        document.body.classList.add('device-4k');
                    } else if (width >= 1920) {
                        document.body.classList.add('device-desktop-large');
                    } else if (width >= 1200) {
                        document.body.classList.add('device-desktop');
                    } else if (width >= 768) {
                        document.body.classList.add('device-tablet');
                    } else {
                        document.body.classList.add('device-mobile');
                    }
                }

                detectDevice();

                // إعادة تحديد نوع الجهاز عند تغيير حجم الشاشة
                window.addEventListener('resize', function() {
                    // إزالة جميع فئات الأجهزة
                    document.body.classList.remove('device-4k', 'device-desktop-large', 'device-desktop', 'device-tablet', 'device-mobile');
                    detectDevice();
                });

                // تحسين التمرير للأجهزة المحمولة
                if (window.innerWidth <= 768) {
                    document.addEventListener('touchmove', function(e) {
                        if (e.target.closest('.login-container')) {
                            e.preventDefault();
                        }
                    }, { passive: false });
                }
            });
        </script>
    </body>
    </html>
    '''

    # قالب لوحة التحكم المتجاوب والمتميز
    DASHBOARD_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.5, maximum-scale=3.0">
        <title>لوحة التحكم المتجاوبة - نظام إدارة الاشتراكات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
        <style>
            :root {
                --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
                --neon-blue: #00d4ff;
                --neon-purple: #b537f2;
                --neon-pink: #ff006e;
                --neon-green: #39ff14;
                --glass-bg: rgba(255, 255, 255, 0.1);
                --glass-border: rgba(255, 255, 255, 0.2);

                /* متغيرات التجاوب */
                --sidebar-width: 280px;
                --sidebar-width-mobile: 100vw;
                --main-padding: 1.5rem;
                --card-padding: 2rem;
                --stat-card-min-width: 280px;
                --border-radius: 25px;
                --border-radius-small: 15px;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            html {
                font-size: 16px;
                scroll-behavior: smooth;
            }

            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
                min-height: 100vh;
                min-height: 100dvh;
                color: white;
                overflow-x: hidden;
                position: relative;
            }

            /* خلفية متحركة متجاوبة */
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.4) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(181, 55, 242, 0.4) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(255, 0, 110, 0.4) 0%, transparent 50%),
                    radial-gradient(circle at 60% 80%, rgba(57, 255, 20, 0.3) 0%, transparent 50%);
                z-index: -2;
                animation: backgroundFlow 15s ease-in-out infinite;
            }

            @keyframes backgroundFlow {
                0%, 100% {
                    transform: scale(1) rotate(0deg);
                    opacity: 0.8;
                }
                33% {
                    transform: scale(1.1) rotate(1deg);
                    opacity: 1;
                }
                66% {
                    transform: scale(0.9) rotate(-1deg);
                    opacity: 0.9;
                }
            }

            /* الشريط الجانبي المتجاوب */
            .sidebar {
                position: fixed;
                right: 0;
                top: 0;
                width: var(--sidebar-width);
                height: 100vh;
                height: 100dvh;
                background: var(--glass-bg);
                backdrop-filter: blur(25px);
                border-left: 2px solid var(--glass-border);
                box-shadow: -20px 0 40px rgba(0, 0, 0, 0.3);
                z-index: 1000;
                overflow-y: auto;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                transform: translateX(0);
            }

            /* أشرطة التمرير المخصصة */
            .sidebar::-webkit-scrollbar {
                width: 6px;
            }

            .sidebar::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }

            .sidebar::-webkit-scrollbar-thumb {
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                border-radius: 3px;
                transition: all 0.3s ease;
            }

            .sidebar::-webkit-scrollbar-thumb:hover {
                background: linear-gradient(135deg, var(--neon-purple), var(--neon-pink));
                box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            }

            .sidebar-header {
                padding: 1.5rem;
                text-align: center;
                border-bottom: 2px solid var(--glass-border);
                background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(181, 55, 242, 0.1));
                position: relative;
                overflow: hidden;
            }

            .sidebar-header::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
                background-size: 200% 100%;
                animation: gradientShift 3s ease-in-out infinite;
            }

            @keyframes gradientShift {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            .sidebar-logo {
                width: 60px;
                height: 60px;
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1rem;
                color: white;
                font-size: 1.5rem;
                box-shadow:
                    0 15px 30px rgba(0, 212, 255, 0.4),
                    0 0 20px rgba(181, 55, 242, 0.3);
                animation: logoFloat 3s ease-in-out infinite;
                position: relative;
            }

            .sidebar-logo::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: -3px;
                bottom: -3px;
                background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
                border-radius: 50%;
                z-index: -1;
                animation: rotate 4s linear infinite;
                opacity: 0.7;
            }

            @keyframes logoFloat {
                0%, 100% { transform: translateY(0px) scale(1); }
                50% { transform: translateY(-3px) scale(1.05); }
            }

            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .sidebar-title {
                font-size: 1.1rem;
                font-weight: 700;
                color: white;
                margin-bottom: 0.5rem;
                text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
                line-height: 1.2;
            }

            .sidebar-subtitle {
                font-size: 0.85rem;
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 0;
                line-height: 1.3;
            }

            /* معلومات المستخدم المتجاوبة */
            .user-info-section {
                padding: 1rem;
                background: var(--glass-bg);
                backdrop-filter: blur(15px);
                border: 1px solid var(--glass-border);
                border-radius: var(--border-radius-small);
                margin: 1rem;
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .user-info-section::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple));
                animation: userGlow 3s ease-in-out infinite;
            }

            @keyframes userGlow {
                0%, 100% { opacity: 0.5; }
                50% { opacity: 1; }
            }

            .user-info-section:hover {
                background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(181, 55, 242, 0.2));
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
            }

            .user-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 700;
                margin-left: 0.75rem;
                font-size: 1rem;
                box-shadow: 0 8px 16px rgba(0, 212, 255, 0.4);
                position: relative;
                float: right;
            }

            .user-avatar::before {
                content: '';
                position: absolute;
                top: -2px;
                left: -2px;
                right: -2px;
                bottom: -2px;
                background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
                border-radius: 50%;
                z-index: -1;
                animation: avatarRotate 6s linear infinite;
                opacity: 0.7;
            }

            @keyframes avatarRotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .user-details {
                overflow: hidden;
            }

            .user-name {
                font-weight: 600;
                color: white;
                font-size: 0.9rem;
                margin-bottom: 0.25rem;
                text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
                line-height: 1.2;
            }

            .user-role {
                font-size: 0.75rem;
                color: rgba(255, 255, 255, 0.7);
                margin-bottom: 0;
                line-height: 1.2;
            }

            .user-status {
                width: 8px;
                height: 8px;
                background: var(--neon-green);
                border-radius: 50%;
                float: left;
                margin-top: 0.5rem;
                animation: statusPulse 2s ease-in-out infinite;
                box-shadow: 0 0 8px var(--neon-green);
            }

            @keyframes statusPulse {
                0%, 100% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.2); opacity: 0.8; }
            }

            /* قائمة الشريط الجانبي المتجاوبة */
            .sidebar-menu {
                padding: 1rem 0;
            }

            .menu-section {
                margin-bottom: 1rem;
            }

            .menu-section-title {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
                font-weight: 600;
                color: white;
                background: var(--glass-bg);
                backdrop-filter: blur(15px);
                border: 1px solid var(--glass-border);
                border-radius: 0 20px 20px 0;
                margin-left: 0.75rem;
                display: flex;
                align-items: center;
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
                overflow: hidden;
                text-decoration: none;
                line-height: 1.2;
            }

            .menu-section-title::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 3px;
                height: 100%;
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                transform: scaleY(0);
                transition: all 0.3s ease;
            }

            .menu-section-title:hover::before {
                transform: scaleY(1);
            }

            .menu-section-title:hover {
                background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(181, 55, 242, 0.2));
                transform: translateX(-8px);
                box-shadow: 0 8px 20px rgba(0, 212, 255, 0.3);
                color: white;
                text-decoration: none;
            }

            .menu-section-icon {
                margin-left: 0.75rem;
                font-size: 1rem;
                color: var(--neon-blue);
                text-shadow: 0 0 8px var(--neon-blue);
            }

            .menu-items {
                list-style: none;
                padding: 0;
                margin: 0.5rem 0 0 0;
            }

            .menu-item-link {
                display: flex;
                align-items: center;
                padding: 0.6rem 1rem 0.6rem 2rem;
                color: rgba(255, 255, 255, 0.8);
                text-decoration: none;
                font-size: 0.8rem;
                font-weight: 500;
                transition: all 0.3s ease;
                border-radius: 0 20px 20px 0;
                margin-left: 0.75rem;
                position: relative;
                overflow: hidden;
                line-height: 1.2;
            }

            .menu-item-link::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 2px;
                height: 100%;
                background: var(--neon-green);
                transform: scaleY(0);
                transition: all 0.3s ease;
            }

            .menu-item-link:hover::before {
                transform: scaleY(1);
            }

            .menu-item-link:hover {
                background: linear-gradient(135deg, rgba(57, 255, 20, 0.1), rgba(0, 242, 254, 0.1));
                color: var(--neon-green);
                text-decoration: none;
                transform: translateX(-6px);
                box-shadow: 0 4px 12px rgba(57, 255, 20, 0.3);
            }

            .menu-item-icon {
                margin-left: 0.5rem;
                font-size: 0.8rem;
                width: 14px;
                text-align: center;
            }

            .menu-divider {
                height: 1px;
                background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
                margin: 0.75rem 1rem;
                border-radius: 1px;
            }

            /* زر تبديل الشريط الجانبي */
            .sidebar-toggle {
                position: fixed;
                top: 15px;
                right: 15px;
                z-index: 1001;
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                color: white;
                border: none;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
                cursor: pointer;
                box-shadow: 0 10px 20px rgba(0, 212, 255, 0.4);
                transition: all 0.3s ease;
                display: none;
            }

            .sidebar-toggle:hover {
                transform: scale(1.1);
                box-shadow: 0 15px 30px rgba(0, 212, 255, 0.5);
            }

            /* المحتوى الرئيسي المتجاوب */
            .main-content {
                margin-left: var(--sidebar-width);
                min-height: 100vh;
                min-height: 100dvh;
                padding: var(--main-padding);
                transition: all 0.4s ease;
            }

            /* أشرطة التمرير للمحتوى الرئيسي */
            .main-content::-webkit-scrollbar {
                width: 8px;
                height: 8px;
            }

            .main-content::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }

            .main-content::-webkit-scrollbar-thumb {
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                border-radius: 4px;
                transition: all 0.3s ease;
            }

            .main-content::-webkit-scrollbar-thumb:hover {
                background: linear-gradient(135deg, var(--neon-purple), var(--neon-pink));
                box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            }

            /* حاوي الإحصائيات المتجاوب */
            .stats-container {
                margin-bottom: 2rem;
            }

            .stats-scroll {
                display: flex;
                gap: 1.5rem;
                overflow-x: auto;
                padding: 1rem 0;
                scroll-behavior: smooth;
            }

            /* أشرطة التمرير للإحصائيات */
            .stats-scroll::-webkit-scrollbar {
                height: 6px;
            }

            .stats-scroll::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }

            .stats-scroll::-webkit-scrollbar-thumb {
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple));
                border-radius: 3px;
                transition: all 0.3s ease;
            }

            .stats-scroll::-webkit-scrollbar-thumb:hover {
                background: linear-gradient(90deg, var(--neon-purple), var(--neon-pink));
                box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            }

            /* بطاقات الإحصائيات المتجاوبة */
            .stat-card {
                min-width: var(--stat-card-min-width);
                background: var(--glass-bg);
                backdrop-filter: blur(25px);
                border: 2px solid var(--glass-border);
                border-radius: var(--border-radius);
                padding: var(--card-padding);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                cursor: pointer;
                position: relative;
                overflow: hidden;
                animation: fadeInUp 1s ease-out;
                animation-fill-mode: both;
            }

            .stat-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple));
                transform: scaleX(0);
                transition: all 0.3s ease;
            }

            .stat-card:hover::before {
                transform: scaleX(1);
            }

            .stat-card:hover {
                transform: translateY(-8px) scale(1.02);
                box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
            }

            .stat-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                flex-wrap: wrap;
                gap: 1rem;
            }

            .stat-details {
                flex: 1;
                min-width: 0;
            }

            .stat-number {
                font-size: 2.5rem;
                font-weight: 800;
                color: white;
                margin-bottom: 0.5rem;
                text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
                line-height: 1;
                animation: numberGlow 2s ease-in-out infinite alternate;
                word-break: break-all;
            }

            @keyframes numberGlow {
                from { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
                to { text-shadow: 0 0 30px rgba(0, 212, 255, 0.8); }
            }

            .stat-label {
                color: rgba(255, 255, 255, 0.9);
                font-weight: 600;
                font-size: 1rem;
                margin-bottom: 0.5rem;
                line-height: 1.2;
            }

            .stat-change {
                font-size: 0.85rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 0.25rem;
                flex-wrap: wrap;
            }

            .stat-change.positive {
                color: var(--neon-green);
                text-shadow: 0 0 10px var(--neon-green);
            }

            .stat-icon {
                width: 70px;
                height: 70px;
                border-radius: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 2rem;
                color: white;
                box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
                transition: all 0.3s ease;
                flex-shrink: 0;
            }

            .stat-card:hover .stat-icon {
                transform: rotate(10deg) scale(1.1);
            }

            .stat-card.primary .stat-icon {
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
            }
            .stat-card.success .stat-icon {
                background: linear-gradient(135deg, var(--neon-green), var(--neon-blue));
            }
            .stat-card.warning .stat-icon {
                background: linear-gradient(135deg, #ffd700, #ff8c00);
            }
            .stat-card.danger .stat-icon {
                background: linear-gradient(135deg, var(--neon-pink), var(--neon-purple));
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* ✅ استجابة للشاشات المختلفة */

            /* شاشات كبيرة جداً (4K وما فوق) */
            @media (min-width: 2560px) {
                :root {
                    --sidebar-width: 350px;
                    --main-padding: 2.5rem;
                    --card-padding: 3rem;
                    --stat-card-min-width: 350px;
                    --border-radius: 35px;
                    --border-radius-small: 20px;
                }

                html {
                    font-size: 20px;
                }

                .sidebar-logo {
                    width: 80px;
                    height: 80px;
                    font-size: 2rem;
                }

                .sidebar-title {
                    font-size: 1.4rem;
                }

                .stat-number {
                    font-size: 3.5rem;
                }

                .stat-icon {
                    width: 90px;
                    height: 90px;
                    font-size: 2.5rem;
                }
            }

            /* شاشات كبيرة (Desktop) */
            @media (min-width: 1920px) and (max-width: 2559px) {
                :root {
                    --sidebar-width: 320px;
                    --main-padding: 2rem;
                    --card-padding: 2.5rem;
                    --stat-card-min-width: 320px;
                    --border-radius: 30px;
                    --border-radius-small: 18px;
                }

                html {
                    font-size: 18px;
                }

                .sidebar-logo {
                    width: 70px;
                    height: 70px;
                    font-size: 1.8rem;
                }

                .sidebar-title {
                    font-size: 1.3rem;
                }

                .stat-number {
                    font-size: 3rem;
                }

                .stat-icon {
                    width: 80px;
                    height: 80px;
                    font-size: 2.2rem;
                }
            }

            /* شاشات متوسطة (Laptop) */
            @media (min-width: 1200px) and (max-width: 1919px) {
                :root {
                    --sidebar-width: 300px;
                    --main-padding: 1.8rem;
                    --card-padding: 2.2rem;
                    --stat-card-min-width: 300px;
                }

                .stat-number {
                    font-size: 2.8rem;
                }

                .stat-icon {
                    width: 75px;
                    height: 75px;
                    font-size: 2.1rem;
                }
            }

            /* شاشات صغيرة (Tablet) */
            @media (min-width: 768px) and (max-width: 1199px) {
                :root {
                    --sidebar-width: 280px;
                    --main-padding: 1.5rem;
                    --card-padding: 2rem;
                    --stat-card-min-width: 280px;
                }

                .sidebar {
                    transform: translateX(100%);
                }

                .sidebar.active {
                    transform: translateX(0);
                }

                .main-content {
                    margin-left: 0;
                }

                .sidebar-toggle {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .stat-content {
                    flex-direction: column;
                    text-align: center;
                }

                .stat-icon {
                    order: -1;
                    margin-bottom: 1rem;
                }
            }

            /* شاشات صغيرة جداً (Mobile) */
            @media (max-width: 767px) {
                :root {
                    --sidebar-width-mobile: 100vw;
                    --main-padding: 1rem;
                    --card-padding: 1.5rem;
                    --stat-card-min-width: 250px;
                    --border-radius: 20px;
                    --border-radius-small: 12px;
                }

                html {
                    font-size: 14px;
                }

                .sidebar {
                    width: var(--sidebar-width-mobile);
                    transform: translateX(100%);
                }

                .sidebar.active {
                    transform: translateX(0);
                }

                .main-content {
                    margin-left: 0;
                    padding: var(--main-padding);
                }

                .sidebar-toggle {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 45px;
                    height: 45px;
                    font-size: 1.1rem;
                }

                .sidebar-header {
                    padding: 1.2rem;
                }

                .sidebar-logo {
                    width: 50px;
                    height: 50px;
                    font-size: 1.2rem;
                }

                .sidebar-title {
                    font-size: 1rem;
                }

                .sidebar-subtitle {
                    font-size: 0.8rem;
                }

                .user-info-section {
                    margin: 0.8rem;
                    padding: 0.8rem;
                }

                .user-avatar {
                    width: 35px;
                    height: 35px;
                    font-size: 0.9rem;
                }

                .user-name {
                    font-size: 0.85rem;
                }

                .user-role {
                    font-size: 0.7rem;
                }

                .stats-scroll {
                    gap: 1rem;
                    padding: 0.8rem 0;
                }

                .stat-card {
                    min-width: 250px;
                    padding: 1.5rem;
                }

                .stat-content {
                    flex-direction: column;
                    text-align: center;
                    gap: 1rem;
                }

                .stat-number {
                    font-size: 2rem;
                }

                .stat-label {
                    font-size: 0.9rem;
                }

                .stat-change {
                    font-size: 0.8rem;
                    justify-content: center;
                }

                .stat-icon {
                    width: 60px;
                    height: 60px;
                    font-size: 1.8rem;
                    order: -1;
                }

                .menu-section-title {
                    font-size: 0.85rem;
                    padding: 0.6rem 0.8rem;
                }

                .menu-item-link {
                    font-size: 0.75rem;
                    padding: 0.5rem 0.8rem 0.5rem 1.5rem;
                }
            }

            /* شاشات صغيرة جداً (Small Mobile) */
            @media (max-width: 480px) {
                :root {
                    --main-padding: 0.8rem;
                    --card-padding: 1.2rem;
                    --stat-card-min-width: 220px;
                }

                html {
                    font-size: 13px;
                }

                .sidebar-toggle {
                    width: 40px;
                    height: 40px;
                    font-size: 1rem;
                    top: 10px;
                    right: 10px;
                }

                .sidebar-header {
                    padding: 1rem;
                }

                .sidebar-logo {
                    width: 45px;
                    height: 45px;
                    font-size: 1.1rem;
                    margin-bottom: 0.8rem;
                }

                .sidebar-title {
                    font-size: 0.9rem;
                }

                .sidebar-subtitle {
                    font-size: 0.75rem;
                }

                .user-info-section {
                    margin: 0.6rem;
                    padding: 0.6rem;
                }

                .stats-scroll {
                    gap: 0.8rem;
                }

                .stat-card {
                    min-width: 220px;
                    padding: 1.2rem;
                }

                .stat-number {
                    font-size: 1.8rem;
                }

                .stat-icon {
                    width: 50px;
                    height: 50px;
                    font-size: 1.5rem;
                }
            }

            /* شاشات عمودية طويلة */
            @media (max-height: 600px) and (orientation: landscape) {
                .sidebar-header {
                    padding: 1rem;
                }

                .sidebar-logo {
                    width: 40px;
                    height: 40px;
                    font-size: 1rem;
                    margin-bottom: 0.5rem;
                }

                .sidebar-title {
                    font-size: 0.9rem;
                }

                .user-info-section {
                    margin: 0.5rem;
                    padding: 0.5rem;
                }

                .main-content {
                    padding: 1rem;
                }

                .stat-card {
                    padding: 1.5rem;
                }
            }

            /* دعم الشاشات عالية الكثافة */
            @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
                .sidebar-logo {
                    box-shadow:
                        0 20px 40px rgba(0, 212, 255, 0.5),
                        0 0 30px rgba(181, 55, 242, 0.4);
                }

                .stat-card {
                    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
                }

                .stat-icon {
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
                }
            }

            /* دعم الوضع المظلم */
            @media (prefers-color-scheme: dark) {
                :root {
                    --glass-bg: rgba(255, 255, 255, 0.08);
                    --glass-border: rgba(255, 255, 255, 0.15);
                }
            }

            /* تحسين الأداء للشاشات البطيئة */
            @media (prefers-reduced-motion: reduce) {
                * {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            }

            /* دعم الطباعة */
            @media print {
                .sidebar,
                .sidebar-toggle {
                    display: none;
                }

                .main-content {
                    margin-left: 0;
                    padding: 1rem;
                }

                .stat-card {
                    break-inside: avoid;
                    box-shadow: none;
                    border: 1px solid #ccc;
                }
            }
        </style>
    </head>
    <body>
        <button class="sidebar-toggle" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>

        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-rocket"></i>
                </div>
                <h3 class="sidebar-title">نظام إدارة الاشتراكات المتجاوب</h3>
                <p class="sidebar-subtitle">AdenLink - العراق</p>
            </div>

            {% if current_user.is_authenticated %}
            <div class="user-info-section">
                <div class="user-avatar">
                    {{ current_user.full_name[0] if current_user.full_name else 'A' }}
                </div>
                <div class="user-details">
                    <div class="user-name">{{ current_user.full_name }}</div>
                    <div class="user-role">{{ 'المدير العام' if current_user.role == 'admin' else 'مستخدم عادي' }}</div>
                </div>
                <div class="user-status"></div>
                <div style="clear: both;"></div>
            </div>
            {% endif %}

            <div class="sidebar-menu">
                <div class="menu-section">
                    <a href="{{ url_for('dashboard') }}" class="menu-section-title">
                        <i class="fas fa-tachometer-alt menu-section-icon"></i>
                        لوحة التحكم المتجاوبة
                    </a>
                </div>

                <div class="menu-divider"></div>

                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-server menu-section-icon"></i>
                        إدارة الاشتراكات
                    </div>
                    <ul class="menu-items">
                        <li><a href="{{ url_for('subscriptions_list') }}" class="menu-item-link"><i class="fas fa-list menu-item-icon"></i>قائمة الاشتراكات</a></li>
                        <li><a href="{{ url_for('add_subscription') }}" class="menu-item-link"><i class="fas fa-plus menu-item-icon"></i>إضافة اشتراك جديد</a></li>
                        <li><a href="{{ url_for('subscription_analytics') }}" class="menu-item-link"><i class="fas fa-chart-line menu-item-icon"></i>تحليلات الاشتراكات</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-credit-card menu-item-icon"></i>طرق الدفع (6 طرق)</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-file-alt menu-item-icon"></i>تقارير مفصلة</a></li>
                    </ul>
                </div>

                <div class="menu-divider"></div>

                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-file-invoice menu-section-icon"></i>
                        إدارة الفواتير
                    </div>
                    <ul class="menu-items">
                        <li><a href="#" class="menu-item-link"><i class="fas fa-list menu-item-icon"></i>قائمة الفواتير</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-plus menu-item-icon"></i>إنشاء فاتورة جديدة</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-chart-bar menu-item-icon"></i>تقارير مالية</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-receipt menu-item-icon"></i>كشف حساب العملاء</a></li>
                    </ul>
                </div>

                <div class="menu-divider"></div>

                <div class="menu-section">
                    <a href="{{ url_for('communication_center') }}" class="menu-section-title">
                        <i class="fas fa-envelope menu-section-icon"></i>
                        مركز التواصل
                    </a>
                </div>

                <div class="menu-divider"></div>

                <div class="menu-section">
                    <a href="{{ url_for('logout') }}" class="menu-section-title" style="background: linear-gradient(135deg, rgba(255, 0, 110, 0.2), rgba(255, 87, 87, 0.2));">
                        <i class="fas fa-sign-out-alt menu-section-icon" style="color: var(--neon-pink);"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="stats-container">
                <div class="stats-scroll">
                    <div class="stat-card primary">
                        <div class="stat-content">
                            <div class="stat-details">
                                <div class="stat-number" data-target="{{ stats.total_customers }}">0</div>
                                <div class="stat-label">إجمالي العملاء</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+{{ stats.monthly_growth_rate }}% نمو شهري</span>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-content">
                            <div class="stat-details">
                                <div class="stat-number" data-target="{{ stats.active_subscriptions }}">0</div>
                                <div class="stat-label">اشتراكات نشطة</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-server"></i>
                                    <span>{{ stats.total_providers }} مزودين</span>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-server"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-content">
                            <div class="stat-details">
                                <div class="stat-number" data-target="{{ stats.expiring_soon }}">0</div>
                                <div class="stat-label">تنتهي قريباً</div>
                                <div class="stat-change">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>{{ stats.expiring_this_week }} هذا الأسبوع</span>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card danger">
                        <div class="stat-content">
                            <div class="stat-details">
                                <div class="stat-number" data-target="{{ "%.0f"|format(stats.monthly_revenue) }}">0</div>
                                <div class="stat-label">إيرادات الشهر ($)</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>متوسط: ${{ stats.avg_subscription_value }}</span>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card primary">
                        <div class="stat-content">
                            <div class="stat-details">
                                <div class="stat-number" data-target="{{ stats.total_invoices }}">0</div>
                                <div class="stat-label">إجمالي الفواتير</div>
                                <div class="stat-change positive">
                                    <i class="fas fa-file-invoice"></i>
                                    <span>{{ stats.pending_invoices }} معلقة</span>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="background: var(--glass-bg); backdrop-filter: blur(25px); border: 2px solid var(--glass-border); border-radius: var(--border-radius); padding: var(--card-padding); text-align: center; margin-bottom: 2rem;">
                <h1 style="color: white; font-size: 2.5rem; margin-bottom: 2rem; text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); line-height: 1.2;">
                    🎉 مرحباً بك في لوحة التحكم المتجاوبة والمتميزة
                </h1>
                <p style="color: rgba(255, 255, 255, 0.8); font-size: 1.2rem; margin-bottom: 2rem; line-height: 1.4;">
                    تصميم متجاوب مثالي مع جميع أحجام الشاشات من الموبايل إلى 4K
                </p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                    <div style="background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(181, 55, 242, 0.2)); padding: 1.5rem; border-radius: 20px; border: 1px solid var(--glass-border);">
                        <h3 style="color: var(--neon-blue); margin-bottom: 0.5rem; font-size: 1.1rem;">📱 متجاوب تماماً</h3>
                        <p style="color: rgba(255, 255, 255, 0.8); margin: 0; font-size: 0.9rem; line-height: 1.3;">يعمل على جميع الأجهزة</p>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(57, 255, 20, 0.2), rgba(0, 242, 254, 0.2)); padding: 1.5rem; border-radius: 20px; border: 1px solid var(--glass-border);">
                        <h3 style="color: var(--neon-green); margin-bottom: 0.5rem; font-size: 1.1rem;">🖥️ دعم 4K</h3>
                        <p style="color: rgba(255, 255, 255, 0.8); margin: 0; font-size: 0.9rem; line-height: 1.3;">شاشات عالية الدقة</p>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 140, 0, 0.2)); padding: 1.5rem; border-radius: 20px; border: 1px solid var(--glass-border);">
                        <h3 style="color: #ffd700; margin-bottom: 0.5rem; font-size: 1.1rem;">⚡ أداء سريع</h3>
                        <p style="color: rgba(255, 255, 255, 0.8); margin: 0; font-size: 0.9rem; line-height: 1.3;">محسن للسرعة</p>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 0, 110, 0.2), rgba(181, 55, 242, 0.2)); padding: 1.5rem; border-radius: 20px; border: 1px solid var(--glass-border);">
                        <h3 style="color: var(--neon-pink); margin-bottom: 0.5rem; font-size: 1.1rem;">🎨 تصميم متميز</h3>
                        <p style="color: rgba(255, 255, 255, 0.8); margin: 0; font-size: 0.9rem; line-height: 1.3;">Glassmorphism حديث</p>
                    </div>
                </div>
                <div style="background: linear-gradient(135deg, rgba(57, 255, 20, 0.1), rgba(0, 242, 254, 0.1)); padding: 1.5rem; border-radius: 15px; border: 1px solid rgba(57, 255, 20, 0.3);">
                    <p style="color: var(--neon-green); margin: 0; font-weight: 600; line-height: 1.3;">
                        🎯 النظام المتجاوب يتكيف تلقائياً مع حجم شاشتك لأفضل تجربة استخدام!
                    </p>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('active');
            }

            document.addEventListener('click', function(event) {
                const sidebar = document.getElementById('sidebar');
                const toggleBtn = document.querySelector('.sidebar-toggle');

                if (window.innerWidth <= 1199) {
                    if (!sidebar.contains(event.target) && !toggleBtn.contains(event.target)) {
                        sidebar.classList.remove('active');
                    }
                }
            });

            document.addEventListener('DOMContentLoaded', function() {
                // دعم الإيماءات اللمسية
                let touchStartX = 0;
                let touchEndX = 0;

                document.addEventListener('touchstart', function(e) {
                    touchStartX = e.changedTouches[0].screenX;
                });

                document.addEventListener('touchend', function(e) {
                    touchEndX = e.changedTouches[0].screenX;
                    handleSwipe();
                });

                function handleSwipe() {
                    const sidebar = document.getElementById('sidebar');
                    if (window.innerWidth <= 1199) {
                        if (touchEndX < touchStartX - 50) {
                            sidebar.classList.remove('active');
                        }
                        if (touchEndX > touchStartX + 50) {
                            sidebar.classList.add('active');
                        }
                    }
                }

                // أرقام متحركة بالعد التصاعدي
                function animateNumbers() {
                    const numbers = document.querySelectorAll('.stat-number[data-target]');

                    numbers.forEach(number => {
                        const target = parseInt(number.getAttribute('data-target'));
                        const duration = 2000;
                        const step = target / (duration / 16);
                        let current = 0;

                        const timer = setInterval(() => {
                            current += step;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            number.textContent = Math.floor(current);
                        }, 16);
                    });
                }

                setTimeout(animateNumbers, 500);

                // تمرير سلس للإحصائيات
                const statsScroll = document.querySelector('.stats-scroll');
                let isDown = false;
                let startX;
                let scrollLeft;

                if (statsScroll) {
                    statsScroll.addEventListener('mousedown', (e) => {
                        isDown = true;
                        startX = e.pageX - statsScroll.offsetLeft;
                        scrollLeft = statsScroll.scrollLeft;
                        statsScroll.style.cursor = 'grabbing';
                    });

                    statsScroll.addEventListener('mouseleave', () => {
                        isDown = false;
                        statsScroll.style.cursor = 'grab';
                    });

                    statsScroll.addEventListener('mouseup', () => {
                        isDown = false;
                        statsScroll.style.cursor = 'grab';
                    });

                    statsScroll.addEventListener('mousemove', (e) => {
                        if (!isDown) return;
                        e.preventDefault();
                        const x = e.pageX - statsScroll.offsetLeft;
                        const walk = (x - startX) * 2;
                        statsScroll.scrollLeft = scrollLeft - walk;
                    });

                    // دعم التمرير بالعجلة
                    statsScroll.addEventListener('wheel', (e) => {
                        e.preventDefault();
                        statsScroll.scrollLeft += e.deltaY;
                    });
                }

                // تأثيرات البطاقات التفاعلية
                const cards = document.querySelectorAll('.stat-card');
                cards.forEach((card, index) => {
                    card.style.animationDelay = `${index * 0.1}s`;

                    card.addEventListener('mouseenter', function() {
                        this.style.zIndex = '10';
                    });

                    card.addEventListener('mouseleave', function() {
                        this.style.zIndex = '1';
                    });
                });

                // تحديد نوع الجهاز وإضافة فئات CSS
                function detectDevice() {
                    const width = window.innerWidth;
                    const height = window.innerHeight;

                    // إزالة جميع فئات الأجهزة
                    document.body.classList.remove('device-4k', 'device-desktop-large', 'device-desktop', 'device-tablet', 'device-mobile', 'device-small-mobile');

                    if (width >= 2560) {
                        document.body.classList.add('device-4k');
                    } else if (width >= 1920) {
                        document.body.classList.add('device-desktop-large');
                    } else if (width >= 1200) {
                        document.body.classList.add('device-desktop');
                    } else if (width >= 768) {
                        document.body.classList.add('device-tablet');
                    } else if (width >= 480) {
                        document.body.classList.add('device-mobile');
                    } else {
                        document.body.classList.add('device-small-mobile');
                    }

                    // إضافة فئة للأجهزة اللمسية
                    if ('ontouchstart' in window) {
                        document.body.classList.add('touch-device');
                    }

                    // إضافة فئة للشاشات عالية الكثافة
                    if (window.devicePixelRatio > 1) {
                        document.body.classList.add('high-dpi');
                    }
                }

                detectDevice();

                // إعادة تحديد نوع الجهاز عند تغيير حجم الشاشة
                window.addEventListener('resize', function() {
                    detectDevice();

                    // إغلاق الشريط الجانبي عند التكبير
                    if (window.innerWidth > 1199) {
                        document.getElementById('sidebar').classList.remove('active');
                    }
                });

                // تحسين الأداء للأجهزة المحمولة
                if (window.innerWidth <= 768) {
                    // تقليل عدد الرسوم المتحركة
                    document.body.classList.add('reduced-animations');

                    // تحسين التمرير
                    document.addEventListener('touchmove', function(e) {
                        if (e.target.closest('.stats-scroll')) {
                            e.stopPropagation();
                        }
                    }, { passive: true });
                }

                // دعم اختصارات لوحة المفاتيح
                document.addEventListener('keydown', function(e) {
                    // ESC لإغلاق الشريط الجانبي
                    if (e.key === 'Escape') {
                        document.getElementById('sidebar').classList.remove('active');
                    }

                    // Ctrl+M لتبديل الشريط الجانبي
                    if (e.ctrlKey && e.key === 'm') {
                        e.preventDefault();
                        toggleSidebar();
                    }
                });

                // تحسين إمكانية الوصول
                const focusableElements = document.querySelectorAll('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
                focusableElements.forEach(element => {
                    element.addEventListener('focus', function() {
                        this.style.outline = '2px solid var(--neon-blue)';
                        this.style.outlineOffset = '2px';
                    });

                    element.addEventListener('blur', function() {
                        this.style.outline = 'none';
                    });
                });
            });
        </script>
    </body>
    </html>
    '''

    # قوالب مبسطة للصفحات الأخرى المتجاوبة
    RESPONSIVE_PAGE_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.5, maximum-scale=3.0">
        <title>{{ page_title }} - نظام إدارة الاشتراكات المتجاوب</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
        <style>
            :root {
                --neon-blue: #00d4ff;
                --neon-purple: #b537f2;
                --neon-pink: #ff006e;
                --neon-green: #39ff14;
                --glass-bg: rgba(255, 255, 255, 0.1);
                --glass-border: rgba(255, 255, 255, 0.2);

                /* متغيرات التجاوب */
                --container-max-width: 1200px;
                --container-padding: 3rem;
                --border-radius: 25px;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            html {
                font-size: 16px;
                scroll-behavior: smooth;
            }

            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
                min-height: 100vh;
                min-height: 100dvh;
                color: white;
                padding: 2rem;
                position: relative;
                overflow-x: hidden;
            }

            /* خلفية متحركة متجاوبة */
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(181, 55, 242, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(255, 0, 110, 0.3) 0%, transparent 50%);
                z-index: -2;
                animation: backgroundPulse 8s ease-in-out infinite;
            }

            @keyframes backgroundPulse {
                0%, 100% {
                    transform: scale(1) rotate(0deg);
                    opacity: 0.8;
                }
                50% {
                    transform: scale(1.1) rotate(2deg);
                    opacity: 1;
                }
            }

            /* حاوي الصفحة المتجاوب */
            .page-container {
                background: var(--glass-bg);
                backdrop-filter: blur(25px);
                border: 2px solid var(--glass-border);
                border-radius: var(--border-radius);
                padding: var(--container-padding);
                text-align: center;
                max-width: var(--container-max-width);
                margin: 0 auto;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
                position: relative;
                overflow: hidden;
                animation: slideInUp 1s ease-out;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(50px) scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            .page-container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
                background-size: 200% 100%;
                animation: gradientShift 3s ease-in-out infinite;
            }

            @keyframes gradientShift {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            .page-title {
                font-size: 2.5rem;
                font-weight: 800;
                margin-bottom: 2rem;
                text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
                animation: titleGlow 2s ease-in-out infinite alternate;
                line-height: 1.2;
            }

            @keyframes titleGlow {
                from { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
                to { text-shadow: 0 0 30px rgba(0, 212, 255, 0.8); }
            }

            .page-content {
                font-size: 1.2rem;
                margin-bottom: 2rem;
                color: rgba(255, 255, 255, 0.8);
                line-height: 1.6;
            }

            .btn-back {
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                color: white;
                border: none;
                border-radius: 20px;
                padding: 1rem 2rem;
                font-weight: 600;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                transition: all 0.3s ease;
                box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
                font-size: 1rem;
                position: relative;
                overflow: hidden;
            }

            .btn-back::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                transition: left 0.5s ease;
            }

            .btn-back:hover::before {
                left: 100%;
            }

            .btn-back:hover {
                transform: translateY(-3px);
                box-shadow: 0 15px 30px rgba(0, 212, 255, 0.4);
                color: white;
                text-decoration: none;
                background: linear-gradient(135deg, var(--neon-purple), var(--neon-pink));
            }

            /* ✅ استجابة للشاشات المختلفة */

            /* شاشات كبيرة جداً (4K وما فوق) */
            @media (min-width: 2560px) {
                :root {
                    --container-max-width: 1600px;
                    --container-padding: 4rem;
                    --border-radius: 35px;
                }

                html {
                    font-size: 20px;
                }

                .page-title {
                    font-size: 3.5rem;
                }

                .page-content {
                    font-size: 1.5rem;
                }

                .btn-back {
                    padding: 1.5rem 2.5rem;
                    font-size: 1.2rem;
                }
            }

            /* شاشات كبيرة (Desktop) */
            @media (min-width: 1920px) and (max-width: 2559px) {
                :root {
                    --container-max-width: 1400px;
                    --container-padding: 3.5rem;
                    --border-radius: 30px;
                }

                html {
                    font-size: 18px;
                }

                .page-title {
                    font-size: 3rem;
                }

                .page-content {
                    font-size: 1.3rem;
                }

                .btn-back {
                    padding: 1.2rem 2.2rem;
                    font-size: 1.1rem;
                }
            }

            /* شاشات متوسطة (Laptop) */
            @media (min-width: 1200px) and (max-width: 1919px) {
                :root {
                    --container-max-width: 1200px;
                    --container-padding: 3rem;
                }

                .page-title {
                    font-size: 2.8rem;
                }
            }

            /* شاشات صغيرة (Tablet) */
            @media (min-width: 768px) and (max-width: 1199px) {
                :root {
                    --container-max-width: 100%;
                    --container-padding: 2.5rem;
                    --border-radius: 20px;
                }

                body {
                    padding: 1.5rem;
                }

                .page-title {
                    font-size: 2.2rem;
                }

                .page-content {
                    font-size: 1.1rem;
                }

                .btn-back {
                    padding: 0.9rem 1.8rem;
                    font-size: 0.95rem;
                }
            }

            /* شاشات صغيرة جداً (Mobile) */
            @media (max-width: 767px) {
                :root {
                    --container-max-width: 100%;
                    --container-padding: 2rem;
                    --border-radius: 18px;
                }

                html {
                    font-size: 14px;
                }

                body {
                    padding: 1rem;
                }

                .page-title {
                    font-size: 1.8rem;
                }

                .page-content {
                    font-size: 1rem;
                }

                .btn-back {
                    padding: 0.8rem 1.5rem;
                    font-size: 0.9rem;
                }
            }

            /* شاشات صغيرة جداً (Small Mobile) */
            @media (max-width: 480px) {
                :root {
                    --container-padding: 1.5rem;
                    --border-radius: 15px;
                }

                html {
                    font-size: 13px;
                }

                body {
                    padding: 0.5rem;
                }

                .page-title {
                    font-size: 1.6rem;
                    margin-bottom: 1.5rem;
                }

                .page-content {
                    font-size: 0.9rem;
                    margin-bottom: 1.5rem;
                }

                .btn-back {
                    padding: 0.7rem 1.2rem;
                    font-size: 0.85rem;
                }
            }

            /* شاشات عمودية طويلة */
            @media (max-height: 600px) and (orientation: landscape) {
                body {
                    padding: 0.5rem;
                }

                .page-container {
                    padding: 1.5rem;
                }

                .page-title {
                    font-size: 1.8rem;
                    margin-bottom: 1rem;
                }

                .page-content {
                    margin-bottom: 1rem;
                }
            }

            /* دعم الشاشات عالية الكثافة */
            @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
                .page-container {
                    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.25);
                }

                .btn-back {
                    box-shadow: 0 15px 30px rgba(0, 212, 255, 0.4);
                }
            }

            /* دعم الوضع المظلم */
            @media (prefers-color-scheme: dark) {
                :root {
                    --glass-bg: rgba(255, 255, 255, 0.08);
                    --glass-border: rgba(255, 255, 255, 0.15);
                }
            }

            /* تحسين الأداء للشاشات البطيئة */
            @media (prefers-reduced-motion: reduce) {
                * {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            }
        </style>
    </head>
    <body>
        <div class="page-container">
            <h1 class="page-title">{{ page_title }}</h1>
            <div class="page-content">{{ page_content }}</div>
            <a href="{{ url_for('dashboard') }}" class="btn-back">
                <i class="fas fa-arrow-right"></i>
                العودة إلى لوحة التحكم المتجاوبة
            </a>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // تأثير الكتابة للعنوان
                const title = document.querySelector('.page-title');
                const originalText = title.textContent;
                title.textContent = '';

                let i = 0;
                const typeWriter = () => {
                    if (i < originalText.length) {
                        title.textContent += originalText.charAt(i);
                        i++;
                        setTimeout(typeWriter, 80);
                    }
                };

                setTimeout(typeWriter, 500);

                // تحديد نوع الجهاز
                function detectDevice() {
                    const width = window.innerWidth;

                    document.body.classList.remove('device-4k', 'device-desktop-large', 'device-desktop', 'device-tablet', 'device-mobile', 'device-small-mobile');

                    if (width >= 2560) {
                        document.body.classList.add('device-4k');
                    } else if (width >= 1920) {
                        document.body.classList.add('device-desktop-large');
                    } else if (width >= 1200) {
                        document.body.classList.add('device-desktop');
                    } else if (width >= 768) {
                        document.body.classList.add('device-tablet');
                    } else if (width >= 480) {
                        document.body.classList.add('device-mobile');
                    } else {
                        document.body.classList.add('device-small-mobile');
                    }

                    if ('ontouchstart' in window) {
                        document.body.classList.add('touch-device');
                    }
                }

                detectDevice();
                window.addEventListener('resize', detectDevice);

                // دعم اختصارات لوحة المفاتيح
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' || (e.ctrlKey && e.key === 'h')) {
                        e.preventDefault();
                        window.location.href = "{{ url_for('dashboard') }}";
                    }
                });
            });
        </script>
    </body>
    </html>
    '''

    # المسارات والوظائف المتجاوبة
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')

            user = User.query.filter_by(username=username).first()

            if user and user.check_password(password):
                login_user(user)
                user.last_login = datetime.now()
                db.session.commit()

                log_activity('تسجيل دخول', f'تم تسجيل الدخول بنجاح من IP: {request.remote_addr}')
                flash('🎉 مرحباً بك في النظام المتجاوب! تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('dashboard'))
            else:
                log_activity('محاولة دخول فاشلة', f'محاولة دخول فاشلة لاسم المستخدم: {username}')
                flash('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

        return render_template_string(LOGIN_TEMPLATE)

    @app.route('/logout')
    @login_required
    def logout():
        log_activity('تسجيل خروج', 'تم تسجيل الخروج من النظام المتجاوب')
        logout_user()
        flash('✅ تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('login'))

    @app.route('/dashboard')
    @login_required
    def dashboard():
        try:
            # حساب الإحصائيات المتطورة
            stats = get_dashboard_stats()

            log_activity('عرض لوحة التحكم', 'تم الوصول إلى لوحة التحكم المتجاوبة')

            return render_template_string(DASHBOARD_TEMPLATE, stats=stats)
        except Exception as e:
            print(f"خطأ في لوحة التحكم المتجاوبة: {e}")
            return render_template_string(
                RESPONSIVE_PAGE_TEMPLATE,
                page_title="خطأ في لوحة التحكم المتجاوبة",
                page_content=f"حدث خطأ: {str(e)}"
            )

    @app.route('/subscriptions')
    @login_required
    def subscriptions_list():
        return render_template_string(
            RESPONSIVE_PAGE_TEMPLATE,
            page_title="📋 إدارة الاشتراكات المتجاوبة",
            page_content="صفحة إدارة الاشتراكات مع تصميم متجاوب مثالي لجميع الأجهزة"
        )

    @app.route('/subscriptions/add')
    @login_required
    def add_subscription():
        return render_template_string(
            RESPONSIVE_PAGE_TEMPLATE,
            page_title="➕ إضافة اشتراك جديد",
            page_content="نموذج متجاوب وشامل لإضافة اشتراك جديد مع دعم جميع أحجام الشاشات"
        )

    @app.route('/subscriptions/analytics')
    @login_required
    def subscription_analytics():
        return render_template_string(
            RESPONSIVE_PAGE_TEMPLATE,
            page_title="📈 تحليلات الاشتراكات المتجاوبة",
            page_content="لوحة تحليلات متجاوبة مع رسوم بيانية تتكيف مع حجم الشاشة"
        )

    @app.route('/communication')
    @login_required
    def communication_center():
        return render_template_string(
            RESPONSIVE_PAGE_TEMPLATE,
            page_title="📧 مركز التواصل المتجاوب",
            page_content="مركز تواصل متطور مع تصميم متجاوب لإدارة التواصل مع العملاء"
        )

    print("✅ تم إعداد المسارات والوظائف المتجاوبة بنجاح")

    # تحسين إنشاء قاعدة البيانات التلقائي المتجاوب
    def init_database():
        with app.app_context():
            try:
                db.create_all()
                print("✅ تم إنشاء جداول قاعدة البيانات المتجاوبة بنجاح")

                # إنشاء مستخدم مدير إذا لم يكن موجوداً
                admin = User.query.filter_by(username='admin').first()
                if not admin:
                    admin = User(
                        username='admin',
                        email='<EMAIL>',
                        full_name='المدير العام - النظام المتجاوب',
                        role='admin',
                        company='AdenLink - العراق',
                        phone='+964-XXX-XXXX',
                        avatar_url='https://via.placeholder.com/150/667eea/ffffff?text=AL'
                    )
                    admin.set_password('123456')
                    db.session.add(admin)

                    # إنشاء البيانات المتطورة والمتجاوبة
                    create_responsive_sample_data()

                    db.session.commit()
                    print("✅ تم إنشاء المستخدم المدير والبيانات المتجاوبة")
                else:
                    print("✅ المستخدم المدير موجود مسبقاً")

            except Exception as e:
                print(f"❌ خطأ في تهيئة قاعدة البيانات المتجاوبة: {e}")
                db.session.rollback()

    def create_responsive_sample_data():
        """إنشاء بيانات تجريبية متجاوبة"""
        try:
            # إنشاء العملات المدعومة
            currencies = [
                Currency(name='دولار أمريكي', code='USD', symbol='$', exchange_rate=1.0, is_default=True),
                Currency(name='يورو', code='EUR', symbol='€', exchange_rate=0.85),
                Currency(name='دينار عراقي', code='IQD', symbol='د.ع', exchange_rate=1310.0),
                Currency(name='ريال سعودي', code='SAR', symbol='ر.س', exchange_rate=3.75),
                Currency(name='درهم إماراتي', code='AED', symbol='د.إ', exchange_rate=3.67),
                Currency(name='جنيه مصري', code='EGP', symbol='ج.م', exchange_rate=30.9),
            ]

            for currency in currencies:
                db.session.add(currency)

            db.session.flush()

            # إنشاء مزودي الخدمة المتجاوبين
            providers = [
                ServiceProvider(
                    name='AdenLink المتجاوب',
                    code='ADENLINK_RESPONSIVE',
                    description='مزود الخدمات السحابية المتجاوب الرائد في العراق',
                    website='https://adenlink.com',
                    logo_url='https://via.placeholder.com/100/667eea/ffffff?text=AL',
                    api_endpoint='https://api.adenlink.com/v2/responsive'
                ),
                ServiceProvider(
                    name='Amazon Web Services',
                    code='AWS',
                    description='منصة الحوسبة السحابية الرائدة عالمياً مع دعم متجاوب',
                    website='https://aws.amazon.com',
                    logo_url='https://via.placeholder.com/100/ff9900/ffffff?text=AWS',
                    api_endpoint='https://ec2.amazonaws.com'
                ),
                ServiceProvider(
                    name='Google Cloud Platform',
                    code='GCP',
                    description='منصة جوجل للحوسبة السحابية مع واجهة متجاوبة',
                    website='https://cloud.google.com',
                    logo_url='https://via.placeholder.com/100/4285f4/ffffff?text=GCP',
                    api_endpoint='https://compute.googleapis.com'
                ),
                ServiceProvider(
                    name='Microsoft Azure',
                    code='AZURE',
                    description='منصة مايكروسوفت السحابية مع تصميم متجاوب',
                    website='https://azure.microsoft.com',
                    logo_url='https://via.placeholder.com/100/0078d4/ffffff?text=AZ',
                    api_endpoint='https://management.azure.com'
                ),
            ]

            for provider in providers:
                db.session.add(provider)

            db.session.flush()

            # إنشاء طرق الدفع المتجاوبة
            payment_methods = [
                PaymentMethod(
                    name='بطاقة ائتمان/خصم',
                    code='CARD',
                    description='فيزا، ماستركارد، أمريكان إكسبريس - واجهة متجاوبة',
                    processing_fee=2.9,
                    fee_type='percentage',
                    icon_class='fas fa-credit-card'
                ),
                PaymentMethod(
                    name='PayPal المتجاوب',
                    code='PAYPAL',
                    description='محفظة PayPal الإلكترونية مع تصميم متجاوب',
                    processing_fee=3.4,
                    fee_type='percentage',
                    icon_class='fab fa-paypal'
                ),
                PaymentMethod(
                    name='تحويل بنكي',
                    code='BANK',
                    description='تحويل مصرفي مباشر مع نظام متجاوب',
                    processing_fee=5.0,
                    fee_type='fixed',
                    icon_class='fas fa-university'
                ),
                PaymentMethod(
                    name='محافظ رقمية',
                    code='DIGITAL_WALLET',
                    description='محافظ رقمية متنوعة مع واجهة متجاوبة',
                    processing_fee=2.5,
                    fee_type='percentage',
                    icon_class='fas fa-wallet'
                ),
                PaymentMethod(
                    name='عملات رقمية',
                    code='CRYPTO',
                    description='بيتكوين وعملات رقمية أخرى - نظام متجاوب',
                    processing_fee=1.5,
                    fee_type='percentage',
                    icon_class='fab fa-bitcoin'
                ),
                PaymentMethod(
                    name='دفع عند الاستلام',
                    code='COD',
                    description='دفع نقدي عند الاستلام مع تتبع متجاوب',
                    processing_fee=10.0,
                    fee_type='fixed',
                    icon_class='fas fa-money-bill-wave'
                ),
            ]

            for method in payment_methods:
                db.session.add(method)

            db.session.flush()

            # إنشاء عملاء تجريبيين
            customers = [
                Customer(
                    name='شركة التقنيات المتجاوبة',
                    email='<EMAIL>',
                    phone='+964-************',
                    company='شركة التقنيات المتجاوبة المحدودة',
                    address='بغداد، العراق',
                    country='العراق',
                    city='بغداد',
                    preferred_currency_id=1
                ),
                Customer(
                    name='مؤسسة الحلول الذكية',
                    email='<EMAIL>',
                    phone='+964-************',
                    company='مؤسسة الحلول الذكية',
                    address='أربيل، العراق',
                    country='العراق',
                    city='أربيل',
                    preferred_currency_id=3
                ),
            ]

            for customer in customers:
                db.session.add(customer)

            db.session.flush()

            print("✅ تم إنشاء البيانات المتجاوبة بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات المتجاوبة: {e}")
            db.session.rollback()

    # تشغيل النظام المتجاوب والمتميز
    if __name__ == '__main__':
        print("🔄 تهيئة قاعدة البيانات المتجاوبة...")
        init_database()

        print("=" * 120)
        print("🎉 نظام إدارة الاشتراكات المتجاوب والمتميز جاهز للتشغيل!")
        print("=" * 120)
        print("🌟 معلومات الوصول:")
        print("   🔗 الرابط: http://localhost:5091")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: 123456")
        print()
        print("✅ الميزات المتجاوبة الجديدة:")
        print("   📱 تصميم متجاوب مثالي (من 320px إلى 4K)")
        print("   🖥️ دعم شاشات 4K وما فوق")
        print("   📱 تحسين خاص للأجهزة المحمولة")
        print("   💻 تحسين للأجهزة اللوحية")
        print("   🖱️ تفاعلات محسنة للماوس واللمس")
        print("   ⚡ أداء محسن لجميع الأجهزة")
        print("   🎨 تأثيرات بصرية متكيفة")
        print("   📏 أحجام خطوط متجاوبة")
        print("   🔄 أشرطة تمرير مخصصة")
        print("   🎯 إمكانية وصول محسنة")
        print()
        print("🎯 أحجام الشاشات المدعومة:")
        print("   📱 الهواتف الصغيرة: 320px - 479px")
        print("   📱 الهواتف: 480px - 767px")
        print("   📱 الأجهزة اللوحية: 768px - 1199px")
        print("   💻 أجهزة الكمبيوتر المحمولة: 1200px - 1919px")
        print("   🖥️ الشاشات الكبيرة: 1920px - 2559px")
        print("   🖥️ شاشات 4K وما فوق: 2560px+")
        print()
        print("🚀 التحسينات المتقدمة:")
        print("   ✅ متغيرات CSS متجاوبة")
        print("   ✅ أحجام خطوط ديناميكية")
        print("   ✅ مساحات متكيفة")
        print("   ✅ أيقونات متجاوبة")
        print("   ✅ تأثيرات محسنة للأداء")
        print("   ✅ دعم الشاشات عالية الكثافة")
        print("   ✅ دعم الوضع المظلم")
        print("   ✅ تحسين للطباعة")
        print("=" * 120)
        print("🚀 بدء تشغيل الخادم المتجاوب والمتميز...")

        try:
            app.run(
                debug=True,
                host='0.0.0.0',
                port=5091,
                threaded=True
            )
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف النظام المتجاوب بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل الخادم المتجاوب: {e}")

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("\n📦 يرجى تثبيت المكتبات المطلوبة:")
    print("pip install flask flask-sqlalchemy flask-login werkzeug")
    print("\n💡 أو استخدم الأمر التالي لتثبيت جميع المتطلبات:")
    print("pip install flask flask-sqlalchemy flask-login werkzeug")
    print("\n🔧 إذا كنت تستخدم Python 3.12+ قد تحتاج:")
    print("pip install --upgrade flask flask-sqlalchemy flask-login werkzeug")
    print("\n📱 هذا النظام محسن للعمل على جميع أحجام الشاشات:")
    print("   📱 الهواتف المحمولة")
    print("   📱 الأجهزة اللوحية")
    print("   💻 أجهزة الكمبيوتر")
    print("   🖥️ الشاشات الكبيرة")
    print("   🖥️ شاشات 4K وما فوق")

    input("\n⏸️ اضغط Enter للخروج...")
    exit(1)

except Exception as e:
    print(f"❌ خطأ في إعداد النظام المتجاوب والمتميز: {e}")
    import traceback
    traceback.print_exc()

    print("\n🔍 نصائح لحل المشاكل:")
    print("1. تأكد من تثبيت جميع المكتبات المطلوبة")
    print("2. تحقق من إصدار Python (يُفضل 3.8+)")
    print("3. تأكد من أن المنفذ 5091 متاح")
    print("4. جرب إعادة تشغيل النظام")
    print("5. احذف ملف قاعدة البيانات وأعد التشغيل")
    print("6. تأكد من دعم المتصفح للميزات الحديثة")
    print("7. جرب تشغيل النظام على أجهزة مختلفة")

    input("\n⏸️ اضغط Enter للخروج...")
    exit(1)

print("\n⏹️ تم إيقاف النظام المتجاوب والمتميز")
print("🙏 شكراً لاستخدام نظام إدارة الاشتراكات المتجاوب!")
print("✨ النظام محسن للعمل على جميع أحجام الشاشات من الموبايل إلى 4K")
print("📱 تجربة مستخدم مثالية على جميع الأجهزة")
input("اضغط Enter للخروج...")
