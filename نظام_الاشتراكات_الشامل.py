#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام إدارة الاشتراكات السحابية الشامل
مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
شركة AdenLink - العراق 🇮🇶
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta
import json

print("🚀 بدء تشغيل نظام إدارة الاشتراكات السحابية الشامل...")

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'comprehensive-subscription-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///comprehensive_subscriptions.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# نماذج قاعدة البيانات الشاملة

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user', nullable=False)
    company = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    email_verified = db.Column(db.Boolean, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

class CloudProvider(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    website = db.Column(db.String(255))
    description = db.Column(db.Text)
    logo_url = db.Column(db.String(255))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class PaymentMethod(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    processing_fee = db.Column(db.Float, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    is_active = db.Column(db.Boolean, default=True)

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    service_type = db.Column(db.String(50))
    price = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    billing_cycle = db.Column(db.String(20), default='monthly')
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    server_ip = db.Column(db.String(45))
    port = db.Column(db.Integer)
    username = db.Column(db.String(100))
    password = db.Column(db.String(255))
    region = db.Column(db.String(50))
    priority = db.Column(db.String(20), default='medium')
    status = db.Column(db.String(20), default='active')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    user = db.relationship('User', backref='subscriptions')
    provider = db.relationship('CloudProvider', backref='subscriptions')

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    subtotal = db.Column(db.Float, nullable=False, default=0.0)
    tax_rate = db.Column(db.Float, default=0.0)
    tax_amount = db.Column(db.Float, default=0.0)
    discount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='pending')
    payment_method = db.Column(db.String(50))
    payment_reference = db.Column(db.String(100))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    user = db.relationship('User', backref='invoices')
    subscription = db.relationship('Subscription', backref='invoices')

class MessageTemplate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    template_type = db.Column(db.String(50), nullable=False)
    variables = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    recipient_email = db.Column(db.String(255), nullable=False)
    recipient_name = db.Column(db.String(100))
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    template_id = db.Column(db.Integer, db.ForeignKey('message_template.id'))
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'))
    sent_at = db.Column(db.DateTime, default=datetime.utcnow)
    delivery_status = db.Column(db.String(20), default='sent')
    
    sender = db.relationship('User', backref='sent_messages')
    template = db.relationship('MessageTemplate', backref='messages')
    subscription = db.relationship('Subscription', backref='messages')

# دوال مساعدة

def get_dashboard_stats():
    """إحصائيات لوحة التحكم"""
    try:
        if current_user.is_admin():
            total_subscriptions = Subscription.query.count()
            active_subscriptions = Subscription.query.filter_by(status='active').count()
            total_users = User.query.count()
            total_invoices = Invoice.query.count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(status='paid').scalar() or 0
            pending_invoices = Invoice.query.filter_by(status='pending').count()
        else:
            total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
            active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
            total_users = 1
            total_invoices = Invoice.query.filter_by(user_id=current_user.id).count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(user_id=current_user.id, status='paid').scalar() or 0
            pending_invoices = Invoice.query.filter_by(user_id=current_user.id, status='pending').count()

        return {
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'expired_subscriptions': total_subscriptions - active_subscriptions,
            'total_users': total_users,
            'total_invoices': total_invoices,
            'pending_invoices': pending_invoices,
            'total_revenue': total_revenue
        }
    except Exception as e:
        print(f"خطأ في إحصائيات لوحة التحكم: {e}")
        return {
            'total_subscriptions': 0,
            'active_subscriptions': 0,
            'expired_subscriptions': 0,
            'total_users': 0,
            'total_invoices': 0,
            'pending_invoices': 0,
            'total_revenue': 0
        }

def generate_invoice_number():
    """توليد رقم فاتورة فريد"""
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return f"INV-{timestamp}"

def calculate_subscription_end_date(start_date, billing_cycle):
    """حساب تاريخ انتهاء الاشتراك"""
    if billing_cycle == 'monthly':
        return start_date + timedelta(days=30)
    elif billing_cycle == 'quarterly':
        return start_date + timedelta(days=90)
    elif billing_cycle == 'semi_annual':
        return start_date + timedelta(days=180)
    elif billing_cycle == 'annual':
        return start_date + timedelta(days=365)
    else:
        return start_date + timedelta(days=30)

def get_subscription_charts_data():
    """بيانات مخططات الاشتراكات"""
    try:
        if current_user.is_admin():
            subscriptions = Subscription.query.all()
        else:
            subscriptions = Subscription.query.filter_by(user_id=current_user.id).all()

        # توزيع حسب الحالة
        status_data = {}
        for sub in subscriptions:
            status_data[sub.status] = status_data.get(sub.status, 0) + 1

        # توزيع حسب المزودين
        provider_data = {}
        for sub in subscriptions:
            provider_name = sub.provider.name if sub.provider else 'غير محدد'
            provider_data[provider_name] = provider_data.get(provider_name, 0) + 1

        # توزيع حسب نوع الفوترة
        billing_data = {}
        for sub in subscriptions:
            billing_data[sub.billing_cycle] = billing_data.get(sub.billing_cycle, 0) + 1

        # الإيرادات الشهرية (بيانات تجريبية)
        monthly_revenue = [
            {'month': 'يناير', 'revenue': 1200},
            {'month': 'فبراير', 'revenue': 1900},
            {'month': 'مارس', 'revenue': 3000},
            {'month': 'أبريل', 'revenue': 5000},
            {'month': 'مايو', 'revenue': 2000},
            {'month': 'يونيو', 'revenue': 3000}
        ]

        return {
            'status_data': status_data,
            'provider_data': provider_data,
            'billing_data': billing_data,
            'monthly_revenue': monthly_revenue
        }
    except Exception as e:
        print(f"خطأ في بيانات المخططات: {e}")
        return {}

print("✅ تم إعداد النماذج والدوال الأساسية")
