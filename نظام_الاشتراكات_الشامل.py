#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام إدارة الاشتراكات السحابية الشامل
مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
شركة AdenLink - العراق 🇮🇶
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta
import json

print("🚀 بدء تشغيل نظام إدارة الاشتراكات السحابية الشامل...")

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'comprehensive-subscription-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///comprehensive_subscriptions.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# نماذج قاعدة البيانات الشاملة

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user', nullable=False)
    company = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    email_verified = db.Column(db.Boolean, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

class CloudProvider(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    website = db.Column(db.String(255))
    description = db.Column(db.Text)
    logo_url = db.Column(db.String(255))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class PaymentMethod(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    processing_fee = db.Column(db.Float, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    is_active = db.Column(db.Boolean, default=True)

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    service_type = db.Column(db.String(50))
    price = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    billing_cycle = db.Column(db.String(20), default='monthly')
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    server_ip = db.Column(db.String(45))
    port = db.Column(db.Integer)
    username = db.Column(db.String(100))
    password = db.Column(db.String(255))
    region = db.Column(db.String(50))
    priority = db.Column(db.String(20), default='medium')
    status = db.Column(db.String(20), default='active')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    user = db.relationship('User', backref='subscriptions')
    provider = db.relationship('CloudProvider', backref='subscriptions')

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    subtotal = db.Column(db.Float, nullable=False, default=0.0)
    tax_rate = db.Column(db.Float, default=0.0)
    tax_amount = db.Column(db.Float, default=0.0)
    discount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='pending')
    payment_method = db.Column(db.String(50))
    payment_reference = db.Column(db.String(100))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    user = db.relationship('User', backref='invoices')
    subscription = db.relationship('Subscription', backref='invoices')

class MessageTemplate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    template_type = db.Column(db.String(50), nullable=False)
    variables = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    recipient_email = db.Column(db.String(255), nullable=False)
    recipient_name = db.Column(db.String(100))
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    template_id = db.Column(db.Integer, db.ForeignKey('message_template.id'))
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'))
    sent_at = db.Column(db.DateTime, default=datetime.utcnow)
    delivery_status = db.Column(db.String(20), default='sent')
    
    sender = db.relationship('User', backref='sent_messages')
    template = db.relationship('MessageTemplate', backref='messages')
    subscription = db.relationship('Subscription', backref='messages')

# دوال مساعدة

def get_dashboard_stats():
    """إحصائيات لوحة التحكم"""
    try:
        if current_user.is_admin():
            total_subscriptions = Subscription.query.count()
            active_subscriptions = Subscription.query.filter_by(status='active').count()
            total_users = User.query.count()
            total_invoices = Invoice.query.count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(status='paid').scalar() or 0
            pending_invoices = Invoice.query.filter_by(status='pending').count()
        else:
            total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
            active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
            total_users = 1
            total_invoices = Invoice.query.filter_by(user_id=current_user.id).count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(user_id=current_user.id, status='paid').scalar() or 0
            pending_invoices = Invoice.query.filter_by(user_id=current_user.id, status='pending').count()

        return {
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'expired_subscriptions': total_subscriptions - active_subscriptions,
            'total_users': total_users,
            'total_invoices': total_invoices,
            'pending_invoices': pending_invoices,
            'total_revenue': total_revenue
        }
    except Exception as e:
        print(f"خطأ في إحصائيات لوحة التحكم: {e}")
        return {
            'total_subscriptions': 0,
            'active_subscriptions': 0,
            'expired_subscriptions': 0,
            'total_users': 0,
            'total_invoices': 0,
            'pending_invoices': 0,
            'total_revenue': 0
        }

def generate_invoice_number():
    """توليد رقم فاتورة فريد"""
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return f"INV-{timestamp}"

def calculate_subscription_end_date(start_date, billing_cycle):
    """حساب تاريخ انتهاء الاشتراك"""
    if billing_cycle == 'monthly':
        return start_date + timedelta(days=30)
    elif billing_cycle == 'quarterly':
        return start_date + timedelta(days=90)
    elif billing_cycle == 'semi_annual':
        return start_date + timedelta(days=180)
    elif billing_cycle == 'annual':
        return start_date + timedelta(days=365)
    else:
        return start_date + timedelta(days=30)

def get_subscription_charts_data():
    """بيانات مخططات الاشتراكات"""
    try:
        if current_user.is_admin():
            subscriptions = Subscription.query.all()
        else:
            subscriptions = Subscription.query.filter_by(user_id=current_user.id).all()

        # توزيع حسب الحالة
        status_data = {}
        for sub in subscriptions:
            status_data[sub.status] = status_data.get(sub.status, 0) + 1

        # توزيع حسب المزودين
        provider_data = {}
        for sub in subscriptions:
            provider_name = sub.provider.name if sub.provider else 'غير محدد'
            provider_data[provider_name] = provider_data.get(provider_name, 0) + 1

        # توزيع حسب نوع الفوترة
        billing_data = {}
        for sub in subscriptions:
            billing_data[sub.billing_cycle] = billing_data.get(sub.billing_cycle, 0) + 1

        # الإيرادات الشهرية (بيانات تجريبية)
        monthly_revenue = [
            {'month': 'يناير', 'revenue': 1200},
            {'month': 'فبراير', 'revenue': 1900},
            {'month': 'مارس', 'revenue': 3000},
            {'month': 'أبريل', 'revenue': 5000},
            {'month': 'مايو', 'revenue': 2000},
            {'month': 'يونيو', 'revenue': 3000}
        ]

        return {
            'status_data': status_data,
            'provider_data': provider_data,
            'billing_data': billing_data,
            'monthly_revenue': monthly_revenue
        }
    except Exception as e:
        print(f"خطأ في بيانات المخططات: {e}")
        return {}

# قوالب HTML الأساسية

LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }
        .logo-section {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo-icon {
            font-size: 4rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .system-title {
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }
        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            padding: 1rem;
        }
        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            color: white;
            box-shadow: none;
        }
        .btn-login {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            width: 100%;
        }
        .demo-credentials {
            background: rgba(0, 212, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <div class="logo-icon">
                <i class="fas fa-cloud"></i>
            </div>
            <h1 class="system-title">نظام إدارة الاشتراكات السحابية</h1>
            <p>مطور بواسطة: المهندس محمد ياسر الجبوري ❤️</p>
            <p>شركة AdenLink - العراق 🇮🇶</p>
        </div>

        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST">
            <div class="mb-3">
                <input type="text" class="form-control" name="username" placeholder="اسم المستخدم" required>
            </div>
            <div class="mb-3">
                <input type="password" class="form-control" name="password" placeholder="كلمة المرور" required>
            </div>
            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
            </button>
        </form>

        <div class="demo-credentials">
            <div><strong>بيانات التجربة:</strong></div>
            <div>اسم المستخدم: <strong>admin</strong></div>
            <div>كلمة المرور: <strong>123456</strong></div>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-left: 2px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            overflow-y: auto;
        }
        .sidebar-header {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }
        .logo {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .nav-link:hover {
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
            border-left-color: #00d4ff;
        }
        .nav-link i {
            width: 20px;
            margin-left: 1rem;
        }
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        .top-bar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .page-title {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        /* تغيير الشبكة لتصبح عمودية */
        .stats-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
        }
        .stat-info {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }
        .stat-icon {
            font-size: 3rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            width: 80px;
            text-align: center;
        }
        .stat-details {
            flex: 1;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }
        .stat-title {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }
        .stat-description {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 0.3rem;
        }
        .btn-logout {
            background: linear-gradient(135deg, #ff006e, #b537f2);
            border: none;
            border-radius: 12px;
            padding: 0.8rem 1.5rem;
            color: white;
            text-decoration: none;
        }
        /* قسم الروابط السريعة بشكل عمودي */
        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
        }
        .action-card {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .action-card:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: #00d4ff;
            color: white;
            text-decoration: none;
            transform: translateX(-5px);
        }
        .action-icon {
            font-size: 2rem;
            color: #00d4ff;
            width: 50px;
            text-align: center;
        }
        .action-content {
            flex: 1;
        }
        .action-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.3rem;
        }
        .action-desc {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            .sidebar.show { transform: translateX(0); }
            .main-content { margin-right: 0; }
            .top-bar { flex-direction: column; gap: 1rem; text-align: center; }
            .stat-card { flex-direction: column; text-align: center; }
            .stat-info { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo"><i class="fas fa-cloud"></i></div>
            <div>نظام إدارة الاشتراكات</div>
            <div>محمد ياسر الجبوري</div>
        </div>

        <nav>
            <a href="{{ url_for('dashboard') }}" class="nav-link">
                <i class="fas fa-tachometer-alt"></i>لوحة التحكم
            </a>
            <a href="{{ url_for('subscriptions') }}" class="nav-link">
                <i class="fas fa-server"></i>إدارة الاشتراكات
            </a>
            <a href="{{ url_for('subscription_charts') }}" class="nav-link">
                <i class="fas fa-chart-pie"></i>مخططات الاشتراكات
            </a>
            <a href="{{ url_for('invoices') }}" class="nav-link">
                <i class="fas fa-file-invoice"></i>إدارة الفواتير
            </a>
            <a href="{{ url_for('customer_statement') }}" class="nav-link">
                <i class="fas fa-file-alt"></i>كشف حساب العملاء
            </a>
            {% if current_user.is_admin() %}
            <a href="{{ url_for('users') }}" class="nav-link">
                <i class="fas fa-users"></i>إدارة المستخدمين
            </a>
            <a href="{{ url_for('providers') }}" class="nav-link">
                <i class="fas fa-cloud"></i>مزودي الخدمة
            </a>
            <a href="{{ url_for('payment_methods') }}" class="nav-link">
                <i class="fas fa-credit-card"></i>طرق الدفع
            </a>
            {% endif %}
        </nav>
    </div>

    <div class="main-content">
        <div class="top-bar">
            <div>
                <h1 class="page-title">لوحة التحكم الشاملة</h1>
                <p class="mb-0">مرحباً بك في نظام إدارة الاشتراكات السحابية</p>
            </div>
            <div>
                <span>{{ current_user.full_name }}</span>
                <a href="{{ url_for('logout') }}" class="btn-logout ms-2">
                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                </a>
            </div>
        </div>

        <!-- الإحصائيات بشكل عمودي -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.total_subscriptions }}</div>
                        <div class="stat-title">إجمالي الاشتراكات</div>
                        <div class="stat-description">جميع الاشتراكات المسجلة في النظام</div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.active_subscriptions }}</div>
                        <div class="stat-title">الاشتراكات النشطة</div>
                        <div class="stat-description">الاشتراكات التي تعمل حالياً</div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.total_invoices }}</div>
                        <div class="stat-title">إجمالي الفواتير</div>
                        <div class="stat-description">جميع الفواتير المُصدرة</div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-number">${{ "%.0f"|format(stats.total_revenue) }}</div>
                        <div class="stat-title">إجمالي الإيرادات</div>
                        <div class="stat-description">المبالغ المحصلة من الفواتير المدفوعة</div>
                    </div>
                </div>
            </div>

            {% if current_user.is_admin() %}
            <div class="stat-card">
                <div class="stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.total_users }}</div>
                        <div class="stat-title">إجمالي المستخدمين</div>
                        <div class="stat-description">جميع المستخدمين المسجلين</div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.pending_invoices }}</div>
                        <div class="stat-title">الفواتير المعلقة</div>
                        <div class="stat-description">الفواتير في انتظار الدفع</div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- الإجراءات السريعة بشكل عمودي -->
        <div class="quick-actions">
            <h3 style="color: #00d4ff; margin-bottom: 1rem;">
                <i class="fas fa-bolt me-2"></i>الإجراءات السريعة
            </h3>

            <a href="{{ url_for('subscriptions') }}" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-server"></i>
                </div>
                <div class="action-content">
                    <div class="action-title">إدارة الاشتراكات</div>
                    <div class="action-desc">عرض وإدارة جميع الاشتراكات السحابية</div>
                </div>
                <div class="action-icon">
                    <i class="fas fa-arrow-left"></i>
                </div>
            </a>

            <a href="{{ url_for('invoices') }}" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="action-content">
                    <div class="action-title">إدارة الفواتير</div>
                    <div class="action-desc">إنشاء وإدارة الفواتير والمدفوعات</div>
                </div>
                <div class="action-icon">
                    <i class="fas fa-arrow-left"></i>
                </div>
            </a>

            <a href="{{ url_for('subscription_charts') }}" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <div class="action-content">
                    <div class="action-title">المخططات التفاعلية</div>
                    <div class="action-desc">رسوم بيانية وتحليلات شاملة</div>
                </div>
                <div class="action-icon">
                    <i class="fas fa-arrow-left"></i>
                </div>
            </a>

            {% if current_user.is_admin() %}
            <a href="{{ url_for('users') }}" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="action-content">
                    <div class="action-title">إدارة المستخدمين</div>
                    <div class="action-desc">إضافة وإدارة مستخدمي النظام</div>
                </div>
                <div class="action-icon">
                    <i class="fas fa-arrow-left"></i>
                </div>
            </a>

            <a href="{{ url_for('providers') }}" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-cloud"></i>
                </div>
                <div class="action-content">
                    <div class="action-title">مزودي الخدمة</div>
                    <div class="action-desc">إدارة مزودي الخدمة السحابية</div>
                </div>
                <div class="action-icon">
                    <i class="fas fa-arrow-left"></i>
                </div>
            </a>
            {% endif %}

            <a href="{{ url_for('customer_statement') }}" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="action-content">
                    <div class="action-title">كشف حساب العملاء</div>
                    <div class="action-desc">تقارير مالية مفصلة وكشوف حساب</div>
                </div>
                <div class="action-icon">
                    <i class="fas fa-arrow-left"></i>
                </div>
            </a>
        </div>
    </div>
</body>
</html>
'''

# المسارات الأساسية

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()
            flash('🎉 مرحباً بك في نظام إدارة الاشتراكات السحابية!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('❌ اسم المستخدم أو كلمة المرور غير صحيحة أو الحساب غير نشط', 'error')

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('✅ تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    stats = get_dashboard_stats()
    return render_template_string(DASHBOARD_TEMPLATE, stats=stats)

# مسار إدارة الاشتراكات
@app.route('/subscriptions')
@login_required
def subscriptions():
    try:
        if current_user.is_admin():
            subscriptions = Subscription.query.order_by(Subscription.created_at.desc()).all()
        else:
            subscriptions = Subscription.query.filter_by(user_id=current_user.id).order_by(Subscription.created_at.desc()).all()

        return render_template_string(SUBSCRIPTIONS_TEMPLATE, subscriptions=subscriptions)
    except Exception as e:
        print(f"خطأ في صفحة الاشتراكات: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# مسار إضافة اشتراك جديد
@app.route('/add_subscription', methods=['GET', 'POST'])
@login_required
def add_subscription():
    if request.method == 'POST':
        try:
            # جمع البيانات من النموذج
            name = request.form.get('name')
            description = request.form.get('description')
            provider_id = request.form.get('provider_id')
            service_type = request.form.get('service_type')
            price = float(request.form.get('price', 0))
            currency = request.form.get('currency', 'USD')
            billing_cycle = request.form.get('billing_cycle', 'monthly')
            start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date()
            server_ip = request.form.get('server_ip')
            port = request.form.get('port')
            username = request.form.get('username')
            password = request.form.get('password')
            region = request.form.get('region')
            priority = request.form.get('priority', 'medium')
            notes = request.form.get('notes')

            # حساب تاريخ الانتهاء
            end_date = calculate_subscription_end_date(start_date, billing_cycle)

            # إنشاء الاشتراك
            subscription = Subscription(
                user_id=current_user.id,
                provider_id=provider_id,
                name=name,
                description=description,
                service_type=service_type,
                price=price,
                currency=currency,
                billing_cycle=billing_cycle,
                start_date=start_date,
                end_date=end_date,
                server_ip=server_ip,
                port=int(port) if port else None,
                username=username,
                password=password,
                region=region,
                priority=priority,
                notes=notes,
                status='active'
            )

            db.session.add(subscription)
            db.session.commit()

            flash('✅ تم إضافة الاشتراك بنجاح!', 'success')
            return redirect(url_for('subscriptions'))

        except Exception as e:
            print(f"خطأ في إضافة الاشتراك: {e}")
            flash(f'حدث خطأ: {str(e)}', 'error')

    try:
        providers = CloudProvider.query.filter_by(is_active=True).all()
        return render_template_string(ADD_SUBSCRIPTION_TEMPLATE, providers=providers)
    except Exception as e:
        print(f"خطأ في صفحة إضافة الاشتراك: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('subscriptions'))

# مسار تجديد الاشتراك
@app.route('/renew_subscription/<int:subscription_id>')
@login_required
def renew_subscription(subscription_id):
    try:
        subscription = Subscription.query.get_or_404(subscription_id)

        # التحقق من الصلاحية
        if not current_user.is_admin() and subscription.user_id != current_user.id:
            flash('❌ ليس لديك صلاحية لتجديد هذا الاشتراك', 'error')
            return redirect(url_for('subscriptions'))

        # تجديد الاشتراك
        old_end_date = subscription.end_date
        subscription.end_date = calculate_subscription_end_date(old_end_date, subscription.billing_cycle)
        subscription.status = 'active'
        subscription.updated_at = datetime.utcnow()

        # إنشاء فاتورة التجديد
        invoice = Invoice(
            invoice_number=generate_invoice_number(),
            user_id=subscription.user_id,
            subscription_id=subscription.id,
            subtotal=subscription.price,
            tax_rate=10.0,
            tax_amount=subscription.price * 0.1,
            total_amount=subscription.price * 1.1,
            currency=subscription.currency,
            issue_date=date.today(),
            due_date=date.today() + timedelta(days=30),
            status='pending'
        )

        db.session.add(invoice)
        db.session.commit()

        flash('✅ تم تجديد الاشتراك وإنشاء فاتورة التجديد بنجاح!', 'success')

    except Exception as e:
        print(f"خطأ في تجديد الاشتراك: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')

    return redirect(url_for('subscriptions'))

# مسار تعديل الاشتراك
@app.route('/edit_subscription/<int:subscription_id>', methods=['GET', 'POST'])
@login_required
def edit_subscription(subscription_id):
    try:
        subscription = Subscription.query.get_or_404(subscription_id)

        # التحقق من الصلاحية
        if not current_user.is_admin() and subscription.user_id != current_user.id:
            flash('❌ ليس لديك صلاحية لتعديل هذا الاشتراك', 'error')
            return redirect(url_for('subscriptions'))

        if request.method == 'POST':
            # تحديث البيانات
            subscription.name = request.form.get('name')
            subscription.description = request.form.get('description')
            subscription.provider_id = request.form.get('provider_id')
            subscription.service_type = request.form.get('service_type')
            subscription.price = float(request.form.get('price', 0))
            subscription.currency = request.form.get('currency', 'USD')
            subscription.billing_cycle = request.form.get('billing_cycle')
            subscription.server_ip = request.form.get('server_ip')
            subscription.port = int(request.form.get('port')) if request.form.get('port') else None
            subscription.username = request.form.get('username')
            subscription.password = request.form.get('password')
            subscription.region = request.form.get('region')
            subscription.priority = request.form.get('priority')
            subscription.notes = request.form.get('notes')
            subscription.updated_at = datetime.utcnow()

            db.session.commit()
            flash('✅ تم تحديث الاشتراك بنجاح!', 'success')
            return redirect(url_for('subscriptions'))

        providers = CloudProvider.query.filter_by(is_active=True).all()
        return render_template_string(EDIT_SUBSCRIPTION_TEMPLATE, subscription=subscription, providers=providers)

    except Exception as e:
        print(f"خطأ في تعديل الاشتراك: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('subscriptions'))

# مسار حذف الاشتراك
@app.route('/delete_subscription/<int:subscription_id>')
@login_required
def delete_subscription(subscription_id):
    try:
        subscription = Subscription.query.get_or_404(subscription_id)

        # التحقق من الصلاحية
        if not current_user.is_admin() and subscription.user_id != current_user.id:
            flash('❌ ليس لديك صلاحية لحذف هذا الاشتراك', 'error')
            return redirect(url_for('subscriptions'))

        db.session.delete(subscription)
        db.session.commit()

        flash('✅ تم حذف الاشتراك بنجاح!', 'success')

    except Exception as e:
        print(f"خطأ في حذف الاشتراك: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')

    return redirect(url_for('subscriptions'))

# قوالب الاشتراكات

SUBSCRIPTIONS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الاشتراكات - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 2rem;
        }
        .page-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .subscriptions-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            padding: 1rem 0;
        }
        .subscription-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            gap: 2rem;
        }
        .subscription-card:hover {
            transform: translateX(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
        }
        .subscription-icon {
            font-size: 3rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            width: 80px;
            text-align: center;
            flex-shrink: 0;
        }
        .subscription-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .subscription-details {
            flex: 1;
        }
        .subscription-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }
        .subscription-provider {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 1rem;
        }
        .subscription-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .info-item {
            display: flex;
            flex-direction: column;
        }
        .info-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 0.3rem;
        }
        .info-value {
            font-weight: 600;
            color: white;
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 1rem;
        }
        .status-active { background: #28a745; }
        .status-expired { background: #dc3545; }
        .status-suspended { background: #ffc107; color: #000; }
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            min-width: 120px;
        }
        .btn-action {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            transition: all 0.3s ease;
        }
        .btn-edit { background: linear-gradient(135deg, #007bff, #0056b3); color: white; }
        .btn-renew { background: linear-gradient(135deg, #28a745, #1e7e34); color: white; }
        .btn-invoice { background: linear-gradient(135deg, #17a2b8, #117a8b); color: white; }
        .btn-delete { background: linear-gradient(135deg, #dc3545, #c82333); color: white; }
        .btn-action:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); }
        .add-subscription-card {
            background: rgba(0, 212, 255, 0.1);
            border: 2px dashed rgba(0, 212, 255, 0.5);
            display: flex;
            align-items: center;
            gap: 2rem;
            padding: 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }
        .add-subscription-card:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
            transform: translateX(-5px);
        }
        .add-icon {
            font-size: 3rem;
            color: #00d4ff;
            width: 80px;
            text-align: center;
            flex-shrink: 0;
        }
        .add-content {
            flex: 1;
        }
        .add-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }
        .add-description {
            color: rgba(255, 255, 255, 0.7);
        }
        .back-btn {
            background: linear-gradient(135deg, #6c757d, #495057);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-server me-3"></i>
            إدارة الاشتراكات السحابية
        </h1>
        <p class="lead mb-3">عرض وإدارة جميع اشتراكاتك السحابية</p>
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>

    {% with messages = get_flashed_messages() %}
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-info">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="subscriptions-container">
        <!-- بطاقة إضافة اشتراك جديد -->
        <div class="add-subscription-card" onclick="window.location.href='{{ url_for('add_subscription') }}'">
            <div class="add-icon">
                <i class="fas fa-plus-circle"></i>
            </div>
            <div class="add-content">
                <div class="add-title">إضافة اشتراك جديد</div>
                <div class="add-description">اضغط هنا لإضافة اشتراك سحابي جديد للنظام</div>
            </div>
            <div class="add-icon">
                <i class="fas fa-arrow-left"></i>
            </div>
        </div>

        <!-- بطاقات الاشتراكات -->
        {% for subscription in subscriptions %}
        <div class="subscription-card">
            <div class="subscription-icon">
                <i class="fas fa-server"></i>
            </div>

            <div class="subscription-content">
                <div class="subscription-details">
                    <div class="subscription-name">{{ subscription.name }}</div>
                    <div class="subscription-provider">{{ subscription.provider.name if subscription.provider else 'غير محدد' }}</div>

                    <div class="subscription-info">
                        <div class="info-item">
                            <div class="info-label">نوع الخدمة</div>
                            <div class="info-value">{{ subscription.service_type or 'غير محدد' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">السعر</div>
                            <div class="info-value">{{ subscription.price }} {{ subscription.currency }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">دورة الفوترة</div>
                            <div class="info-value">{{ subscription.billing_cycle }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">تاريخ الانتهاء</div>
                            <div class="info-value">{{ subscription.end_date }}</div>
                        </div>
                        {% if subscription.server_ip %}
                        <div class="info-item">
                            <div class="info-label">عنوان الخادم</div>
                            <div class="info-value">{{ subscription.server_ip }}{% if subscription.port %}:{{ subscription.port }}{% endif %}</div>
                        </div>
                        {% endif %}
                        {% if subscription.region %}
                        <div class="info-item">
                            <div class="info-label">المنطقة</div>
                            <div class="info-value">{{ subscription.region }}</div>
                        </div>
                        {% endif %}
                        <div class="info-item">
                            <div class="info-label">الأولوية</div>
                            <div class="info-value">{{ subscription.priority }}</div>
                        </div>
                    </div>
                </div>

                <div style="display: flex; flex-direction: column; align-items: center; gap: 1rem;">
                    <div class="status-badge status-{{ subscription.status }}">
                        {{ 'نشط' if subscription.status == 'active' else 'منتهي' if subscription.status == 'expired' else 'موقوف' }}
                    </div>

                    <div class="action-buttons">
                        <a href="{{ url_for('edit_subscription', subscription_id=subscription.id) }}" class="btn-action btn-edit">
                            <i class="fas fa-edit"></i>تعديل
                        </a>
                        <a href="{{ url_for('renew_subscription', subscription_id=subscription.id) }}" class="btn-action btn-renew">
                            <i class="fas fa-redo"></i>تجديد
                        </a>
                        <a href="{{ url_for('add_invoice') }}?subscription_id={{ subscription.id }}" class="btn-action btn-invoice">
                            <i class="fas fa-file-invoice"></i>فاتورة
                        </a>
                        <a href="{{ url_for('delete_subscription', subscription_id=subscription.id) }}"
                           class="btn-action btn-delete"
                           onclick="return confirm('هل أنت متأكد من حذف هذا الاشتراك؟')">
                            <i class="fas fa-trash"></i>حذف
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    {% if not subscriptions %}
    <div class="text-center mt-5">
        <div style="font-size: 4rem; color: rgba(255, 255, 255, 0.3); margin-bottom: 1rem;">
            <i class="fas fa-server"></i>
        </div>
        <h3>لا توجد اشتراكات حالياً</h3>
        <p class="lead">ابدأ بإضافة أول اشتراك سحابي لك</p>
        <a href="{{ url_for('add_subscription') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>إضافة اشتراك جديد
        </a>
    </div>
    {% endif %}
</body>
</html>
'''

ADD_SUBSCRIPTION_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة اشتراك جديد - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 2rem;
        }
        .form-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 2rem;
        }
        .form-section {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #00d4ff;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 0.8rem;
        }
        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
        }
        .btn-submit {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            width: 100%;
        }
        .btn-back {
            background: linear-gradient(135deg, #6c757d, #495057);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1 class="page-title">
            <i class="fas fa-plus-circle me-3"></i>
            إضافة اشتراك سحابي جديد
        </h1>

        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST">
            <!-- المعلومات الأساسية -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-info-circle"></i>
                    المعلومات الأساسية
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم الاشتراك *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">مزود الخدمة *</label>
                        <select class="form-select" name="provider_id" required>
                            <option value="">اختر مزود الخدمة</option>
                            {% for provider in providers %}
                            <option value="{{ provider.id }}">{{ provider.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">وصف الاشتراك</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">نوع الخدمة</label>
                        <select class="form-select" name="service_type">
                            <option value="">اختر نوع الخدمة</option>
                            <option value="VPS">خادم افتراضي (VPS)</option>
                            <option value="Dedicated">خادم مخصص</option>
                            <option value="Cloud Storage">تخزين سحابي</option>
                            <option value="Database">قاعدة بيانات</option>
                            <option value="CDN">شبكة توصيل المحتوى</option>
                            <option value="Other">أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الأولوية</label>
                        <select class="form-select" name="priority">
                            <option value="low">منخفضة</option>
                            <option value="medium" selected>متوسطة</option>
                            <option value="high">عالية</option>
                            <option value="critical">حرجة</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- معلومات الفوترة -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-dollar-sign"></i>
                    معلومات الفوترة
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">السعر *</label>
                        <input type="number" step="0.01" class="form-control" name="price" required>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">العملة</label>
                        <select class="form-select" name="currency">
                            <option value="USD">دولار أمريكي (USD)</option>
                            <option value="EUR">يورو (EUR)</option>
                            <option value="IQD">دينار عراقي (IQD)</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">دورة الفوترة</label>
                        <select class="form-select" name="billing_cycle">
                            <option value="monthly">شهرياً</option>
                            <option value="quarterly">كل 3 أشهر</option>
                            <option value="semi_annual">كل 6 أشهر</option>
                            <option value="annual">سنوياً</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">تاريخ البداية *</label>
                        <input type="date" class="form-control" name="start_date" required>
                    </div>
                </div>
            </div>

            <!-- معلومات الخادم -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-server"></i>
                    معلومات الخادم (اختيارية)
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">عنوان IP</label>
                        <input type="text" class="form-control" name="server_ip" placeholder="***********">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">رقم البورت</label>
                        <input type="number" class="form-control" name="port" placeholder="22">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" name="username">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" name="password">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">المنطقة</label>
                        <input type="text" class="form-control" name="region" placeholder="us-east-1">
                    </div>
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-sticky-note"></i>
                    ملاحظات إضافية
                </div>
                <textarea class="form-control" name="notes" rows="4" placeholder="أي ملاحظات أو تفاصيل إضافية..."></textarea>
            </div>

            <div class="d-flex gap-3">
                <button type="submit" class="btn-submit">
                    <i class="fas fa-save me-2"></i>حفظ الاشتراك
                </button>
                <a href="{{ url_for('subscriptions') }}" class="btn-back">
                    <i class="fas fa-arrow-right me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <script>
        // تعيين تاريخ اليوم كافتراضي
        document.querySelector('input[name="start_date"]').value = new Date().toISOString().split('T')[0];
    </script>
</body>
</html>
'''

# قالب تعديل الاشتراك (مشابه لقالب الإضافة مع القيم المعبأة)
EDIT_SUBSCRIPTION_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الاشتراك - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 2rem;
        }
        .form-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 2rem;
        }
        .form-section {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #00d4ff;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 0.8rem;
        }
        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
        }
        .btn-submit {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            width: 100%;
        }
        .btn-back {
            background: linear-gradient(135deg, #6c757d, #495057);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1 class="page-title">
            <i class="fas fa-edit me-3"></i>
            تعديل الاشتراك: {{ subscription.name }}
        </h1>

        <form method="POST">
            <!-- المعلومات الأساسية -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-info-circle"></i>
                    المعلومات الأساسية
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم الاشتراك *</label>
                        <input type="text" class="form-control" name="name" value="{{ subscription.name }}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">مزود الخدمة *</label>
                        <select class="form-select" name="provider_id" required>
                            {% for provider in providers %}
                            <option value="{{ provider.id }}" {% if provider.id == subscription.provider_id %}selected{% endif %}>
                                {{ provider.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">وصف الاشتراك</label>
                        <textarea class="form-control" name="description" rows="3">{{ subscription.description or '' }}</textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">نوع الخدمة</label>
                        <select class="form-select" name="service_type">
                            <option value="">اختر نوع الخدمة</option>
                            <option value="VPS" {% if subscription.service_type == 'VPS' %}selected{% endif %}>خادم افتراضي (VPS)</option>
                            <option value="Dedicated" {% if subscription.service_type == 'Dedicated' %}selected{% endif %}>خادم مخصص</option>
                            <option value="Cloud Storage" {% if subscription.service_type == 'Cloud Storage' %}selected{% endif %}>تخزين سحابي</option>
                            <option value="Database" {% if subscription.service_type == 'Database' %}selected{% endif %}>قاعدة بيانات</option>
                            <option value="CDN" {% if subscription.service_type == 'CDN' %}selected{% endif %}>شبكة توصيل المحتوى</option>
                            <option value="Other" {% if subscription.service_type == 'Other' %}selected{% endif %}>أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الأولوية</label>
                        <select class="form-select" name="priority">
                            <option value="low" {% if subscription.priority == 'low' %}selected{% endif %}>منخفضة</option>
                            <option value="medium" {% if subscription.priority == 'medium' %}selected{% endif %}>متوسطة</option>
                            <option value="high" {% if subscription.priority == 'high' %}selected{% endif %}>عالية</option>
                            <option value="critical" {% if subscription.priority == 'critical' %}selected{% endif %}>حرجة</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- معلومات الفوترة -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-dollar-sign"></i>
                    معلومات الفوترة
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">السعر *</label>
                        <input type="number" step="0.01" class="form-control" name="price" value="{{ subscription.price }}" required>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">العملة</label>
                        <select class="form-select" name="currency">
                            <option value="USD" {% if subscription.currency == 'USD' %}selected{% endif %}>دولار أمريكي (USD)</option>
                            <option value="EUR" {% if subscription.currency == 'EUR' %}selected{% endif %}>يورو (EUR)</option>
                            <option value="IQD" {% if subscription.currency == 'IQD' %}selected{% endif %}>دينار عراقي (IQD)</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">دورة الفوترة</label>
                        <select class="form-select" name="billing_cycle">
                            <option value="monthly" {% if subscription.billing_cycle == 'monthly' %}selected{% endif %}>شهرياً</option>
                            <option value="quarterly" {% if subscription.billing_cycle == 'quarterly' %}selected{% endif %}>كل 3 أشهر</option>
                            <option value="semi_annual" {% if subscription.billing_cycle == 'semi_annual' %}selected{% endif %}>كل 6 أشهر</option>
                            <option value="annual" {% if subscription.billing_cycle == 'annual' %}selected{% endif %}>سنوياً</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- معلومات الخادم -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-server"></i>
                    معلومات الخادم
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">عنوان IP</label>
                        <input type="text" class="form-control" name="server_ip" value="{{ subscription.server_ip or '' }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">رقم البورت</label>
                        <input type="number" class="form-control" name="port" value="{{ subscription.port or '' }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" name="username" value="{{ subscription.username or '' }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" name="password" value="{{ subscription.password or '' }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">المنطقة</label>
                        <input type="text" class="form-control" name="region" value="{{ subscription.region or '' }}">
                    </div>
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-sticky-note"></i>
                    ملاحظات إضافية
                </div>
                <textarea class="form-control" name="notes" rows="4">{{ subscription.notes or '' }}</textarea>
            </div>

            <div class="d-flex gap-3">
                <button type="submit" class="btn-submit">
                    <i class="fas fa-save me-2"></i>حفظ التعديلات
                </button>
                <a href="{{ url_for('subscriptions') }}" class="btn-back">
                    <i class="fas fa-arrow-right me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</body>
</html>
'''

# مسارات إدارة المستخدمين
@app.route('/users')
@login_required
def users():
    if not current_user.is_admin():
        flash('❌ ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        users = User.query.order_by(User.created_at.desc()).all()
        return render_template_string(USERS_TEMPLATE, users=users)
    except Exception as e:
        print(f"خطأ في صفحة المستخدمين: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/add_user', methods=['GET', 'POST'])
@login_required
def add_user():
    if not current_user.is_admin():
        flash('❌ ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        try:
            username = request.form.get('username')
            email = request.form.get('email')
            full_name = request.form.get('full_name')
            role = request.form.get('role', 'user')
            password = request.form.get('password')
            company = request.form.get('company')
            phone = request.form.get('phone')

            # التحقق من عدم تكرار البيانات
            if User.query.filter_by(username=username).first():
                flash('❌ اسم المستخدم موجود مسبقاً', 'error')
                return render_template_string(ADD_USER_TEMPLATE)

            if User.query.filter_by(email=email).first():
                flash('❌ البريد الإلكتروني موجود مسبقاً', 'error')
                return render_template_string(ADD_USER_TEMPLATE)

            # إنشاء المستخدم الجديد
            user = User(
                username=username,
                email=email,
                full_name=full_name,
                role=role,
                company=company,
                phone=phone,
                is_active=True,
                email_verified=False
            )
            user.set_password(password)

            db.session.add(user)
            db.session.commit()

            flash('✅ تم إضافة المستخدم بنجاح!', 'success')
            return redirect(url_for('users'))

        except Exception as e:
            print(f"خطأ في إضافة المستخدم: {e}")
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template_string(ADD_USER_TEMPLATE)

@app.route('/toggle_user_status/<int:user_id>')
@login_required
def toggle_user_status(user_id):
    if not current_user.is_admin():
        flash('❌ ليس لديك صلاحية لتعديل المستخدمين', 'error')
        return redirect(url_for('dashboard'))

    try:
        user = User.query.get_or_404(user_id)

        # منع تعديل الحساب الحالي
        if user.id == current_user.id:
            flash('❌ لا يمكنك تعديل حالة حسابك الشخصي', 'error')
            return redirect(url_for('users'))

        user.is_active = not user.is_active
        db.session.commit()

        status = 'تم تفعيل' if user.is_active else 'تم تعطيل'
        flash(f'✅ {status} المستخدم {user.full_name} بنجاح!', 'success')

    except Exception as e:
        print(f"خطأ في تغيير حالة المستخدم: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')

    return redirect(url_for('users'))

# قوالب إدارة المستخدمين

USERS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 2rem;
        }
        .page-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .users-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            padding: 1rem 0;
        }
        .user-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            gap: 2rem;
        }
        .user-card:hover {
            transform: translateX(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
        }
        .user-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 1rem;
        }
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        .user-name {
            font-size: 1.3rem;
            font-weight: 700;
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }
        .user-role {
            padding: 0.3rem 0.8rem;
            border-radius: 10px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .role-admin { background: #ff006e; }
        .role-user { background: #28a745; }
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 10px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-top: 0.5rem;
        }
        .status-active { background: #28a745; }
        .status-inactive { background: #dc3545; }
        .user-info {
            margin: 1rem 0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding: 0.3rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }
        .btn-action {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            transition: all 0.3s ease;
        }
        .btn-toggle { background: linear-gradient(135deg, #ffc107, #e0a800); color: #000; }
        .btn-action:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); }
        .add-user-card {
            background: rgba(0, 212, 255, 0.1);
            border: 2px dashed rgba(0, 212, 255, 0.5);
            display: flex;
            align-items: center;
            gap: 2rem;
            padding: 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }
        .add-user-card:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
            transform: translateX(-5px);
        }
        .add-icon {
            font-size: 3rem;
            color: #00d4ff;
            margin-bottom: 1rem;
        }
        .back-btn {
            background: linear-gradient(135deg, #6c757d, #495057);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-users me-3"></i>
            إدارة المستخدمين
        </h1>
        <p class="lead mb-3">عرض وإدارة جميع مستخدمي النظام</p>
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>

    {% with messages = get_flashed_messages() %}
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-info">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="users-container">
        <!-- بطاقة إضافة مستخدم جديد -->
        <div class="user-card add-user-card" onclick="window.location.href='{{ url_for('add_user') }}'">
            <div class="add-icon">
                <i class="fas fa-user-plus"></i>
            </div>
            <h4>إضافة مستخدم جديد</h4>
            <p>اضغط هنا لإضافة مستخدم جديد للنظام</p>
        </div>

        <!-- بطاقات المستخدمين -->
        {% for user in users %}
        <div class="user-card">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>

            <div class="user-header">
                <div>
                    <div class="user-name">{{ user.full_name }}</div>
                    <div class="user-role role-{{ user.role }}">
                        {{ 'مدير' if user.role == 'admin' else 'مستخدم' }}
                    </div>
                    <div class="status-badge status-{{ 'active' if user.is_active else 'inactive' }}">
                        {{ 'نشط' if user.is_active else 'غير نشط' }}
                    </div>
                </div>
            </div>

            <div class="user-info">
                <div class="info-row">
                    <span>اسم المستخدم:</span>
                    <span>{{ user.username }}</span>
                </div>
                <div class="info-row">
                    <span>البريد الإلكتروني:</span>
                    <span>{{ user.email }}</span>
                </div>
                {% if user.company %}
                <div class="info-row">
                    <span>الشركة:</span>
                    <span>{{ user.company }}</span>
                </div>
                {% endif %}
                {% if user.phone %}
                <div class="info-row">
                    <span>الهاتف:</span>
                    <span>{{ user.phone }}</span>
                </div>
                {% endif %}
                <div class="info-row">
                    <span>تاريخ التسجيل:</span>
                    <span>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'غير محدد' }}</span>
                </div>
                {% if user.last_login %}
                <div class="info-row">
                    <span>آخر دخول:</span>
                    <span>{{ user.last_login.strftime('%Y-%m-%d %H:%M') }}</span>
                </div>
                {% endif %}
            </div>

            <div class="action-buttons">
                {% if user.id != current_user.id %}
                <a href="{{ url_for('toggle_user_status', user_id=user.id) }}"
                   class="btn-action btn-toggle"
                   onclick="return confirm('هل أنت متأكد من تغيير حالة هذا المستخدم؟')">
                    <i class="fas fa-{{ 'toggle-off' if user.is_active else 'toggle-on' }}"></i>
                    {{ 'تعطيل' if user.is_active else 'تفعيل' }}
                </a>
                {% else %}
                <span class="btn-action" style="background: #6c757d; cursor: not-allowed;">
                    <i class="fas fa-user-shield"></i>حسابك الشخصي
                </span>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
</body>
</html>
'''

ADD_USER_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مستخدم جديد - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 2rem;
        }
        .form-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 2rem;
        }
        .form-section {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #00d4ff;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 0.8rem;
        }
        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
        }
        .btn-submit {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            width: 100%;
        }
        .btn-back {
            background: linear-gradient(135deg, #6c757d, #495057);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1 class="page-title">
            <i class="fas fa-user-plus me-3"></i>
            إضافة مستخدم جديد
        </h1>

        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST">
            <!-- المعلومات الأساسية -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-user"></i>
                    المعلومات الأساسية
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم المستخدم *</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">البريد الإلكتروني *</label>
                        <input type="email" class="form-control" name="email" required>
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">الاسم الكامل *</label>
                        <input type="text" class="form-control" name="full_name" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الدور *</label>
                        <select class="form-select" name="role" required>
                            <option value="user">مستخدم عادي</option>
                            <option value="admin">مدير</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">كلمة المرور *</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-info-circle"></i>
                    معلومات إضافية
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الشركة</label>
                        <input type="text" class="form-control" name="company">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" name="phone">
                    </div>
                </div>
            </div>

            <div class="d-flex gap-3">
                <button type="submit" class="btn-submit">
                    <i class="fas fa-save me-2"></i>إضافة المستخدم
                </button>
                <a href="{{ url_for('users') }}" class="btn-back">
                    <i class="fas fa-arrow-right me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</body>
</html>
'''

# مسارات إدارة الفواتير
@app.route('/invoices')
@login_required
def invoices():
    try:
        if current_user.is_admin():
            invoices = Invoice.query.order_by(Invoice.created_at.desc()).all()
        else:
            invoices = Invoice.query.filter_by(user_id=current_user.id).order_by(Invoice.created_at.desc()).all()

        return render_template_string(INVOICES_TEMPLATE, invoices=invoices)
    except Exception as e:
        print(f"خطأ في صفحة الفواتير: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/add_invoice', methods=['GET', 'POST'])
@login_required
def add_invoice():
    if request.method == 'POST':
        try:
            subscription_id = request.form.get('subscription_id')
            subtotal = float(request.form.get('subtotal', 0))
            tax_rate = float(request.form.get('tax_rate', 0))
            discount = float(request.form.get('discount', 0))
            payment_method = request.form.get('payment_method')
            due_days = int(request.form.get('due_days', 30))
            notes = request.form.get('notes')

            # حساب المبالغ
            tax_amount = subtotal * (tax_rate / 100)
            total_amount = subtotal + tax_amount - discount

            # إنشاء الفاتورة
            invoice = Invoice(
                invoice_number=generate_invoice_number(),
                user_id=current_user.id,
                subscription_id=subscription_id,
                subtotal=subtotal,
                tax_rate=tax_rate,
                tax_amount=tax_amount,
                discount=discount,
                total_amount=total_amount,
                issue_date=date.today(),
                due_date=date.today() + timedelta(days=due_days),
                payment_method=payment_method,
                notes=notes,
                status='pending'
            )

            db.session.add(invoice)
            db.session.commit()

            flash('✅ تم إنشاء الفاتورة بنجاح!', 'success')
            return redirect(url_for('invoices'))

        except Exception as e:
            print(f"خطأ في إنشاء الفاتورة: {e}")
            flash(f'حدث خطأ: {str(e)}', 'error')

    try:
        # جلب الاشتراكات النشطة
        if current_user.is_admin():
            subscriptions = Subscription.query.filter_by(status='active').all()
        else:
            subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').all()

        payment_methods = PaymentMethod.query.filter_by(is_active=True).all()

        # إذا تم تمرير subscription_id في الرابط
        selected_subscription_id = request.args.get('subscription_id')

        return render_template_string(ADD_INVOICE_TEMPLATE,
                                    subscriptions=subscriptions,
                                    payment_methods=payment_methods,
                                    selected_subscription_id=selected_subscription_id)
    except Exception as e:
        print(f"خطأ في صفحة إضافة الفاتورة: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('invoices'))

@app.route('/update_invoice_status/<int:invoice_id>/<status>')
@login_required
def update_invoice_status(invoice_id, status):
    try:
        invoice = Invoice.query.get_or_404(invoice_id)

        # التحقق من الصلاحية
        if not current_user.is_admin() and invoice.user_id != current_user.id:
            flash('❌ ليس لديك صلاحية لتعديل هذه الفاتورة', 'error')
            return redirect(url_for('invoices'))

        invoice.status = status
        if status == 'paid':
            invoice.paid_date = date.today()

        db.session.commit()

        flash(f'✅ تم تحديث حالة الفاتورة إلى {status}!', 'success')

    except Exception as e:
        print(f"خطأ في تحديث حالة الفاتورة: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')

    return redirect(url_for('invoices'))

# مسارات مزودي الخدمة
@app.route('/providers')
@login_required
def providers():
    if not current_user.is_admin():
        flash('❌ ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        providers = CloudProvider.query.order_by(CloudProvider.created_at.desc()).all()

        # إحصائيات الاشتراكات لكل مزود
        provider_stats = {}
        for provider in providers:
            provider_stats[provider.id] = Subscription.query.filter_by(provider_id=provider.id).count()

        return render_template_string(PROVIDERS_TEMPLATE, providers=providers, provider_stats=provider_stats)
    except Exception as e:
        print(f"خطأ في صفحة مزودي الخدمة: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/add_provider', methods=['GET', 'POST'])
@login_required
def add_provider():
    if not current_user.is_admin():
        flash('❌ ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        try:
            name = request.form.get('name')
            slug = request.form.get('slug')
            website = request.form.get('website')
            description = request.form.get('description')
            logo_url = request.form.get('logo_url')

            # التحقق من عدم تكرار الرمز
            if CloudProvider.query.filter_by(slug=slug).first():
                flash('❌ رمز المزود موجود مسبقاً', 'error')
                return render_template_string(ADD_PROVIDER_TEMPLATE)

            # إنشاء مزود الخدمة
            provider = CloudProvider(
                name=name,
                slug=slug,
                website=website,
                description=description,
                logo_url=logo_url,
                is_active=True
            )

            db.session.add(provider)
            db.session.commit()

            flash('✅ تم إضافة مزود الخدمة بنجاح!', 'success')
            return redirect(url_for('providers'))

        except Exception as e:
            print(f"خطأ في إضافة مزود الخدمة: {e}")
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template_string(ADD_PROVIDER_TEMPLATE)

# مسار طرق الدفع
@app.route('/payment_methods')
@login_required
def payment_methods():
    if not current_user.is_admin():
        flash('❌ ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        payment_methods = PaymentMethod.query.all()
        return render_template_string(PAYMENT_METHODS_TEMPLATE, payment_methods=payment_methods)
    except Exception as e:
        print(f"خطأ في صفحة طرق الدفع: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# مسار مخططات الاشتراكات
@app.route('/subscription_charts')
@login_required
def subscription_charts():
    try:
        charts_data = get_subscription_charts_data()
        stats = get_dashboard_stats()
        return render_template_string(SUBSCRIPTION_CHARTS_TEMPLATE, charts_data=charts_data, stats=stats)
    except Exception as e:
        print(f"خطأ في صفحة المخططات: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# مسار كشف حساب العملاء
@app.route('/customer_statement')
@login_required
def customer_statement():
    try:
        # فلترة التاريخ (افتراضي: آخر 30 يوم)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        user_id = request.args.get('user_id') if current_user.is_admin() else current_user.id

        if not start_date:
            start_date = (date.today() - timedelta(days=30)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = date.today().strftime('%Y-%m-%d')

        # بناء الاستعلام
        query = Invoice.query

        if user_id:
            query = query.filter_by(user_id=user_id)
        elif not current_user.is_admin():
            query = query.filter_by(user_id=current_user.id)

        query = query.filter(Invoice.issue_date >= start_date, Invoice.issue_date <= end_date)
        invoices = query.order_by(Invoice.issue_date.desc()).all()

        # حساب الإحصائيات
        total_invoices = len(invoices)
        total_amount = sum(inv.total_amount for inv in invoices)
        paid_amount = sum(inv.total_amount for inv in invoices if inv.status == 'paid')
        pending_amount = sum(inv.total_amount for inv in invoices if inv.status == 'pending')

        statement_stats = {
            'total_invoices': total_invoices,
            'total_amount': total_amount,
            'paid_amount': paid_amount,
            'pending_amount': pending_amount,
            'start_date': start_date,
            'end_date': end_date
        }

        # جلب المستخدمين للمديرين
        users = User.query.all() if current_user.is_admin() else []

        return render_template_string(CUSTOMER_STATEMENT_TEMPLATE,
                                    invoices=invoices,
                                    statement_stats=statement_stats,
                                    users=users,
                                    selected_user_id=int(user_id) if user_id else None)
    except Exception as e:
        print(f"خطأ في كشف حساب العملاء: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# تهيئة قاعدة البيانات مع البيانات الشاملة
def init_comprehensive_database():
    """تهيئة قاعدة البيانات مع البيانات التجريبية الشاملة"""
    with app.app_context():
        try:
            # إنشاء الجداول
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات")

            # التحقق من وجود البيانات
            if User.query.count() > 0:
                print("✅ البيانات موجودة مسبقاً")
                return

            # إنشاء المستخدمين
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام - محمد ياسر الجبوري',
                role='admin',
                company='AdenLink - العراق',
                phone='+***********-789',
                is_active=True,
                email_verified=True
            )
            admin.set_password('123456')
            db.session.add(admin)

            user1 = User(
                username='user1',
                email='<EMAIL>',
                full_name='أحمد محمد علي',
                role='user',
                company='شركة التقنية المتقدمة',
                phone='+***********-333',
                is_active=True,
                email_verified=True
            )
            user1.set_password('123456')
            db.session.add(user1)

            user2 = User(
                username='user2',
                email='<EMAIL>',
                full_name='فاطمة أحمد حسن',
                role='user',
                company='مؤسسة الحلول الذكية',
                phone='+***********-666',
                is_active=True,
                email_verified=False
            )
            user2.set_password('123456')
            db.session.add(user2)

            user3 = User(
                username='user3',
                email='<EMAIL>',
                full_name='علي حسين محمد',
                role='user',
                company='دار الابتكار التقني',
                phone='+***********-999',
                is_active=False,
                email_verified=True
            )
            user3.set_password('123456')
            db.session.add(user3)

            db.session.flush()
            print("✅ تم إنشاء المستخدمين")

            # إنشاء مزودي الخدمة
            providers_data = [
                ('Amazon Web Services', 'aws', 'https://aws.amazon.com', 'خدمات الحوسبة السحابية الرائدة من أمازون', 'https://logo.clearbit.com/aws.amazon.com'),
                ('Microsoft Azure', 'azure', 'https://azure.microsoft.com', 'منصة الحوسبة السحابية الشاملة من مايكروسوفت', 'https://logo.clearbit.com/azure.microsoft.com'),
                ('Google Cloud Platform', 'gcp', 'https://cloud.google.com', 'خدمات الحوسبة السحابية المتطورة من جوجل', 'https://logo.clearbit.com/cloud.google.com'),
                ('DigitalOcean', 'digitalocean', 'https://digitalocean.com', 'خدمات الخوادم الافتراضية البسيطة والقوية', 'https://logo.clearbit.com/digitalocean.com'),
                ('Vultr', 'vultr', 'https://vultr.com', 'خدمات الخوادم السحابية عالية الأداء', 'https://logo.clearbit.com/vultr.com'),
                ('Linode', 'linode', 'https://linode.com', 'خدمات الخوادم الافتراضية المتقدمة', 'https://logo.clearbit.com/linode.com')
            ]

            for name, slug, website, description, logo_url in providers_data:
                provider = CloudProvider(
                    name=name,
                    slug=slug,
                    website=website,
                    description=description,
                    logo_url=logo_url,
                    is_active=True
                )
                db.session.add(provider)

            db.session.flush()
            print("✅ تم إنشاء مزودي الخدمة")

            # إنشاء طرق الدفع
            payment_methods_data = [
                ('بطاقة ائتمان', 'credit_card', 'دفع بالبطاقة الائتمانية', 2.9, 'USD'),
                ('باي بال', 'paypal', 'دفع عبر باي بال', 3.4, 'USD'),
                ('تحويل بنكي', 'bank_transfer', 'تحويل بنكي مباشر', 0.0, 'USD'),
                ('زين كاش', 'zain_cash', 'دفع عبر زين كاش', 1.5, 'IQD'),
                ('آسيا حوالة', 'asia_hawala', 'دفع عبر آسيا حوالة', 1.0, 'IQD')
            ]

            for name, type_name, description, fee, currency in payment_methods_data:
                payment_method = PaymentMethod(
                    name=name,
                    type=type_name,
                    description=description,
                    processing_fee=fee,
                    currency=currency,
                    is_active=True
                )
                db.session.add(payment_method)

            db.session.flush()
            print("✅ تم إنشاء طرق الدفع")

            # إنشاء الاشتراكات التجريبية
            subscriptions_data = [
                (2, 1, 'خادم ويب أساسي AWS', 'خادم ويب للموقع الشخصي مع قاعدة بيانات', 'VPS', 25.99, 'USD', 'monthly', '***********0', 22, 'ubuntu', 'pass123', 'us-east-1', 'medium', 'active'),
                (2, 2, 'قاعدة بيانات Azure متقدمة', 'قاعدة بيانات SQL للتطبيق الرئيسي', 'Database', 89.99, 'USD', 'monthly', '********', 1433, 'admin', 'sqlpass456', 'east-us', 'high', 'active'),
                (3, 3, 'تخزين سحابي Google', 'مساحة تخزين للملفات والنسخ الاحتياطية', 'Cloud Storage', 15.50, 'USD', 'monthly', None, None, None, None, 'us-central1', 'low', 'active'),
                (3, 4, 'خادم تطوير DigitalOcean', 'خادم للتطوير والاختبار', 'VPS', 12.00, 'USD', 'monthly', '*************', 22, 'root', 'devpass789', 'nyc3', 'medium', 'expired'),
                (1, 5, 'خادم الإنتاج Vultr', 'خادم الإنتاج الرئيسي عالي الأداء', 'Dedicated', 160.00, 'USD', 'monthly', '45.76.123.89', 22, 'admin', 'prodpass321', 'ewr', 'critical', 'active'),
                (2, 6, 'CDN Linode', 'شبكة توصيل المحتوى للموقع', 'CDN', 35.00, 'USD', 'monthly', None, None, None, None, 'us-east', 'medium', 'active'),
                (3, 1, 'خادم النسخ الاحتياطي', 'خادم للنسخ الاحتياطية اليومية', 'VPS', 45.00, 'USD', 'monthly', '************', 22, 'backup', 'backup456', 'us-west-2', 'high', 'suspended')
            ]

            for user_id, provider_id, name, description, service_type, price, currency, billing_cycle, server_ip, port, username, password, region, priority, status in subscriptions_data:
                start_date = date.today() - timedelta(days=30)
                end_date = calculate_subscription_end_date(start_date, billing_cycle)
                if status == 'expired':
                    end_date = date.today() - timedelta(days=5)

                subscription = Subscription(
                    user_id=user_id,
                    provider_id=provider_id,
                    name=name,
                    description=description,
                    service_type=service_type,
                    price=price,
                    currency=currency,
                    billing_cycle=billing_cycle,
                    start_date=start_date,
                    end_date=end_date,
                    server_ip=server_ip,
                    port=port,
                    username=username,
                    password=password,
                    region=region,
                    priority=priority,
                    status=status
                )
                db.session.add(subscription)

            db.session.flush()
            print("✅ تم إنشاء الاشتراكات التجريبية")

            # إنشاء الفواتير التجريبية
            invoices_data = [
                (2, 1, 25.99, 10.0, 0.0, 'credit_card', 'paid', 'فاتورة الاشتراك الشهري'),
                (2, 2, 89.99, 10.0, 5.0, 'paypal', 'pending', 'فاتورة قاعدة البيانات'),
                (3, 3, 15.50, 0.0, 0.0, 'bank_transfer', 'pending', 'فاتورة التخزين السحابي'),
                (1, 5, 160.00, 10.0, 10.0, 'credit_card', 'paid', 'فاتورة خادم الإنتاج'),
                (2, 6, 35.00, 5.0, 0.0, 'zain_cash', 'paid', 'فاتورة CDN'),
                (3, 7, 45.00, 10.0, 0.0, 'asia_hawala', 'cancelled', 'فاتورة النسخ الاحتياطي')
            ]

            for user_id, subscription_id, subtotal, tax_rate, discount, payment_method, status, notes in invoices_data:
                tax_amount = subtotal * (tax_rate / 100)
                total_amount = subtotal + tax_amount - discount

                issue_date = date.today() - timedelta(days=15)
                due_date = date.today() + timedelta(days=15)
                paid_date = date.today() - timedelta(days=5) if status == 'paid' else None

                invoice = Invoice(
                    invoice_number=generate_invoice_number(),
                    user_id=user_id,
                    subscription_id=subscription_id,
                    subtotal=subtotal,
                    tax_rate=tax_rate,
                    tax_amount=tax_amount,
                    discount=discount,
                    total_amount=total_amount,
                    issue_date=issue_date,
                    due_date=due_date,
                    paid_date=paid_date,
                    status=status,
                    payment_method=payment_method,
                    notes=notes
                )
                db.session.add(invoice)

            db.session.flush()
            print("✅ تم إنشاء الفواتير التجريبية")

            # إنشاء قوالب الرسائل
            templates_data = [
                ('ترحيب بالعضو الجديد', 'مرحباً بك في نظام إدارة الاشتراكات', 'مرحباً {user_name}، نرحب بك في نظامنا المتطور لإدارة الاشتراكات السحابية...', 'welcome', '{"user_name": "اسم المستخدم"}'),
                ('تذكير انتهاء الاشتراك', 'تذكير: اشتراكك ينتهي قريباً', 'عزيز {user_name}، نود تذكيرك بأن اشتراك {subscription_name} سينتهي في {days_left} أيام...', 'expiry_reminder', '{"user_name": "اسم المستخدم", "subscription_name": "اسم الاشتراك", "days_left": "عدد الأيام المتبقية"}'),
                ('فاتورة جديدة', 'فاتورة جديدة رقم {invoice_number}', 'تم إنشاء فاتورة جديدة بمبلغ {total_amount} {currency} لاشتراك {subscription_name}...', 'new_invoice', '{"invoice_number": "رقم الفاتورة", "total_amount": "المبلغ الإجمالي", "currency": "العملة", "subscription_name": "اسم الاشتراك"}')
            ]

            for name, subject, body, template_type, variables in templates_data:
                template = MessageTemplate(
                    name=name,
                    subject=subject,
                    body=body,
                    template_type=template_type,
                    variables=variables,
                    is_active=True
                )
                db.session.add(template)

            db.session.flush()
            print("✅ تم إنشاء قوالب الرسائل")

            # إنشاء رسائل تجريبية
            messages_data = [
                (1, '<EMAIL>', 'أحمد محمد علي', 'مرحباً بك في نظام إدارة الاشتراكات', 'مرحباً أحمد، نرحب بك في نظامنا المتطور...', 1, 1),
                (1, '<EMAIL>', 'فاطمة أحمد حسن', 'تذكير: اشتراكك ينتهي قريباً', 'عزيزة فاطمة، نود تذكيرك بأن اشتراكك سينتهي قريباً...', 2, 3),
                (1, '<EMAIL>', 'أحمد محمد علي', 'فاتورة جديدة رقم INV-001', 'تم إنشاء فاتورة جديدة بمبلغ 28.59 USD...', 3, 1)
            ]

            for sender_id, recipient_email, recipient_name, subject, body, template_id, subscription_id in messages_data:
                message = Message(
                    sender_id=sender_id,
                    recipient_email=recipient_email,
                    recipient_name=recipient_name,
                    subject=subject,
                    body=body,
                    template_id=template_id,
                    subscription_id=subscription_id,
                    delivery_status='sent'
                )
                db.session.add(message)

            print("✅ تم إنشاء الرسائل التجريبية")

            # حفظ جميع التغييرات
            db.session.commit()
            print("✅ تم حفظ جميع البيانات التجريبية الشاملة بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            db.session.rollback()
            raise e

# تشغيل النظام الشامل
if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام إدارة الاشتراكات السحابية الشامل...")
    print("=" * 80)
    print("🎯 نظام إدارة الاشتراكات السحابية الشامل والمتطور")
    print("💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️")
    print("🏢 شركة AdenLink - العراق 🇮🇶")
    print("📅 تاريخ التطوير: 2024")
    print("=" * 80)

    try:
        # تهيئة قاعدة البيانات
        print("📊 تهيئة قاعدة البيانات الشاملة...")
        init_comprehensive_database()

        print("\n✅ تم تهيئة النظام الشامل بنجاح!")
        print("=" * 80)
        print("🌐 معلومات الوصول:")
        print("🔗 الرابط: http://localhost:5000")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: 123456")
        print("=" * 80)
        print("🎮 الميزات الشاملة المتاحة:")
        print("📊 • لوحة تحكم متطورة مع إحصائيات شاملة")
        print("📋 • إدارة الاشتراكات الكاملة (إضافة، تعديل، تجديد، حذف)")
        print("👥 • إدارة المستخدمين الشاملة (للمديرين)")
        print("🧾 • إدارة الفواتير المتقدمة مع حسابات تلقائية")
        print("☁️ • إدارة مزودي الخدمة السحابية")
        print("💳 • إدارة طرق الدفع المتعددة")
        print("📊 • مخططات تفاعلية متطورة (4 أنواع)")
        print("📋 • كشف حساب العملاء مع فلترة متقدمة")
        print("📧 • نظام رسائل متطور مع قوالب ديناميكية")
        print("🎨 • تصميم متجاوب ومتطور مع شريط جانبي")
        print("🔐 • نظام صلاحيات متقدم وأمان عالي")
        print("⚡ • أداء محسن وسرعة عالية")
        print("📱 • دعم كامل للأجهزة المحمولة")
        print("=" * 80)
        print("📋 الصفحات المتاحة:")
        print("🏠 • لوحة التحكم: /dashboard")
        print("📋 • إدارة الاشتراكات: /subscriptions")
        print("➕ • إضافة اشتراك: /add_subscription")
        print("✏️ • تعديل اشتراك: /edit_subscription/<id>")
        print("🔄 • تجديد اشتراك: /renew_subscription/<id>")
        print("🗑️ • حذف اشتراك: /delete_subscription/<id>")
        print("👥 • إدارة المستخدمين: /users (للمديرين)")
        print("➕ • إضافة مستخدم: /add_user (للمديرين)")
        print("☁️ • مزودي الخدمة: /providers (للمديرين)")
        print("➕ • إضافة مزود: /add_provider (للمديرين)")
        print("🧾 • إدارة الفواتير: /invoices")
        print("➕ • إضافة فاتورة: /add_invoice")
        print("💳 • طرق الدفع: /payment_methods (للمديرين)")
        print("📊 • مخططات الاشتراكات: /subscription_charts")
        print("📋 • كشف حساب العملاء: /customer_statement")
        print("=" * 80)
        print("👥 المستخدمين التجريبيين:")
        print("🔹 admin / 123456 (مدير)")
        print("🔹 user1 / 123456 (مستخدم)")
        print("🔹 user2 / 123456 (مستخدم)")
        print("🔹 user3 / 123456 (مستخدم - معطل)")
        print("=" * 80)
        print("📊 البيانات التجريبية:")
        print("🔹 4 مستخدمين مع معلومات كاملة")
        print("🔹 6 مزودي خدمة سحابية")
        print("🔹 5 طرق دفع متنوعة")
        print("🔹 7 اشتراكات بحالات مختلفة")
        print("🔹 6 فواتير بحالات متعددة")
        print("🔹 3 قوالب رسائل ديناميكية")
        print("🔹 3 رسائل تجريبية مرسلة")
        print("=" * 80)
        print("🚀 النظام الشامل جاهز للاستخدام!")
        print("=" * 80)

        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("🔧 يرجى التحقق من:")
        print("   • تثبيت جميع المكتبات المطلوبة")
        print("   • عدم استخدام البورت 5000 من تطبيق آخر")
        print("   • صلاحيات الكتابة في مجلد التطبيق")
        print("   • إصدار Python 3.8 أو أحدث")

print("🎉 تم إعداد النظام الشامل لإدارة الاشتراكات السحابية بالكامل!")
print("🌟 النظام يحتوي على جميع الميزات المطلوبة:")
print("   👥 إدارة المستخدمين الكاملة")
print("   📋 إدارة الاشتراكات الشاملة")
print("   🧾 إدارة الفواتير المتقدمة")
print("   ☁️ إدارة مزودي الخدمة")
print("   💳 إدارة طرق الدفع")
print("   📊 مخططات تفاعلية متطورة")
print("   📋 كشف حساب العملاء")
print("   📧 نظام رسائل متطور")
print("   🎨 تصميم متجاوب ومتطور")
print("   🔐 نظام أمان وصلاحيات متقدم")
print("   ⚡ أداء عالي ومحسن")
print("🚀 النظام جاهز للتشغيل والاستخدام الفوري!")
print("💻 مطور بحب وإتقان بواسطة: المهندس محمد ياسر الجبوري ❤️")
print("🏢 شركة AdenLink - العراق 🇮🇶")
