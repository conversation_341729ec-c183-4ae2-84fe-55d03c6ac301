@echo off
chcp 65001 >nul
cd /d "%~dp0"

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
echo ║                                                🚀 نظام إدارة الاشتراكات المتجاوب والمتميز                                                                      ║
echo ║                                                                  AdenLink - العراق                                                                              ║
echo ║                                                      النسخة المتجاوبة مع دعم جميع أحجام الشاشات                                                                ║
echo ╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🌟 تشغيل النظام المتجاوب والمتميز...
echo 📂 المجلد الحالي: %CD%
echo.
echo 🔍 فحص متطلبات النظام المتجاوب...
echo 🐍 إصدار Python:
python --version
echo.
echo 📦 فحص المكتبات المطلوبة...
python -c "import flask; print('✅ Flask متاح - الإصدار:', flask.__version__)" 2>nul || echo "❌ Flask غير متاح - يرجى التثبيت"
python -c "import flask_sqlalchemy; print('✅ Flask-SQLAlchemy متاح')" 2>nul || echo "❌ Flask-SQLAlchemy غير متاح"
python -c "import flask_login; print('✅ Flask-Login متاح')" 2>nul || echo "❌ Flask-Login غير متاح"
python -c "import werkzeug; print('✅ Werkzeug متاح')" 2>nul || echo "❌ Werkzeug غير متاح"
echo.
echo ⚡ تشغيل النظام المتجاوب والمتميز...
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
echo ║                                                        ✨ النظام المتجاوب والمتميز ✨                                                                        ║
echo ║                                                                                                                                                                  ║
echo ║  🌐 الرابط: http://localhost:5091                                                                                                                              ║
echo ║  👤 اسم المستخدم: admin                                                                                                                                        ║
echo ║  🔑 كلمة المرور: 123456                                                                                                                                        ║
echo ║                                                                                                                                                                  ║
echo ║  📱 الميزات المتجاوبة الجديدة:                                                                                                                                  ║
echo ║  🎯 تصميم متجاوب مثالي (من 320px إلى 4K)                                                                                                                     ║
echo ║  🖥️ دعم شاشات 4K وما فوق                                                                                                                                      ║
echo ║  📱 تحسين خاص للأجهزة المحمولة                                                                                                                                ║
echo ║  💻 تحسين للأجهزة اللوحية                                                                                                                                     ║
echo ║  🖱️ تفاعلات محسنة للماوس واللمس                                                                                                                               ║
echo ║  ⚡ أداء محسن لجميع الأجهزة                                                                                                                                    ║
echo ║  🎨 تأثيرات بصرية متكيفة                                                                                                                                       ║
echo ║  📏 أحجام خطوط متجاوبة                                                                                                                                        ║
echo ║  🔄 أشرطة تمرير مخصصة                                                                                                                                         ║
echo ║  🎯 إمكانية وصول محسنة                                                                                                                                        ║
echo ║                                                                                                                                                                  ║
echo ║  🎯 أحجام الشاشات المدعومة:                                                                                                                                    ║
echo ║  📱 الهواتف الصغيرة: 320px - 479px                                                                                                                            ║
echo ║  📱 الهواتف: 480px - 767px                                                                                                                                     ║
echo ║  📱 الأجهزة اللوحية: 768px - 1199px                                                                                                                           ║
echo ║  💻 أجهزة الكمبيوتر المحمولة: 1200px - 1919px                                                                                                                ║
echo ║  🖥️ الشاشات الكبيرة: 1920px - 2559px                                                                                                                          ║
echo ║  🖥️ شاشات 4K وما فوق: 2560px+                                                                                                                                 ║
echo ║                                                                                                                                                                  ║
echo ║  🚀 التحسينات المتقدمة:                                                                                                                                        ║
echo ║  ✅ متغيرات CSS متجاوبة                                                                                                                                        ║
echo ║  ✅ أحجام خطوط ديناميكية                                                                                                                                      ║
echo ║  ✅ مساحات متكيفة                                                                                                                                              ║
echo ║  ✅ أيقونات متجاوبة                                                                                                                                            ║
echo ║  ✅ تأثيرات محسنة للأداء                                                                                                                                       ║
echo ║  ✅ دعم الشاشات عالية الكثافة                                                                                                                                  ║
echo ║  ✅ دعم الوضع المظلم                                                                                                                                           ║
echo ║  ✅ تحسين للطباعة                                                                                                                                              ║
echo ║                                                                                                                                                                  ║
echo ║  🎨 التصميم المتميز:                                                                                                                                            ║
echo ║  ✨ تأثيرات Glassmorphism متطورة                                                                                                                               ║
echo ║  🌈 ألوان نيون متدرجة ومتحركة                                                                                                                                  ║
echo ║  🎭 رسوم متحركة سلسة ومتكيفة                                                                                                                                   ║
echo ║  🎮 تفاعلات متقدمة ومتجاوبة                                                                                                                                    ║
echo ║  📱 واجهة مستخدم حديثة ومتطورة                                                                                                                                 ║
echo ║                                                                                                                                                                  ║
echo ║  🔥 النظام المتجاوب يعمل بشكل مثالي على جميع الأجهزة!                                                                                                        ║
echo ║                                                                                                                                                                  ║
echo ╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
echo.

python responsive_perfect_system.py

echo.
echo ⏹️ تم إيقاف النظام المتجاوب والمتميز
echo.
echo 📝 ملاحظات مهمة:
echo • النظام محسن للعمل على جميع أحجام الشاشات
echo • تصميم متجاوب مثالي من الموبايل إلى 4K
echo • تأثيرات بصرية متكيفة مع حجم الشاشة
echo • أداء محسن لجميع الأجهزة
echo • واجهة مستخدم حديثة ومتطورة
echo • دعم كامل للأجهزة اللمسية
echo • أشرطة تمرير مخصصة ومتجاوبة
echo • إمكانية وصول محسنة
echo.
echo 💡 للحصول على الدعم:
echo • راجع ملف RESPONSIVE_README.md
echo • جرب النظام على أجهزة مختلفة
echo • اختبر أحجام شاشات متنوعة
echo • تحقق من التجاوب في أدوات المطور
echo.
echo 🔄 اضغط أي مفتاح للخروج...
pause >nul
