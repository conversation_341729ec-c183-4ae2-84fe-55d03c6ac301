{% extends "base.html" %}

{% block title %}مركز التواصل - نظام إدارة الاشتراكات{% endblock %}

{% block extra_css %}
<style>
.communication-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.communication-title {
    color: #00f5ff;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 0 20px #00f5ff;
    margin-bottom: 10px;
}

.email-form-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    margin-bottom: 30px;
}

.templates-section {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.section-title {
    color: #bf00ff;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #bf00ff;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-left: 10px;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.template-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.template-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.template-card.selected {
    border-color: #00f5ff;
    box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
}

.template-title {
    color: white;
    font-weight: 600;
    margin-bottom: 10px;
}

.template-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.template-preview {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 10px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    font-family: monospace;
    max-height: 100px;
    overflow: hidden;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    width: 100%;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: #00f5ff;
    box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
    color: white;
    outline: none;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-select {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    padding: 12px 15px;
    backdrop-filter: blur(10px);
}

.form-select option {
    background: #1a1a1a;
    color: white;
}

.variables-info {
    background: rgba(0, 245, 255, 0.1);
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.variables-title {
    color: #00f5ff;
    font-weight: 600;
    margin-bottom: 10px;
}

.variables-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.variable-item {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    padding: 5px 10px;
    font-family: monospace;
    font-size: 0.9rem;
    color: #00f5ff;
}

.preview-section {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.preview-content {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    min-height: 150px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-send {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 15px;
    color: white;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-send:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-preview {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    color: rgba(255, 255, 255, 0.8);
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-preview:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-3px);
}

@media (max-width: 768px) {
    .communication-title {
        font-size: 2rem;
    }
    
    .email-form-container {
        padding: 25px 20px;
    }
    
    .template-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .variables-list {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-send,
    .btn-preview {
        width: 100%;
        max-width: 300px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="communication-header">
        <h1 class="communication-title">
            <i class="fas fa-envelope me-3"></i>
            مركز التواصل
        </h1>
        <p style="color: rgba(255, 255, 255, 0.7); font-size: 1.1rem;">
            إرسال رسائل إلكترونية للعملاء مع قوالب جاهزة ومتغيرات ديناميكية
        </p>
    </div>
    
    <!-- Email Templates -->
    <div class="templates-section">
        <h3 class="section-title">
            <i class="fas fa-file-alt"></i>
            القوالب الجاهزة
        </h3>
        
        <div class="template-grid">
            <div class="template-card" data-template="renewal">
                <div class="template-title">تذكير التجديد</div>
                <div class="template-description">رسالة تذكير للعملاء بقرب انتهاء اشتراكهم</div>
                <div class="template-preview">
                    عزيزي {اسم_العميل}،
                    نود تذكيرك بأن اشتراكك في {اسم_الاشتراك} سينتهي في {تاريخ_الانتهاء}...
                </div>
            </div>
            
            <div class="template-card" data-template="payment">
                <div class="template-title">استحقاق الدفع</div>
                <div class="template-description">إشعار بوجود فاتورة مستحقة الدفع</div>
                <div class="template-preview">
                    عزيزي {اسم_العميل}،
                    لديك فاتورة بمبلغ {المبلغ} مستحقة الدفع بتاريخ {تاريخ_الاستحقاق}...
                </div>
            </div>
            
            <div class="template-card" data-template="welcome">
                <div class="template-title">رسالة ترحيب</div>
                <div class="template-description">رسالة ترحيب للعملاء الجدد</div>
                <div class="template-preview">
                    مرحباً {اسم_العميل}،
                    نرحب بك في خدماتنا ونشكرك على اختيار {اسم_الاشتراك}...
                </div>
            </div>
            
            <div class="template-card" data-template="custom">
                <div class="template-title">رسالة مخصصة</div>
                <div class="template-description">إنشاء رسالة مخصصة من البداية</div>
                <div class="template-preview">
                    ابدأ بكتابة رسالتك المخصصة...
                </div>
            </div>
        </div>
    </div>
    
    <!-- Email Form -->
    <div class="email-form-container">
        <form method="POST" id="emailForm">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">المرسل إليه</label>
                    <select class="form-select" name="recipient_type" id="recipientType">
                        <option value="all">جميع العملاء</option>
                        <option value="active">العملاء النشطين</option>
                        <option value="expiring">الاشتراكات المنتهية قريباً</option>
                        <option value="custom">عناوين مخصصة</option>
                    </select>
                </div>
                
                <div class="form-group" id="customEmailsGroup" style="display: none;">
                    <label class="form-label">العناوين المخصصة</label>
                    <input type="text" class="form-control" name="custom_emails" 
                           placeholder="<EMAIL>, <EMAIL>">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">موضوع الرسالة</label>
                    <input type="text" class="form-control" name="subject" id="emailSubject"
                           placeholder="أدخل موضوع الرسالة">
                </div>
                
                <div class="form-group">
                    <label class="form-label">الأولوية</label>
                    <select class="form-select" name="priority">
                        <option value="normal">عادية</option>
                        <option value="high">عالية</option>
                        <option value="urgent">عاجلة</option>
                    </select>
                </div>
            </div>
            
            <!-- Variables Info -->
            <div class="variables-info">
                <div class="variables-title">
                    <i class="fas fa-code me-2"></i>
                    المتغيرات المتاحة
                </div>
                <div class="variables-list">
                    <div class="variable-item">{اسم_العميل}</div>
                    <div class="variable-item">{اسم_الاشتراك}</div>
                    <div class="variable-item">{المزود}</div>
                    <div class="variable-item">{السعر}</div>
                    <div class="variable-item">{تاريخ_الانتهاء}</div>
                    <div class="variable-item">{الأيام_المتبقية}</div>
                    <div class="variable-item">{رقم_الفاتورة}</div>
                    <div class="variable-item">{المبلغ}</div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">محتوى الرسالة</label>
                <textarea class="form-control" name="message" id="emailMessage" rows="10"
                          placeholder="اكتب محتوى الرسالة هنا..."></textarea>
            </div>
            
            <!-- Preview Section -->
            <div class="preview-section" id="previewSection" style="display: none;">
                <h4 style="color: #bf00ff; margin-bottom: 15px;">
                    <i class="fas fa-eye me-2"></i>
                    معاينة الرسالة
                </h4>
                <div class="preview-content" id="previewContent">
                    <!-- Preview will be shown here -->
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" class="btn-preview crystal-btn" id="previewBtn">
                    <i class="fas fa-eye me-2"></i>
                    معاينة الرسالة
                </button>
                
                <button type="submit" class="btn-send crystal-btn ripple">
                    <i class="fas fa-paper-plane me-2"></i>
                    إرسال الرسالة
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // قوالب الرسائل
    const templates = {
        renewal: {
            subject: 'تذكير: انتهاء اشتراكك قريباً',
            message: `عزيزي {اسم_العميل},

نود تذكيرك بأن اشتراكك في {اسم_الاشتراك} من {المزود} سينتهي في {تاريخ_الانتهاء}.

تفاصيل الاشتراك:
- الخدمة: {اسم_الاشتراك}
- المزود: {المزود}
- السعر: {السعر}
- الأيام المتبقية: {الأيام_المتبقية}

لتجديد اشتراكك، يرجى التواصل معنا أو زيارة موقعنا الإلكتروني.

شكراً لك على ثقتك بخدماتنا.

مع أطيب التحيات,
فريق إدارة الاشتراكات`
        },
        payment: {
            subject: 'فاتورة مستحقة الدفع - {رقم_الفاتورة}',
            message: `عزيزي {اسم_العميل},

لديك فاتورة مستحقة الدفع بالتفاصيل التالية:

- رقم الفاتورة: {رقم_الفاتورة}
- المبلغ: {المبلغ}
- تاريخ الاستحقاق: {تاريخ_الاستحقاق}
- الخدمة: {اسم_الاشتراك}

يرجى سداد المبلغ في أقرب وقت ممكن لتجنب انقطاع الخدمة.

لأي استفسارات، لا تتردد في التواصل معنا.

شكراً لك,
فريق المحاسبة`
        },
        welcome: {
            subject: 'مرحباً بك في خدماتنا!',
            message: `مرحباً {اسم_العميل},

نرحب بك في خدماتنا ونشكرك على اختيار {اسم_الاشتراك} من {المزود}.

تفاصيل اشتراكك:
- الخدمة: {اسم_الاشتراك}
- المزود: {المزود}
- السعر: {السعر}
- تاريخ البداية: {تاريخ_البداية}
- تاريخ الانتهاء: {تاريخ_الانتهاء}

نحن ملتزمون بتقديم أفضل خدمة لك. إذا كان لديك أي أسئلة أو تحتاج إلى مساعدة، فلا تتردد في التواصل معنا.

مرحباً بك مرة أخرى!

فريق خدمة العملاء`
        },
        custom: {
            subject: '',
            message: ''
        }
    };
    
    // التعامل مع اختيار القوالب
    const templateCards = document.querySelectorAll('.template-card');
    const emailSubject = document.getElementById('emailSubject');
    const emailMessage = document.getElementById('emailMessage');
    
    templateCards.forEach(card => {
        card.addEventListener('click', function() {
            // إزالة التحديد من جميع البطاقات
            templateCards.forEach(c => c.classList.remove('selected'));
            
            // تحديد البطاقة المختارة
            this.classList.add('selected');
            
            // تطبيق القالب
            const templateType = this.dataset.template;
            const template = templates[templateType];
            
            emailSubject.value = template.subject;
            emailMessage.value = template.message;
        });
    });
    
    // التعامل مع نوع المستقبل
    const recipientType = document.getElementById('recipientType');
    const customEmailsGroup = document.getElementById('customEmailsGroup');
    
    recipientType.addEventListener('change', function() {
        if (this.value === 'custom') {
            customEmailsGroup.style.display = 'block';
        } else {
            customEmailsGroup.style.display = 'none';
        }
    });
    
    // معاينة الرسالة
    const previewBtn = document.getElementById('previewBtn');
    const previewSection = document.getElementById('previewSection');
    const previewContent = document.getElementById('previewContent');
    
    previewBtn.addEventListener('click', function() {
        const subject = emailSubject.value;
        const message = emailMessage.value;
        
        if (!subject || !message) {
            alert('يرجى ملء موضوع الرسالة والمحتوى أولاً');
            return;
        }
        
        // عرض المعاينة مع بيانات تجريبية
        const previewData = {
            '{اسم_العميل}': 'أحمد محمد',
            '{اسم_الاشتراك}': 'Netflix Premium',
            '{المزود}': 'Netflix',
            '{السعر}': '$15.99',
            '{تاريخ_الانتهاء}': '2024-02-15',
            '{الأيام_المتبقية}': '7',
            '{رقم_الفاتورة}': 'INV-2024001',
            '{المبلغ}': '$15.99',
            '{تاريخ_الاستحقاق}': '2024-02-10',
            '{تاريخ_البداية}': '2024-01-15'
        };
        
        let previewSubject = subject;
        let previewMessage = message;
        
        // استبدال المتغيرات
        Object.keys(previewData).forEach(variable => {
            const value = previewData[variable];
            previewSubject = previewSubject.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), value);
            previewMessage = previewMessage.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), value);
        });
        
        previewContent.innerHTML = `
            <strong style="color: #00f5ff;">الموضوع:</strong> ${previewSubject}<br><br>
            <strong style="color: #00f5ff;">المحتوى:</strong><br>
            ${previewMessage.replace(/\n/g, '<br>')}
        `;
        
        previewSection.style.display = 'block';
        previewSection.scrollIntoView({ behavior: 'smooth' });
    });
    
    // تأثير الظهور التدريجي
    const sections = document.querySelectorAll('.templates-section, .email-form-container');
    sections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            section.style.transition = 'all 0.6s ease';
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, index * 200);
    });
});
</script>
{% endblock %}
