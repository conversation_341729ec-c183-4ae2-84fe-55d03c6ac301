#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات - النسخة النهائية بالتصميم المحدث
"""

print("🚀 بدء تشغيل نظام إدارة الاشتراكات بالتصميم المحدث...")

try:
    from flask import Flask, render_template_string, request, redirect, url_for, flash
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
    from werkzeug.security import generate_password_hash, check_password_hash
    from datetime import datetime, date
    import os
    
    print("✅ تم تحميل المكتبات بنجاح")
    
    # إعداد التطبيق
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'subscription-system-secret-key-2024-updated'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///final_subscriptions.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # إعداد قاعدة البيانات
    db = SQLAlchemy(app)
    
    # إعداد نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # نماذج قاعدة البيانات
    class User(UserMixin, db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        password_hash = db.Column(db.String(200), nullable=False)
        role = db.Column(db.String(20), default='user')
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        def set_password(self, password):
            self.password_hash = generate_password_hash(password)
        
        def check_password(self, password):
            return check_password_hash(self.password_hash, password)
        
        def is_admin(self):
            return self.role == 'admin'
    
    class Subscription(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        price = db.Column(db.Float, nullable=False)
        start_date = db.Column(db.Date, nullable=False)
        end_date = db.Column(db.Date, nullable=False)
        status = db.Column(db.String(20), default='active')
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        user = db.relationship('User', backref='subscriptions')
    
    print("✅ تم إعداد النماذج بنجاح")
    
    # تحميل القوالب من الملف الآخر
    with open('working_system.py', 'r', encoding='utf-8') as f:
        content = f.read()
        
        # استخراج قالب تسجيل الدخول
        start_login = content.find("LOGIN_TEMPLATE = '''") + len("LOGIN_TEMPLATE = '''")
        end_login = content.find("'''", start_login)
        LOGIN_TEMPLATE = content[start_login:end_login]
        
        # استخراج قالب لوحة التحكم
        start_dashboard = content.find("DASHBOARD_TEMPLATE = '''") + len("DASHBOARD_TEMPLATE = '''")
        end_dashboard = content.find("'''", start_dashboard)
        DASHBOARD_TEMPLATE = content[start_dashboard:end_dashboard]
    
    print("✅ تم تحميل القوالب بنجاح")
    
    # المسارات
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')
            
            user = User.query.filter_by(username=username).first()
            
            if user and user.check_password(password):
                login_user(user)
                flash('تم تسجيل الدخول بنجاح! مرحباً بك في النظام المحدث 🎉', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
        
        return render_template_string(LOGIN_TEMPLATE)
    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        return render_template_string(DASHBOARD_TEMPLATE)
    
    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('login'))
    
    # تهيئة قاعدة البيانات
    def init_db():
        with app.app_context():
            db.create_all()
            
            # إنشاء مستخدم مدير إذا لم يكن موجوداً
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام - AdenLink',
                    role='admin'
                )
                admin.set_password('123456')
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء المستخدم المدير")
            else:
                print("✅ المستخدم المدير موجود مسبقاً")
    
    print("✅ تم إعداد المسارات بنجاح")
    
    # تهيئة قاعدة البيانات
    print("🔄 تهيئة قاعدة البيانات...")
    init_db()
    
    print("=" * 60)
    print("🎉 نظام إدارة الاشتراكات جاهز للتشغيل!")
    print("=" * 60)
    print("🌐 معلومات الوصول:")
    print("   🔗 الرابط: http://localhost:5090")
    print("   👤 اسم المستخدم: admin")
    print("   🔑 كلمة المرور: 123456")
    print("   🎨 التصميم: محدث بنفس تصميم أمس")
    print("=" * 60)
    
    # تشغيل التطبيق
    app.run(debug=True, host='0.0.0.0', port=5090)
    
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("يرجى تثبيت المكتبات المطلوبة:")
    print("pip install flask flask-sqlalchemy flask-login")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل النظام: {e}")
    import traceback
    traceback.print_exc()

print("\n⏹️ تم إيقاف النظام")
input("اضغط Enter للخروج...")
