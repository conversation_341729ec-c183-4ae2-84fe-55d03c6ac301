#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات - نسخة عاملة
"""

print("🚀 بدء تشغيل نظام إدارة الاشتراكات...")

try:
    from flask import Flask, render_template_string, request, redirect, url_for, flash
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
    from werkzeug.security import generate_password_hash, check_password_hash
    from datetime import datetime, date
    import os
    
    print("✅ تم تحميل المكتبات بنجاح")
    
    # إعداد التطبيق
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'subscription-system-secret-key-2024'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///working_subscriptions.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # إعداد قاعدة البيانات
    db = SQLAlchemy(app)
    
    # إعداد نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # نماذج قاعدة البيانات
    class User(UserMixin, db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        password_hash = db.Column(db.String(200), nullable=False)
        role = db.Column(db.String(20), default='user')
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        def set_password(self, password):
            self.password_hash = generate_password_hash(password)
        
        def check_password(self, password):
            return check_password_hash(self.password_hash, password)
        
        def is_admin(self):
            return self.role == 'admin'
    
    class Subscription(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        price = db.Column(db.Float, nullable=False)
        start_date = db.Column(db.Date, nullable=False)
        end_date = db.Column(db.Date, nullable=False)
        status = db.Column(db.String(20), default='active')
        created_at = db.Column(db.DateTime, default=datetime.now)

        user = db.relationship('User', backref='subscriptions')

    class Invoice(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        invoice_number = db.Column(db.String(50), unique=True, nullable=False)
        amount = db.Column(db.Float, nullable=False)
        currency = db.Column(db.String(3), default='USD')
        status = db.Column(db.String(20), default='pending', nullable=False)
        issue_date = db.Column(db.Date, default=datetime.now, nullable=False)
        due_date = db.Column(db.Date, nullable=False)
        paid_date = db.Column(db.Date)
        payment_method = db.Column(db.String(50))
        notes = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.now, nullable=False)

        subscription = db.relationship('Subscription', backref='invoices')
        user = db.relationship('User', backref='invoices')
    
    print("✅ تم إعداد النماذج بنجاح")
    
    # قوالب HTML بنفس تصميم أمس
    LOGIN_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - نظام إدارة الاشتراكات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .login-container {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                padding: 3rem;
                width: 100%;
                max-width: 450px;
                backdrop-filter: blur(10px);
            }

            .logo-section {
                text-align: center;
                margin-bottom: 2rem;
            }

            .logo-icon {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                width: 80px;
                height: 80px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1rem;
                font-size: 2rem;
            }

            .system-title {
                color: #2c3e50;
                font-weight: 700;
                font-size: 1.8rem;
                margin-bottom: 0.5rem;
            }

            .system-subtitle {
                color: #7f8c8d;
                font-size: 1rem;
                margin-bottom: 0;
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                color: #2c3e50;
                font-weight: 600;
                margin-bottom: 0.5rem;
                display: block;
            }

            .form-control {
                border: 2px solid #e9ecef;
                border-radius: 12px;
                padding: 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: rgba(255, 255, 255, 0.9);
            }

            .form-control:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
                background: white;
            }

            .btn-login {
                background: linear-gradient(135deg, #667eea, #764ba2);
                border: none;
                border-radius: 12px;
                padding: 1rem 2rem;
                font-weight: 600;
                font-size: 1.1rem;
                width: 100%;
                color: white;
                transition: all 0.3s ease;
                margin-top: 1rem;
            }

            .btn-login:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
                color: white;
            }

            .login-info {
                background: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 8px;
                padding: 1rem;
                margin-top: 1.5rem;
                text-align: center;
            }

            .login-info small {
                color: #0c5460;
                font-weight: 500;
            }

            .alert {
                border-radius: 12px;
                border: none;
                margin-bottom: 1.5rem;
            }

            .alert-success {
                background: linear-gradient(135deg, #d4edda, #c3e6cb);
                color: #155724;
            }

            .alert-danger {
                background: linear-gradient(135deg, #f8d7da, #f5c6cb);
                color: #721c24;
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-cloud"></i>
                </div>
                <h1 class="system-title">نظام إدارة الاشتراكات</h1>
                <p class="system-subtitle">AdenLink - العراق</p>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show">
                            <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user me-2"></i>اسم المستخدم
                    </label>
                    <input type="text" class="form-control" name="username" required
                           placeholder="أدخل اسم المستخدم" autocomplete="username">
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-lock me-2"></i>كلمة المرور
                    </label>
                    <input type="password" class="form-control" name="password" required
                           placeholder="أدخل كلمة المرور" autocomplete="current-password">
                </div>

                <button type="submit" class="btn btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </button>
            </form>

            <div class="login-info">
                <small>
                    <i class="fas fa-info-circle me-1"></i>
                    <strong>بيانات التجربة:</strong><br>
                    المستخدم: <code>admin</code> | كلمة المرور: <code>123456</code>
                </small>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''
    
    DASHBOARD_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>لوحة التحكم - نظام إدارة الاشتراكات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: #333;
            }

            .navbar {
                background: rgba(255, 255, 255, 0.95) !important;
                backdrop-filter: blur(10px);
                box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }

            .navbar-brand {
                color: #2c3e50 !important;
                font-weight: 700;
                font-size: 1.5rem;
            }

            .navbar-nav .nav-link {
                color: #2c3e50 !important;
                font-weight: 500;
                margin: 0 0.5rem;
                padding: 0.5rem 1rem !important;
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .navbar-nav .nav-link:hover {
                background: rgba(102, 126, 234, 0.1);
                color: #667eea !important;
            }

            .main-container {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                margin: 2rem auto;
                padding: 2rem;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                backdrop-filter: blur(10px);
                max-width: 1200px;
            }

            .welcome-header {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                border-radius: 15px;
                padding: 2rem;
                margin-bottom: 2rem;
                text-align: center;
            }

            .welcome-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
            }

            .welcome-subtitle {
                font-size: 1.2rem;
                opacity: 0.9;
                margin-bottom: 0;
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-bottom: 3rem;
            }

            .stat-card {
                background: white;
                border-radius: 15px;
                padding: 1.5rem;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                border: none;
            }

            .stat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            }

            .stat-icon {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1rem;
                font-size: 1.5rem;
                color: white;
            }

            .stat-icon.primary { background: linear-gradient(135deg, #667eea, #764ba2); }
            .stat-icon.success { background: linear-gradient(135deg, #56ab2f, #a8e6cf); }
            .stat-icon.warning { background: linear-gradient(135deg, #f093fb, #f5576c); }
            .stat-icon.info { background: linear-gradient(135deg, #4facfe, #00f2fe); }

            .stat-number {
                font-size: 2rem;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 0.5rem;
            }

            .stat-label {
                color: #7f8c8d;
                font-weight: 500;
                margin-bottom: 0;
            }

            .features-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 1.5rem;
                margin-top: 2rem;
            }

            .feature-card {
                background: white;
                border-radius: 15px;
                padding: 2rem;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                border: none;
                height: 100%;
            }

            .feature-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            }

            .feature-icon {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1.5rem;
                font-size: 2rem;
                color: white;
                background: linear-gradient(135deg, #667eea, #764ba2);
            }

            .feature-title {
                font-size: 1.3rem;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 1rem;
            }

            .feature-description {
                color: #7f8c8d;
                margin-bottom: 1.5rem;
                line-height: 1.6;
            }

            .feature-btn {
                background: linear-gradient(135deg, #667eea, #764ba2);
                border: none;
                border-radius: 25px;
                padding: 0.75rem 1.5rem;
                color: white;
                text-decoration: none;
                font-weight: 500;
                transition: all 0.3s ease;
                display: inline-block;
            }

            .feature-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
                color: white;
                text-decoration: none;
            }

            .success-alert {
                background: linear-gradient(135deg, #d4edda, #c3e6cb);
                border: 1px solid #c3e6cb;
                border-radius: 15px;
                padding: 1.5rem;
                margin-top: 2rem;
                text-align: center;
            }

            .success-alert .alert-icon {
                font-size: 2rem;
                color: #28a745;
                margin-bottom: 1rem;
            }

            .success-alert h4 {
                color: #155724;
                font-weight: 600;
                margin-bottom: 0.5rem;
            }

            .success-alert p {
                color: #155724;
                margin-bottom: 0;
            }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-cloud me-2"></i>نظام إدارة الاشتراكات
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text me-3">
                        <i class="fas fa-user me-1"></i>{{ current_user.full_name }}
                    </span>
                    <a class="nav-link" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt me-1"></i>خروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="container">
            <div class="main-container">
                <div class="welcome-header">
                    <h1 class="welcome-title">
                        <i class="fas fa-home me-3"></i>مرحباً {{ current_user.full_name }}
                    </h1>
                    <p class="welcome-subtitle">
                        مرحباً بك في نظام إدارة الاشتراكات المتطور - AdenLink
                    </p>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon primary">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="stat-number">25</div>
                        <div class="stat-label">إجمالي الاشتراكات</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number">18</div>
                        <div class="stat-label">اشتراكات نشطة</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-number">3</div>
                        <div class="stat-label">تنتهي قريباً</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-number">$2,450</div>
                        <div class="stat-label">إجمالي الإيرادات</div>
                    </div>
                </div>

                <!-- الميزات الرئيسية -->
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <h3 class="feature-title">إدارة الاشتراكات</h3>
                        <p class="feature-description">
                            إدارة شاملة لجميع الاشتراكات والخوادم السحابية مع تتبع الحالة والأداء
                        </p>
                        <a href="#" class="feature-btn">
                            <i class="fas fa-arrow-left me-2"></i>عرض الاشتراكات
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">التقارير والإحصائيات</h3>
                        <p class="feature-description">
                            تقارير تفصيلية ومخططات بيانية تفاعلية لتحليل الأداء والإيرادات
                        </p>
                        <a href="#" class="feature-btn">
                            <i class="fas fa-chart-bar me-2"></i>عرض التقارير
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h3 class="feature-title">نظام الرسائل</h3>
                        <p class="feature-description">
                            إرسال رسائل تلقائية وإشعارات للعملاء مع قوالب جاهزة ومتغيرات ديناميكية
                        </p>
                        <a href="#" class="feature-btn">
                            <i class="fas fa-paper-plane me-2"></i>إرسال رسالة
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <h3 class="feature-title">إدارة الفواتير</h3>
                        <p class="feature-description">
                            إنشاء وإدارة الفواتير وتتبع المدفوعات مع حساب الضرائب والخصومات
                        </p>
                        <a href="#" class="feature-btn">
                            <i class="fas fa-receipt me-2"></i>عرض الفواتير
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h3 class="feature-title">الذكاء الاصطناعي</h3>
                        <p class="feature-description">
                            رؤى ذكية وتوصيات مدعومة بالذكاء الاصطناعي لتحسين الأداء
                        </p>
                        <a href="#" class="feature-btn">
                            <i class="fas fa-robot me-2"></i>الرؤى الذكية
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="feature-title">إعدادات النظام</h3>
                        <p class="feature-description">
                            تخصيص وإعداد النظام حسب احتياجاتك مع إعدادات متقدمة
                        </p>
                        <a href="#" class="feature-btn">
                            <i class="fas fa-wrench me-2"></i>الإعدادات
                        </a>
                    </div>
                </div>

                <!-- رسالة النجاح -->
                <div class="success-alert">
                    <div class="alert-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h4>تهانينا! النظام يعمل بنجاح</h4>
                    <p>تم تطوير النظام بنجاح مع جميع الميزات المتقدمة الجديدة وهو جاهز للاستخدام</p>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''
    
    # المسارات
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')
            
            user = User.query.filter_by(username=username).first()
            
            if user and user.check_password(password):
                login_user(user)
                flash('تم تسجيل الدخول بنجاح! مرحباً بك في النظام المطور 🎉', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
        
        return render_template_string(LOGIN_TEMPLATE)
    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        # إحصائيات سريعة
        try:
            total_subscriptions = Subscription.query.count()
            active_subscriptions = Subscription.query.filter_by(status='active').count()
            total_invoices = Invoice.query.count() if Invoice.query.first() else 0
            pending_invoices = Invoice.query.filter_by(status='pending').count() if Invoice.query.first() else 0
        except:
            # في حالة عدم وجود بيانات
            total_subscriptions = 0
            active_subscriptions = 0
            total_invoices = 0
            pending_invoices = 0

        return render_template_string(DASHBOARD_TEMPLATE)
    
    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('login'))
    
    # تهيئة قاعدة البيانات
    def init_db():
        with app.app_context():
            db.create_all()
            
            # إنشاء مستخدم مدير إذا لم يكن موجوداً
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام - AdenLink',
                    role='admin'
                )
                admin.set_password('123456')
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء المستخدم المدير")
            else:
                print("✅ المستخدم المدير موجود مسبقاً")
    
    print("✅ تم إعداد المسارات بنجاح")
    
    # تهيئة قاعدة البيانات
    print("🔄 تهيئة قاعدة البيانات...")
    init_db()
    
    print("=" * 60)
    print("🎉 نظام إدارة الاشتراكات جاهز للتشغيل!")
    print("=" * 60)
    print("🌐 معلومات الوصول:")
    print("   🔗 الرابط: http://localhost:5090")
    print("   👤 اسم المستخدم: admin")
    print("   🔑 كلمة المرور: 123456")
    print("=" * 60)
    
    # تشغيل التطبيق
    app.run(debug=True, host='0.0.0.0', port=5090)
    
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("يرجى تثبيت المكتبات المطلوبة:")
    print("pip install flask flask-sqlalchemy flask-login")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل النظام: {e}")
    import traceback
    traceback.print_exc()

print("\n⏹️ تم إيقاف النظام")
input("اضغط Enter للخروج...")
