@echo off
chcp 65001 >nul
cd /d "%~dp0"

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
echo ║                                                🚀 نظام إدارة الاشتراكات المبسط والنظيف                                                                        ║
echo ║                                                                  AdenLink - العراق                                                                              ║
echo ║                                                      النسخة المبسطة بدون لوحة التحكم المعقدة                                                                  ║
echo ╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🌟 تشغيل النظام المبسط والنظيف...
echo 📂 المجلد الحالي: %CD%
echo.
echo 🔍 فحص متطلبات النظام المبسط...
echo 🐍 إصدار Python:
python --version
echo.
echo 📦 فحص المكتبات المطلوبة...
python -c "import flask; print('✅ Flask متاح - الإصدار:', flask.__version__)" 2>nul || echo "❌ Flask غير متاح - يرجى التثبيت"
python -c "import flask_sqlalchemy; print('✅ Flask-SQLAlchemy متاح')" 2>nul || echo "❌ Flask-SQLAlchemy غير متاح"
python -c "import flask_login; print('✅ Flask-Login متاح')" 2>nul || echo "❌ Flask-Login غير متاح"
python -c "import werkzeug; print('✅ Werkzeug متاح')" 2>nul || echo "❌ Werkzeug غير متاح"
echo.
echo ⚡ تشغيل النظام المبسط والنظيف...
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
echo ║                                                        ✨ النظام المبسط والنظيف ✨                                                                          ║
echo ║                                                                                                                                                                  ║
echo ║  🌐 الرابط: http://localhost:5095                                                                                                                              ║
echo ║  👤 اسم المستخدم: admin                                                                                                                                        ║
echo ║  🔑 كلمة المرور: 123456                                                                                                                                        ║
echo ║                                                                                                                                                                  ║
echo ║  ✅ المميزات الجديدة:                                                                                                                                           ║
echo ║  🚫 تم حذف لوحة التحكم المعقدة نهائياً                                                                                                                         ║
echo ║  ✅ واجهة بسيطة ونظيفة وسهلة الاستخدام                                                                                                                        ║
echo ║  ✅ أداء سريع ومحسن بدون تعقيدات                                                                                                                              ║
echo ║  ✅ إحصائيات أساسية فقط (العملاء، الاشتراكات، الإيرادات)                                                                                                      ║
echo ║  ✅ تصميم مبسط وواضح مع Bootstrap                                                                                                                             ║
echo ║  ✅ قاعدة بيانات مبسطة مع البيانات الأساسية فقط                                                                                                               ║
echo ║  ✅ نظام تسجيل دخول بسيط وآمن                                                                                                                                 ║
echo ║  ✅ رسائل تأكيد واضحة ومفهومة                                                                                                                                 ║
echo ║                                                                                                                                                                  ║
echo ║  🎯 ما تم حذفه من النظام:                                                                                                                                       ║
echo ║  ❌ لوحة التحكم المتجاوبة المعقدة                                                                                                                              ║
echo ║  ❌ القائمة الجانبية المنزلقة                                                                                                                                  ║
echo ║  ❌ الإحصائيات المتقدمة والمعقدة                                                                                                                               ║
echo ║  ❌ التأثيرات البصرية المعقدة                                                                                                                                  ║
echo ║  ❌ الرسوم المتحركة الثقيلة                                                                                                                                    ║
echo ║  ❌ أشرطة التمرير المخصصة                                                                                                                                     ║
echo ║  ❌ التفاعلات المتقدمة                                                                                                                                         ║
echo ║  ❌ الجداول المعقدة في قاعدة البيانات                                                                                                                          ║
echo ║                                                                                                                                                                  ║
echo ║  🚀 النتيجة النهائية:                                                                                                                                           ║
echo ║  ✅ نظام بسيط وسريع وسهل الاستخدام                                                                                                                            ║
echo ║  ✅ يحتوي على الوظائف الأساسية فقط                                                                                                                            ║
echo ║  ✅ أداء ممتاز وسرعة عالية                                                                                                                                    ║
echo ║  ✅ واجهة نظيفة ومرتبة                                                                                                                                        ║
echo ║  ✅ سهولة في الصيانة والتطوير                                                                                                                                 ║
echo ║                                                                                                                                                                  ║
echo ║  🔥 النظام المبسط يعمل بشكل مثالي!                                                                                                                            ║
echo ║                                                                                                                                                                  ║
echo ╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
echo.

python clean_simple_system.py

echo.
echo ⏹️ تم إيقاف النظام المبسط والنظيف
echo.
echo 📝 ملاحظات مهمة:
echo • تم حذف لوحة التحكم المعقدة نهائياً كما طلبت
echo • النظام الآن بسيط وسريع وسهل الاستخدام
echo • يحتوي على الوظائف الأساسية فقط
echo • واجهة نظيفة ومرتبة مع Bootstrap
echo • أداء محسن وسرعة عالية
echo • قاعدة بيانات مبسطة
echo • نظام تسجيل دخول آمن وبسيط
echo.
echo 💡 للحصول على الدعم:
echo • راجع ملف SIMPLE_README.md
echo • تحقق من رسائل الخطأ في الطرفية
echo • تأكد من تثبيت جميع المكتبات المطلوبة
echo • تأكد من أن المنفذ 5095 متاح
echo.
echo 🔄 اضغط أي مفتاح للخروج...
pause >nul
