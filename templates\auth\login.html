{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام إدارة الاشتراكات{% endblock %}

{% block body_class %}login-page{% endblock %}

{% block extra_css %}
<style>
.login-page {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-container {
    max-width: 450px;
    width: 100%;
}

.login-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.login-logo {
    font-size: 4rem;
    color: #00f5ff;
    text-shadow: 0 0 30px #00f5ff;
    margin-bottom: 20px;
    animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoGlow {
    from {
        text-shadow: 0 0 20px #00f5ff, 0 0 30px #00f5ff, 0 0 40px #00f5ff;
    }
    to {
        text-shadow: 0 0 10px #00f5ff, 0 0 20px #00f5ff, 0 0 30px #00f5ff;
    }
}

.login-title {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
}

.login-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    color: white;
    padding: 15px 20px;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: #00f5ff;
    box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
    color: white;
    outline: none;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.input-group {
    position: relative;
}

.input-group-text {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-left: none;
    border-radius: 0 15px 15px 0;
    color: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
}

.input-group .form-control {
    border-left: none;
    border-radius: 15px 0 0 15px;
}

.password-toggle {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #00f5ff;
}

.form-check {
    margin: 20px 0;
}

.form-check-input {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
}

.form-check-input:checked {
    background: #00f5ff;
    border-color: #00f5ff;
}

.form-check-label {
    color: rgba(255, 255, 255, 0.8);
    margin-right: 10px;
}

.login-btn {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.login-btn:hover::before {
    left: 100%;
}

.demo-info {
    margin-top: 30px;
    padding: 20px;
    background: rgba(0, 245, 255, 0.1);
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 15px;
    text-align: center;
}

.demo-title {
    color: #00f5ff;
    font-weight: 600;
    margin-bottom: 10px;
}

.demo-credentials {
    color: rgba(255, 255, 255, 0.8);
    font-family: 'Courier New', monospace;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 8px;
    margin: 10px 0;
}

.back-link {
    text-align: center;
    margin-top: 30px;
}

.back-link a {
    color: #bf00ff;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.back-link a:hover {
    color: #ff0080;
    text-shadow: 0 0 10px #ff0080;
}

.error-message {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid rgba(255, 0, 0, 0.3);
    color: #ff6b6b;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
}

@media (max-width: 768px) {
    .login-card {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .login-logo {
        font-size: 3rem;
    }
    
    .login-title {
        font-size: 1.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="login-logo">
                <i class="fas fa-rocket"></i>
            </div>
            <h1 class="login-title">تسجيل الدخول</h1>
            <p class="login-subtitle">مرحباً بك في نظام إدارة الاشتراكات</p>
        </div>
        
        <form method="POST" novalidate>
            {{ form.hidden_tag() }}
            
            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="fas fa-user me-2"></i>
                    اسم المستخدم
                </label>
                <div class="input-group">
                    {{ form.username(class="form-control", placeholder="أدخل اسم المستخدم") }}
                    <span class="input-group-text">
                        <i class="fas fa-user"></i>
                    </span>
                </div>
                {% if form.username.errors %}
                    <div class="error-message">
                        {% for error in form.username.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock me-2"></i>
                    كلمة المرور
                </label>
                <div class="input-group">
                    {{ form.password(class="form-control", placeholder="أدخل كلمة المرور", id="password-input") }}
                    <span class="input-group-text">
                        <button type="button" class="password-toggle" data-target="#password-input">
                            <i class="fas fa-eye"></i>
                        </button>
                    </span>
                </div>
                {% if form.password.errors %}
                    <div class="error-message">
                        {% for error in form.password.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="form-check">
                {{ form.remember_me(class="form-check-input") }}
                {{ form.remember_me.label(class="form-check-label") }}
            </div>
            
            <button type="submit" class="login-btn crystal-btn ripple">
                <i class="fas fa-sign-in-alt me-2"></i>
                تسجيل الدخول
            </button>
        </form>
        
        <div class="demo-info">
            <div class="demo-title">
                <i class="fas fa-info-circle me-2"></i>
                بيانات تجريبية
            </div>
            <div class="demo-credentials">
                اسم المستخدم: <strong>admin</strong><br>
                كلمة المرور: <strong>123456</strong>
            </div>
            <small style="color: rgba(255, 255, 255, 0.6);">
                يمكنك استخدام هذه البيانات لتجربة النظام
            </small>
        </div>
        
        <div class="back-link">
            <a href="{{ url_for('welcome') }}">
                <i class="fas fa-arrow-right me-2"></i>
                العودة إلى الصفحة الرئيسية
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الظهور التدريجي
    const card = document.querySelector('.login-card');
    card.style.opacity = '0';
    card.style.transform = 'translateY(50px) scale(0.9)';
    
    setTimeout(() => {
        card.style.transition = 'all 0.6s ease';
        card.style.opacity = '1';
        card.style.transform = 'translateY(0) scale(1)';
    }, 200);
    
    // تبديل إظهار كلمة المرور
    document.addEventListener('click', function(e) {
        if (e.target.closest('.password-toggle')) {
            const button = e.target.closest('.password-toggle');
            const input = document.querySelector(button.dataset.target);
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
    });
    
    // تركيز تلقائي على حقل اسم المستخدم
    const usernameInput = document.getElementById('username');
    if (usernameInput) {
        usernameInput.focus();
    }
    
    // تأثير الكتابة على الحقول
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
});
</script>
{% endblock %}
