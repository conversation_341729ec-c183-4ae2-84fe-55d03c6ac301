#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

print("🚀 نظام إدارة الاشتراكات المتطور")
print("مطور بواسطة: المهندس محمد ياسر الجبوري ❤️")
print("="*60)

try:
    # تشغيل التطبيق المبسط
    print("📦 تحميل التطبيق...")
    exec(open('simple_app.py').read())
    
except FileNotFoundError:
    print("❌ ملف simple_app.py غير موجود")
    print("💡 تأكد من وجود الملف في نفس المجلد")
    
except Exception as e:
    print(f"❌ خطأ في التشغيل: {e}")
    print("\n🔧 محاولة تشغيل التطبيق الأصلي...")
    
    try:
        from app import app, db, User
        
        with app.app_context():
            db.create_all()
            
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='المدير العام',
                    role='admin'
                )
                admin_user.set_password('123456')
                db.session.add(admin_user)
                db.session.commit()
                print("✅ تم إنشاء المستخدم الافتراضي")
        
        print("\n🌐 معلومات الوصول:")
        print("   الرابط: http://localhost:5000")
        print("   المستخدم: admin")
        print("   كلمة المرور: 123456")
        print("="*60)
        print("🚀 جاري تشغيل الخادم...")
        
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except Exception as e2:
        print(f"❌ خطأ في التطبيق الأصلي أيضاً: {e2}")
        print("\n📋 تعليمات الإصلاح:")
        print("1. تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
        print("2. تأكد من وجود جميع الملفات")
        print("3. جرب تشغيل: python simple_app.py")
        
        input("\nاضغط Enter للخروج...")
