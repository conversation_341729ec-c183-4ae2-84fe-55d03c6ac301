🚀 نظام إدارة الاشتراكات المتطور
مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
===============================================

📋 تعليمات التشغيل:

1️⃣ تثبيت المتطلبات:
   pip install -r requirements.txt

2️⃣ تشغيل النظام:
   python final_run.py
   أو
   python simple_app.py
   أو
   python start.py

3️⃣ الوصول للنظام:
   🌐 الرابط: http://localhost:5000
   👤 اسم المستخدم: admin
   🔑 كلمة المرور: 123456

===============================================

🔧 في حالة وجود مشاكل:

❌ إذا لم يعمل النظام:
   - تأكد من تثبيت Python 3.7+
   - قم بتشغيل: pip install flask flask-sqlalchemy flask-login flask-wtf
   - جرب تشغيل: python simple_app.py

❌ إذا ظهرت صفحة خطأ:
   - تأكد من أن الخادم يعمل
   - تحقق من أن المنفذ 5000 غير مستخدم
   - أعد تشغيل النظام

❌ إذا لم تعمل صفحة تسجيل الدخول:
   - استخدم البيانات: admin / 123456
   - تأكد من أن قاعدة البيانات تم إنشاؤها
   - جرب حذف ملف .db وإعادة التشغيل

===============================================

📁 الملفات المهمة:

✅ simple_app.py - التطبيق المبسط (يعمل بشكل مؤكد)
✅ app.py - التطبيق الكامل مع جميع الميزات
✅ final_run.py - ملف التشغيل الذكي
✅ requirements.txt - قائمة المتطلبات

===============================================

🎯 الميزات المتاحة:

✅ صفحة ترحيب جميلة
✅ تسجيل دخول آمن
✅ لوحة تحكم تفاعلية
✅ تصميم عصري مع تأثيرات بصرية
✅ دعم كامل للغة العربية
✅ واجهة متجاوبة مع الأجهزة

===============================================

💡 نصائح:

🔹 استخدم simple_app.py للاختبار السريع
🔹 استخدم app.py للنسخة الكاملة
🔹 تأكد من تشغيل النظام كمدير إذا لزم الأمر
🔹 يمكنك تغيير المنفذ في الكود إذا كان 5000 مستخدماً

===============================================

📞 الدعم:
إذا واجهت أي مشاكل، تأكد من:
- تثبيت جميع المتطلبات
- استخدام Python 3.7 أو أحدث
- تشغيل النظام من المجلد الصحيح

===============================================

❤️ شكراً لاستخدام نظام إدارة الاشتراكات!
مطور بحب وإتقان في العراق 🇮🇶
