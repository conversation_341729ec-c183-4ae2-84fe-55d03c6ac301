@echo off
chcp 65001 >nul
cd /d "C:\Users\<USER>\Desktop\ammaradenlink"

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                   نظام إدارة الاشتراكات المطور                ║
echo ║                    التصميم الطولي الجديد                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🚀 بدء تشغيل النظام...
echo 📂 المجلد الحالي: %CD%
echo 🐍 إصدار Python:
python --version
echo.
echo ⚡ تشغيل النظام بالتصميم الطولي الجديد...
echo.

python fixed_system.py

echo.
echo ⏹️ تم إيقاف النظام
echo 📝 اضغط أي مفتاح للخروج...
pause >nul
