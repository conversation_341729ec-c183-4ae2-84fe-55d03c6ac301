@echo off
chcp 65001 >nul
title نظام إدارة الاشتراكات السحابية الشامل - محمد ياسر الجبوري

echo.
echo ================================================================================
echo 🚀 نظام إدارة الاشتراكات السحابية الشامل والمتطور
echo ================================================================================
echo 💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
echo 🏢 شركة AdenLink - العراق 🇮🇶
echo 📅 تاريخ التطوير: 2024
echo ================================================================================
echo.

echo 📋 التحقق من متطلبات النظام...
echo.

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
echo.

echo 📦 تثبيت المكتبات المطلوبة...
echo.

REM تثبيت المكتبات
pip install flask flask-sqlalchemy flask-login werkzeug >nul 2>&1

echo ✅ تم تثبيت جميع المكتبات بنجاح
echo.

echo 🔥 بدء تشغيل النظام الشامل...
echo.
echo ================================================================================
echo 🌐 معلومات الوصول:
echo ================================================================================
echo 🔗 الرابط: http://localhost:5000
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: 123456
echo ================================================================================
echo 🎮 الميزات الشاملة المتاحة:
echo ================================================================================
echo 📊 • لوحة تحكم متطورة مع إحصائيات شاملة
echo 📋 • إدارة الاشتراكات الكاملة (إضافة، تعديل، تجديد، حذف)
echo 👥 • إدارة المستخدمين الشاملة (للمديرين)
echo 🧾 • إدارة الفواتير المتقدمة مع حسابات تلقائية
echo ☁️ • إدارة مزودي الخدمة السحابية
echo 💳 • إدارة طرق الدفع المتعددة
echo 📊 • مخططات تفاعلية متطورة (4 أنواع)
echo 📋 • كشف حساب العملاء مع فلترة متقدمة
echo 📧 • نظام رسائل متطور مع قوالب ديناميكية
echo 🎨 • تصميم متجاوب ومتطور مع شريط جانبي
echo 🔐 • نظام صلاحيات متقدم وأمان عالي
echo ⚡ • أداء محسن وسرعة عالية
echo 📱 • دعم كامل للأجهزة المحمولة
echo ================================================================================
echo 📋 الصفحات المتاحة:
echo ================================================================================
echo 🏠 • لوحة التحكم: /dashboard
echo 📋 • إدارة الاشتراكات: /subscriptions
echo ➕ • إضافة اشتراك: /add_subscription
echo ✏️ • تعديل اشتراك: /edit_subscription/^<id^>
echo 🔄 • تجديد اشتراك: /renew_subscription/^<id^>
echo 🗑️ • حذف اشتراك: /delete_subscription/^<id^>
echo 👥 • إدارة المستخدمين: /users (للمديرين)
echo ➕ • إضافة مستخدم: /add_user (للمديرين)
echo ☁️ • مزودي الخدمة: /providers (للمديرين)
echo ➕ • إضافة مزود: /add_provider (للمديرين)
echo 🧾 • إدارة الفواتير: /invoices
echo ➕ • إضافة فاتورة: /add_invoice
echo 💳 • طرق الدفع: /payment_methods (للمديرين)
echo 📊 • مخططات الاشتراكات: /subscription_charts
echo 📋 • كشف حساب العملاء: /customer_statement
echo ================================================================================
echo 👥 المستخدمين التجريبيين:
echo ================================================================================
echo 🔹 admin / 123456 (مدير)
echo 🔹 user1 / 123456 (مستخدم)
echo 🔹 user2 / 123456 (مستخدم)
echo 🔹 user3 / 123456 (مستخدم - معطل)
echo ================================================================================
echo 📊 البيانات التجريبية:
echo ================================================================================
echo 🔹 4 مستخدمين مع معلومات كاملة
echo 🔹 6 مزودي خدمة سحابية
echo 🔹 5 طرق دفع متنوعة
echo 🔹 7 اشتراكات بحالات مختلفة
echo 🔹 6 فواتير بحالات متعددة
echo 🔹 3 قوالب رسائل ديناميكية
echo 🔹 3 رسائل تجريبية مرسلة
echo ================================================================================
echo 🚀 النظام الشامل جاهز للاستخدام!
echo ================================================================================
echo.

REM تشغيل النظام
python نظام_الاشتراكات_الشامل.py

echo.
echo ================================================================================
echo 🛑 تم إيقاف النظام
echo ================================================================================
echo 💡 لإعادة التشغيل، قم بتشغيل هذا الملف مرة أخرى
echo 📞 للدعم التقني: محمد ياسر الجبوري
echo 🏢 شركة AdenLink - العراق 🇮🇶
echo 🌐 الرابط: http://localhost:5000
echo ================================================================================
pause
