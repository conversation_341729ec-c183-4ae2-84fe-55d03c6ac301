@echo off
chcp 65001 >nul
cd /d "C:\Users\<USER>\Desktop\ammaradenlink"

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        نظام إدارة الاشتراكات المتقدم                        ║
echo ║                           AdenLink - العراق                                ║
echo ║                      النسخة المتكاملة مع جميع الميزات                       ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🚀 بدء تشغيل النظام المتقدم...
echo 📂 المجلد الحالي: %CD%
echo.
echo 🔍 فحص متطلبات النظام...
echo 🐍 إصدار Python:
python --version
echo.
echo 📦 فحص المكتبات المطلوبة...
python -c "import flask; print('✅ Flask متاح')" 2>nul || echo "❌ Flask غير متاح - يرجى التثبيت"
python -c "import flask_sqlalchemy; print('✅ Flask-SQLAlchemy متاح')" 2>nul || echo "❌ Flask-SQLAlchemy غير متاح"
python -c "import flask_login; print('✅ Flask-Login متاح')" 2>nul || echo "❌ Flask-Login غير متاح"
echo.
echo ⚡ تشغيل نظام إدارة الاشتراكات المتقدم...
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                              معلومات الوصول                               ║
echo ║                                                                              ║
echo ║  🌐 الرابط: http://localhost:5090                                          ║
echo ║  👤 اسم المستخدم: admin                                                    ║
echo ║  🔑 كلمة المرور: 123456                                                    ║
echo ║                                                                              ║
echo ║  🎨 الميزات المتقدمة:                                                       ║
echo ║  • قائمة جانبية تفاعلية مع جميع الوظائف                                   ║
echo ║  • إدارة شاملة للاشتراكات والعملاء                                        ║
echo ║  • نظام فواتير متطور مع تتبع المدفوعات                                    ║
echo ║  • تقارير وإحصائيات مفصلة                                                 ║
echo ║  • نظام إشعارات وتنبيهات                                                  ║
echo ║  • واجهة مستخدم عصرية ومتجاوبة                                           ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

python advanced_subscription_manager.py

echo.
echo ⏹️ تم إيقاف النظام
echo.
echo 📝 ملاحظات:
echo • إذا واجهت مشاكل في التشغيل، تأكد من تثبيت المكتبات المطلوبة
echo • استخدم الأمر: pip install flask flask-sqlalchemy flask-login
echo • للحصول على الدعم، راجع ملف README أو اتصل بالدعم الفني
echo.
echo 🔄 اضغط أي مفتاح للخروج...
pause >nul
