{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الاشتراكات{% endblock %}

{% block extra_css %}
<style>
.dashboard-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.dashboard-title {
    color: #00f5ff;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 0 20px #00f5ff;
    margin-bottom: 10px;
}

.dashboard-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.quick-actions {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.quick-actions h3 {
    color: #bf00ff;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #bf00ff;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    color: white;
}

.action-btn i {
    font-size: 1.5rem;
    margin-left: 15px;
    color: #00f5ff;
}

.recent-activity {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.activity-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.activity-card h4 {
    color: #00f5ff;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.activity-card h4 i {
    margin-left: 10px;
    font-size: 1.2rem;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    font-size: 1rem;
}

.activity-icon.subscription {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.activity-icon.invoice {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.activity-content {
    flex: 1;
}

.activity-title {
    color: white;
    font-weight: 600;
    margin-bottom: 5px;
}

.activity-meta {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

.alerts-section {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid rgba(255, 0, 0, 0.3);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
}

.alerts-section h4 {
    color: #ff6b6b;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.alert-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.alert-content {
    flex: 1;
}

.alert-title {
    color: white;
    font-weight: 600;
    margin-bottom: 5px;
}

.alert-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.alert-action {
    margin-right: 15px;
}

.chart-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-container h4 {
    color: #bf00ff;
    margin-bottom: 20px;
    text-align: center;
}

@media (max-width: 768px) {
    .dashboard-title {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .recent-activity {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <h1 class="dashboard-title">
            <i class="fas fa-tachometer-alt me-3"></i>
            لوحة التحكم
        </h1>
        <p class="dashboard-subtitle">
            مرحباً {{ current_user.full_name }}، إليك نظرة عامة على نشاطك
        </p>
    </div>
    
    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stats-card hologram-card">
            <div class="stats-icon">
                <i class="fas fa-subscription"></i>
            </div>
            <div class="stats-number" id="total-subscriptions">{{ stats.total_subscriptions }}</div>
            <div class="stats-label">إجمالي الاشتراكات</div>
        </div>
        
        <div class="stats-card hologram-card">
            <div class="stats-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number" id="active-subscriptions">{{ stats.active_subscriptions }}</div>
            <div class="stats-label">الاشتراكات النشطة</div>
        </div>
        
        <div class="stats-card hologram-card">
            <div class="stats-icon">
                <i class="fas fa-file-invoice"></i>
            </div>
            <div class="stats-number" id="total-invoices">{{ stats.total_invoices }}</div>
            <div class="stats-label">إجمالي الفواتير</div>
        </div>
        
        <div class="stats-card hologram-card">
            <div class="stats-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number" id="pending-invoices">{{ stats.pending_invoices }}</div>
            <div class="stats-label">الفواتير المعلقة</div>
        </div>
        
        <div class="stats-card hologram-card">
            <div class="stats-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stats-number" id="total-revenue">${{ "%.2f"|format(stats.total_revenue) }}</div>
            <div class="stats-label">إجمالي الإيرادات</div>
        </div>
        
        <div class="stats-card hologram-card">
            <div class="stats-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stats-number" id="upcoming-expiry">{{ stats.upcoming_expiry }}</div>
            <div class="stats-label">تنتهي قريباً</div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="quick-actions">
        <h3>
            <i class="fas fa-bolt me-2"></i>
            إجراءات سريعة
        </h3>
        <div class="action-buttons">
            <a href="/subscriptions/add" class="action-btn crystal-btn ripple">
                <i class="fas fa-plus"></i>
                <span>إضافة اشتراك جديد</span>
            </a>
            <a href="/invoices/add" class="action-btn crystal-btn ripple">
                <i class="fas fa-file-plus"></i>
                <span>إنشاء فاتورة</span>
            </a>
            <a href="/send_email" class="action-btn crystal-btn ripple">
                <i class="fas fa-envelope"></i>
                <span>إرسال رسالة</span>
            </a>
            <a href="/subscription_analytics" class="action-btn crystal-btn ripple">
                <i class="fas fa-chart-bar"></i>
                <span>عرض التحليلات</span>
            </a>
        </div>
    </div>
    
    <!-- Alerts Section -->
    {% if upcoming_expiry %}
    <div class="alerts-section">
        <h4>
            <i class="fas fa-exclamation-triangle me-2"></i>
            تنبيهات مهمة
        </h4>
        {% for subscription in upcoming_expiry %}
        <div class="alert-item">
            <div class="alert-content">
                <div class="alert-title">{{ subscription.name }}</div>
                <div class="alert-description">
                    ينتهي في {{ subscription.end_date.strftime('%Y-%m-%d') }}
                    ({{ subscription.days_until_expiry() }} أيام متبقية)
                </div>
            </div>
            <div class="alert-action">
                <a href="/subscriptions/{{ subscription.id }}" class="btn btn-sm btn-warning">
                    عرض التفاصيل
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <!-- Recent Activity -->
    <div class="recent-activity">
        <div class="activity-card">
            <h4>
                <i class="fas fa-subscription"></i>
                آخر الاشتراكات
            </h4>
            <div class="activity-list">
                {% for subscription in recent_subscriptions %}
                <div class="activity-item">
                    <div class="activity-icon subscription">
                        <i class="fas fa-subscription"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">{{ subscription.name }}</div>
                        <div class="activity-meta">
                            {{ subscription.provider }} - {{ subscription.created_at.strftime('%Y-%m-%d') }}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="activity-card">
            <h4>
                <i class="fas fa-file-invoice"></i>
                آخر الفواتير
            </h4>
            <div class="activity-list">
                {% for invoice in recent_invoices %}
                <div class="activity-item">
                    <div class="activity-icon invoice">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">{{ invoice.invoice_number }}</div>
                        <div class="activity-meta">
                            ${{ "%.2f"|format(invoice.total_amount) }} - {{ invoice.created_at.strftime('%Y-%m-%d') }}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Chart Container -->
    <div class="chart-container">
        <h4>
            <i class="fas fa-chart-line me-2"></i>
            نظرة عامة على الإيرادات
        </h4>
        <canvas id="revenueChart" width="400" height="200"></canvas>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الرسم البياني
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'الإيرادات الشهرية',
                data: [1200, 1900, 3000, 5000, 2000, 3000],
                borderColor: '#00f5ff',
                backgroundColor: 'rgba(0, 245, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    labels: {
                        color: 'white'
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            }
        }
    });
    
    // تأثير الظهور التدريجي للبطاقات
    const cards = document.querySelectorAll('.stats-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
