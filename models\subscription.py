#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج الاشتراك
"""

from datetime import datetime, timedelta
from flask_sqlalchemy import SQLAlchemy

# استيراد db من app.py سيتم لاحقاً
db = None

class Subscription(db.Model):
    """نموذج الاشتراك"""
    
    __tablename__ = 'subscriptions'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # معلومات أساسية
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    provider = db.Column(db.String(50), nullable=False)  # Netflix, Spotify, etc.
    category = db.Column(db.String(30), nullable=False)  # streaming, software, hosting, etc.
    
    # معلومات السيرفر والاتصال
    server_name = db.Column(db.String(100))
    server_ip = db.Column(db.String(45))  # IPv4 or IPv6
    api_key = db.Column(db.String(255))
    port = db.Column(db.Integer)
    username = db.Column(db.String(100))
    password = db.Column(db.String(255))
    
    # معلومات الاشتراك
    subscription_type = db.Column(db.String(20), nullable=False)  # monthly, yearly, lifetime
    price = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')
    
    # التواريخ
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    renewal_date = db.Column(db.Date)
    
    # الحالة
    status = db.Column(db.String(20), default='active', nullable=False)  # active, suspended, expired, cancelled
    auto_renewal = db.Column(db.Boolean, default=True)
    
    # معلومات إضافية
    notes = db.Column(db.Text)
    tags = db.Column(db.String(255))  # comma-separated tags
    priority = db.Column(db.String(10), default='medium')  # low, medium, high, critical
    
    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    invoices = db.relationship('Invoice', backref='subscription', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(Subscription, self).__init__(**kwargs)
        if self.renewal_date is None and self.subscription_type == 'monthly':
            self.renewal_date = self.end_date
    
    def is_active(self):
        """التحقق من حالة النشاط"""
        return self.status == 'active' and self.end_date >= datetime.now().date()
    
    def is_expired(self):
        """التحقق من انتهاء الصلاحية"""
        return self.end_date < datetime.now().date()
    
    def is_expiring_soon(self, days=7):
        """التحقق من انتهاء الصلاحية قريباً"""
        return (self.end_date - datetime.now().date()).days <= days
    
    def days_until_expiry(self):
        """عدد الأيام المتبقية"""
        return (self.end_date - datetime.now().date()).days
    
    def renew_subscription(self, months=1):
        """تجديد الاشتراك"""
        if self.subscription_type == 'monthly':
            self.end_date = self.end_date + timedelta(days=30 * months)
        elif self.subscription_type == 'yearly':
            self.end_date = self.end_date + timedelta(days=365 * months)
        
        self.renewal_date = self.end_date
        self.status = 'active'
        self.updated_at = datetime.utcnow()
    
    def suspend_subscription(self):
        """تعليق الاشتراك"""
        self.status = 'suspended'
        self.updated_at = datetime.utcnow()
    
    def cancel_subscription(self):
        """إلغاء الاشتراك"""
        self.status = 'cancelled'
        self.auto_renewal = False
        self.updated_at = datetime.utcnow()
    
    def get_monthly_cost(self):
        """الحصول على التكلفة الشهرية"""
        if self.subscription_type == 'monthly':
            return self.price
        elif self.subscription_type == 'yearly':
            return self.price / 12
        else:
            return 0
    
    def get_yearly_cost(self):
        """الحصول على التكلفة السنوية"""
        if self.subscription_type == 'monthly':
            return self.price * 12
        elif self.subscription_type == 'yearly':
            return self.price
        else:
            return 0
    
    def get_total_paid(self):
        """إجمالي المبلغ المدفوع"""
        return sum([inv.amount for inv in self.invoices.filter_by(status='paid').all()])
    
    def get_pending_amount(self):
        """المبلغ المعلق"""
        return sum([inv.amount for inv in self.invoices.filter_by(status='pending').all()])
    
    def get_tags_list(self):
        """الحصول على قائمة العلامات"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',')]
        return []
    
    def set_tags(self, tags_list):
        """تعيين العلامات"""
        if isinstance(tags_list, list):
            self.tags = ', '.join(tags_list)
        else:
            self.tags = tags_list
    
    def to_dict(self):
        """تحويل البيانات إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'provider': self.provider,
            'category': self.category,
            'server_name': self.server_name,
            'server_ip': self.server_ip,
            'port': self.port,
            'subscription_type': self.subscription_type,
            'price': self.price,
            'currency': self.currency,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'status': self.status,
            'auto_renewal': self.auto_renewal,
            'notes': self.notes,
            'tags': self.get_tags_list(),
            'priority': self.priority,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'days_until_expiry': self.days_until_expiry(),
            'is_active': self.is_active(),
            'is_expired': self.is_expired(),
            'monthly_cost': self.get_monthly_cost(),
            'yearly_cost': self.get_yearly_cost()
        }
    
    def __repr__(self):
        return f'<Subscription {self.name} - {self.provider}>'
