@echo off
chcp 65001 >nul
title النظام المتكامل والمتطور - AdenLink اليافعي

echo.
echo ================================================================
echo 🚀 النظام المتكامل والمتطور لإدارة الاشتراكات السحابية
echo ================================================================
echo 💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
echo 🏢 شركة AdenLink - اليافعي
echo ================================================================
echo.

echo 📋 التحقق من متطلبات النظام...
echo.

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
echo.

echo 📦 تثبيت المكتبات المطلوبة...
echo.

REM تثبيت المكتبات
pip install flask flask-sqlalchemy flask-login werkzeug >nul 2>&1

echo ✅ تم تثبيت جميع المكتبات بنجاح
echo.

echo 🔥 بدء تشغيل النظام المتكامل...
echo.
echo ================================================================
echo 🌐 معلومات الوصول:
echo ================================================================
echo 🔗 الرابط: http://localhost:5000
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: 123456
echo ================================================================
echo 🎮 الميزات المتاحة:
echo ================================================================
echo 📊 • مخططات تفاعلية متطورة مع Chart.js
echo 📋 • إدارة الاشتراكات الشاملة
echo 🧾 • إدارة الفواتير المتقدمة
echo 📧 • مركز التواصل مع نظام رسائل
echo 🎨 • واجهة متجاوبة مع تأثيرات متطورة
echo 🔐 • نظام أمان وصلاحيات متقدم
echo ================================================================
echo 🚀 النظام جاهز للاستخدام!
echo ================================================================
echo.

REM تشغيل النظام
python النظام_المتكامل_المبسط.py

echo.
echo ================================================================
echo 🛑 تم إيقاف النظام
echo ================================================================
pause
