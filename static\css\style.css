/* نظام إدارة الاشتراكات - التصميم العصري */
/* مطور بواسطة: المهندس محمد ياسر الجبوري */

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    --neon-blue: #00f5ff;
    --neon-purple: #bf00ff;
    --neon-pink: #ff0080;
    
    --dark-bg: #0a0a0a;
    --dark-card: #1a1a1a;
    --dark-text: #ffffff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: var(--dark-bg);
    color: var(--dark-text);
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}

/* Matrix Background Effect */
.matrix-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e);
    opacity: 0.8;
}

.matrix-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, var(--neon-blue) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, var(--neon-purple) 0%, transparent 50%);
    opacity: 0.1;
    animation: matrixMove 20s ease-in-out infinite;
}

@keyframes matrixMove {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* Glassmorphism Effects */
.glass-nav {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--glass-shadow);
    transition: all 0.3s ease;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
}

.glass-dropdown {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    box-shadow: var(--glass-shadow);
}

.glass-alert {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
}

.glass-footer {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--glass-border);
}

/* Neon Text Effects */
.neon-text {
    color: var(--neon-blue);
    text-shadow: 
        0 0 5px var(--neon-blue),
        0 0 10px var(--neon-blue),
        0 0 15px var(--neon-blue),
        0 0 20px var(--neon-blue);
    animation: neonGlow 2s ease-in-out infinite alternate;
}

.neon-text-small {
    color: var(--neon-purple);
    text-shadow: 
        0 0 3px var(--neon-purple),
        0 0 6px var(--neon-purple);
}

@keyframes neonGlow {
    from {
        text-shadow: 
            0 0 5px var(--neon-blue),
            0 0 10px var(--neon-blue),
            0 0 15px var(--neon-blue),
            0 0 20px var(--neon-blue);
    }
    to {
        text-shadow: 
            0 0 2px var(--neon-blue),
            0 0 5px var(--neon-blue),
            0 0 8px var(--neon-blue),
            0 0 12px var(--neon-blue);
    }
}

/* Hologram Effect */
.hologram-card {
    position: relative;
    background: linear-gradient(45deg, transparent, rgba(0, 245, 255, 0.1), transparent);
    border: 1px solid var(--neon-blue);
    border-radius: 20px;
    overflow: hidden;
}

.hologram-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(0, 245, 255, 0.1), transparent);
    animation: hologramScan 3s linear infinite;
}

@keyframes hologramScan {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Crystal Effect */
.crystal-btn {
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.crystal-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.crystal-btn:hover::before {
    left: 100%;
}

.crystal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Ripple Effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::after {
    width: 300px;
    height: 300px;
}

/* Main Content */
.main-content {
    margin-top: 80px;
    min-height: calc(100vh - 160px);
    padding: 20px 0;
}

/* Stats Cards */
.stats-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-gradient);
}

.stats-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.stats-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--neon-blue);
    text-shadow: 0 0 10px var(--neon-blue);
}

.stats-label {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 10px;
}

/* Forms */
.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    backdrop-filter: blur(10px);
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--neon-blue);
    box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
    color: white;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Buttons */
.btn-primary {
    background: var(--primary-gradient);
    border: none;
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: var(--success-gradient);
    border: none;
}

.btn-warning {
    background: var(--warning-gradient);
    border: none;
}

.btn-danger {
    background: var(--danger-gradient);
    border: none;
}

/* Tables */
.table-dark {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    overflow: hidden;
}

.table-dark th {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--neon-blue);
    font-weight: 600;
}

.table-dark td {
    border-color: rgba(255, 255, 255, 0.1);
    vertical-align: middle;
}

/* Responsive */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 20px;
    }
    
    .stats-icon {
        font-size: 2rem;
    }
    
    .stats-number {
        font-size: 1.8rem;
    }
    
    .main-content {
        margin-top: 70px;
        padding: 10px 0;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--neon-blue);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notification Badge */
.notification-badge {
    background: var(--danger-gradient);
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.75rem;
    position: absolute;
    top: -5px;
    right: -5px;
}
