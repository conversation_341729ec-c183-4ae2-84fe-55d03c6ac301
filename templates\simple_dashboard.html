<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .navbar-brand {
            color: #00f5ff !important;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
        }
        
        .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
        }
        
        .nav-link:hover {
            color: #00f5ff !important;
        }
        
        .main-content {
            padding: 30px 20px;
        }
        
        .welcome-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            margin-bottom: 30px;
        }
        
        .welcome-title {
            color: #00f5ff;
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 0 0 20px #00f5ff;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            color: #00f5ff;
            margin-bottom: 15px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 15px;
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .developer-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-top: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .developer-name {
            color: #bf00ff;
            font-weight: 600;
            text-shadow: 0 0 10px #bf00ff;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-rocket me-2"></i>
                نظام إدارة الاشتراكات
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#">
                    <i class="fas fa-user me-1"></i>
                    {{ current_user.full_name }}
                </a>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="container-fluid main-content">
        <!-- Welcome Card -->
        <div class="welcome-card">
            <h1 class="welcome-title">
                <i class="fas fa-tachometer-alt me-3"></i>
                مرحباً بك في لوحة التحكم
            </h1>
            <p style="font-size: 1.2rem; color: rgba(255, 255, 255, 0.8);">
                أهلاً وسهلاً {{ current_user.full_name }}، إليك نظرة عامة على نشاطك
            </p>
        </div>
        
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-subscription"></i>
                </div>
                <div class="stat-number">{{ stats.total_subscriptions if stats else 0 }}</div>
                <div class="stat-label">إجمالي الاشتراكات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number">{{ stats.active_subscriptions if stats else 0 }}</div>
                <div class="stat-label">الاشتراكات النشطة</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="stat-number">{{ stats.total_invoices if stats else 0 }}</div>
                <div class="stat-label">إجمالي الفواتير</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-number">${{ "%.2f"|format(stats.total_revenue) if stats else "0.00" }}</div>
                <div class="stat-label">إجمالي الإيرادات</div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="/subscriptions" class="action-btn">
                <i class="fas fa-subscription"></i>
                <span>إدارة الاشتراكات</span>
            </a>
            
            <a href="/subscriptions/add" class="action-btn">
                <i class="fas fa-plus"></i>
                <span>إضافة اشتراك جديد</span>
            </a>
            
            <a href="/subscription_analytics" class="action-btn">
                <i class="fas fa-chart-bar"></i>
                <span>التحليلات والتقارير</span>
            </a>
            
            <a href="/send_email" class="action-btn">
                <i class="fas fa-envelope"></i>
                <span>مركز التواصل</span>
            </a>
        </div>
        
        <!-- Developer Info -->
        <div class="developer-info">
            <p class="mb-2">
                <i class="fas fa-code text-primary me-2"></i>
                مطور بواسطة: <span class="developer-name">المهندس محمد ياسر الجبوري</span>
            </p>
            <p class="mb-0">
                <i class="fas fa-heart text-danger me-2"></i>
                صُنع بحب وإتقان لخدمة المجتمع التقني العربي
            </p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
