{% extends "templates/subscriptions/add.html" %}

{% block title %}تعديل {{ subscription.name }} - نظام إدارة الاشتراكات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="add-subscription-header">
        <h1 class="add-subscription-title">
            <i class="fas fa-edit me-3"></i>
            تعديل الاشتراك
        </h1>
        <p style="color: rgba(255, 255, 255, 0.7); font-size: 1.1rem;">
            تعديل بيانات اشتراك: <strong style="color: #00f5ff;">{{ subscription.name }}</strong>
        </p>
    </div>
    
    <!-- Form -->
    <div class="form-container">
        <form method="POST" novalidate>
            {{ form.hidden_tag() }}
            
            <!-- Basic Information -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    المعلومات الأساسية
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control", placeholder="مثال: Netflix Premium") }}
                        {% if form.name.errors %}
                            <div class="error-message">
                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.provider.label(class="form-label") }}
                        {{ form.provider(class="form-control", placeholder="مثال: Netflix") }}
                        {% if form.provider.errors %}
                            <div class="error-message">
                                {% for error in form.provider.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.category.label(class="form-label") }}
                        {{ form.category(class="form-select") }}
                        {% if form.category.errors %}
                            <div class="error-message">
                                {% for error in form.category.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.priority.label(class="form-label") }}
                        {{ form.priority(class="form-select") }}
                        {% if form.priority.errors %}
                            <div class="error-message">
                                {% for error in form.priority.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-group">
                    {{ form.description.label(class="form-label") }}
                    {{ form.description(class="form-control", rows="3", placeholder="وصف مختصر للاشتراك...") }}
                    {% if form.description.errors %}
                        <div class="error-message">
                            {% for error in form.description.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Server Information -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-server"></i>
                    معلومات السيرفر (اختيارية)
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.server_name.label(class="form-label") }}
                        {{ form.server_name(class="form-control", placeholder="اسم السيرفر") }}
                    </div>
                    
                    <div class="form-group">
                        {{ form.server_ip.label(class="form-label") }}
                        {{ form.server_ip(class="form-control", placeholder="***********") }}
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.port.label(class="form-label") }}
                        {{ form.port(class="form-control", placeholder="8080") }}
                    </div>
                    
                    <div class="form-group">
                        {{ form.api_key.label(class="form-label") }}
                        {{ form.api_key(class="form-control", placeholder="مفتاح API") }}
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.username.label(class="form-label") }}
                        {{ form.username(class="form-control", placeholder="اسم المستخدم") }}
                    </div>
                    
                    <div class="form-group">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control", placeholder="كلمة المرور") }}
                    </div>
                </div>
            </div>
            
            <!-- Subscription Details -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-credit-card"></i>
                    تفاصيل الاشتراك
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.subscription_type.label(class="form-label") }}
                        {{ form.subscription_type(class="form-select") }}
                        {% if form.subscription_type.errors %}
                            <div class="error-message">
                                {% for error in form.subscription_type.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.price.label(class="form-label") }}
                        {{ form.price(class="form-control", placeholder="0.00", step="0.01") }}
                        {% if form.price.errors %}
                            <div class="error-message">
                                {% for error in form.price.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.currency.label(class="form-label") }}
                        {{ form.currency(class="form-select") }}
                        {% if form.currency.errors %}
                            <div class="error-message">
                                {% for error in form.currency.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.start_date.label(class="form-label") }}
                        {{ form.start_date(class="form-control") }}
                        {% if form.start_date.errors %}
                            <div class="error-message">
                                {% for error in form.start_date.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.end_date.label(class="form-label") }}
                        {{ form.end_date(class="form-control") }}
                        {% if form.end_date.errors %}
                            <div class="error-message">
                                {% for error in form.end_date.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-check">
                    {{ form.auto_renewal(class="form-check-input") }}
                    {{ form.auto_renewal.label(class="form-check-label") }}
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-tags"></i>
                    معلومات إضافية
                </h3>
                
                <div class="form-group">
                    {{ form.tags.label(class="form-label") }}
                    {{ form.tags(class="form-control", placeholder="علامة1, علامة2, علامة3") }}
                    <div class="help-text">أدخل العلامات مفصولة بفواصل</div>
                </div>
                
                <div class="form-group">
                    {{ form.notes.label(class="form-label") }}
                    {{ form.notes(class="form-control", rows="4", placeholder="ملاحظات إضافية...") }}
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="form-actions">
                <button type="submit" class="btn-submit crystal-btn ripple">
                    <i class="fas fa-save me-2"></i>
                    حفظ التغييرات
                </button>
                
                <a href="{{ url_for('view_subscription', id=subscription.id) }}" class="btn-cancel crystal-btn">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
