#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام إدارة الاشتراكات البسيط والفعال
مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
شركة AdenLink - العراق 🇮🇶
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta

print("🚀 بدء تشغيل نظام إدارة الاشتراكات البسيط...")

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'simple-subscription-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///simple_subscriptions.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# نماذج قاعدة البيانات البسيطة

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user', nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

class CloudProvider(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    website = db.Column(db.String(255))
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='active')
    
    user = db.relationship('User', backref='subscriptions')
    provider = db.relationship('CloudProvider', backref='subscriptions')

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    total_amount = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='pending')
    
    user = db.relationship('User', backref='invoices')
    subscription = db.relationship('Subscription', backref='invoices')

# دوال مساعدة
def get_dashboard_stats():
    try:
        if current_user.is_admin():
            total_subscriptions = Subscription.query.count()
            active_subscriptions = Subscription.query.filter_by(status='active').count()
            total_users = User.query.count()
            total_invoices = Invoice.query.count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(status='paid').scalar() or 0
        else:
            total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
            active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
            total_users = 1
            total_invoices = Invoice.query.filter_by(user_id=current_user.id).count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(user_id=current_user.id, status='paid').scalar() or 0

        return {
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'expired_subscriptions': total_subscriptions - active_subscriptions,
            'total_users': total_users,
            'total_invoices': total_invoices,
            'total_revenue': total_revenue
        }
    except:
        return {
            'total_subscriptions': 0,
            'active_subscriptions': 0,
            'expired_subscriptions': 0,
            'total_users': 0,
            'total_invoices': 0,
            'total_revenue': 0
        }

def generate_invoice_number():
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return f"INV-{timestamp}"

# قوالب HTML

LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }
        .logo-section {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo-icon {
            font-size: 4rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .system-title {
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }
        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            padding: 1rem;
        }
        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            color: white;
            box-shadow: none;
        }
        .btn-login {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            width: 100%;
        }
        .demo-credentials {
            background: rgba(0, 212, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: center;
        }
        .success-message {
            background: rgba(0, 255, 136, 0.2);
            border: 2px solid rgba(0, 255, 136, 0.5);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
            font-weight: 600;
            color: #00ff88;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <div class="logo-icon">
                <i class="fas fa-cloud"></i>
            </div>
            <h1 class="system-title">نظام إدارة الاشتراكات</h1>
            <p>مطور بواسطة: المهندس محمد ياسر الجبوري ❤️</p>
            <p>شركة AdenLink - العراق 🇮🇶</p>
        </div>
        
        <div class="success-message">
            ✅ النظام يعمل بنجاح!
        </div>
        
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <div class="mb-3">
                <input type="text" class="form-control" name="username" placeholder="اسم المستخدم" required>
            </div>
            <div class="mb-3">
                <input type="password" class="form-control" name="password" placeholder="كلمة المرور" required>
            </div>
            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
            </button>
        </form>
        
        <div class="demo-credentials">
            <div><strong>بيانات التجربة:</strong></div>
            <div>اسم المستخدم: <strong>admin</strong></div>
            <div>كلمة المرور: <strong>123456</strong></div>
            <div class="mt-2">
                <small>🔗 http://localhost:5000</small>
            </div>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 2rem;
        }
        .header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        .nav-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
        }
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
            color: white;
            text-decoration: none;
        }
        .nav-icon {
            font-size: 3rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .btn-logout {
            background: linear-gradient(135deg, #ff006e, #b537f2);
            border: none;
            border-radius: 12px;
            padding: 0.8rem 1.5rem;
            color: white;
            text-decoration: none;
        }
        .chart-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="page-title">
            <i class="fas fa-cloud me-3"></i>
            نظام إدارة الاشتراكات السحابية
        </h1>
        <p class="lead">مرحباً {{ current_user.full_name }} في النظام المتطور</p>
        <div class="d-flex justify-content-center gap-3 mt-3">
            <span class="badge bg-success fs-6">✅ النظام يعمل بنجاح</span>
            <a href="{{ url_for('logout') }}" class="btn-logout">
                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
            </a>
        </div>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_subscriptions }}</div>
            <div>إجمالي الاشتراكات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.active_subscriptions }}</div>
            <div>الاشتراكات النشطة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_invoices }}</div>
            <div>إجمالي الفواتير</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${{ "%.0f"|format(stats.total_revenue) }}</div>
            <div>إجمالي الإيرادات</div>
        </div>
        {% if current_user.is_admin() %}
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_users }}</div>
            <div>إجمالي المستخدمين</div>
        </div>
        {% endif %}
    </div>

    <div class="chart-section">
        <h3 class="text-center">مخطط الاشتراكات التفاعلي</h3>
        <div class="chart-container">
            <canvas id="subscriptionChart"></canvas>
        </div>
    </div>

    <div class="nav-grid">
        <a href="{{ url_for('subscriptions') }}" class="nav-card">
            <div class="nav-icon"><i class="fas fa-server"></i></div>
            <h4>إدارة الاشتراكات</h4>
            <p>عرض وإدارة جميع الاشتراكات السحابية</p>
        </a>

        <a href="{{ url_for('invoices') }}" class="nav-card">
            <div class="nav-icon"><i class="fas fa-file-invoice"></i></div>
            <h4>إدارة الفواتير</h4>
            <p>إنشاء وإدارة الفواتير</p>
        </a>

        {% if current_user.is_admin() %}
        <a href="{{ url_for('users') }}" class="nav-card">
            <div class="nav-icon"><i class="fas fa-users"></i></div>
            <h4>إدارة المستخدمين</h4>
            <p>إضافة وإدارة المستخدمين</p>
        </a>

        <a href="{{ url_for('providers') }}" class="nav-card">
            <div class="nav-icon"><i class="fas fa-cloud"></i></div>
            <h4>مزودي الخدمة</h4>
            <p>إدارة مزودي الخدمة السحابية</p>
        </a>
        {% endif %}

        <div class="nav-card">
            <div class="nav-icon"><i class="fas fa-chart-pie"></i></div>
            <h4>التقارير والإحصائيات</h4>
            <p>تحليلات شاملة ومتطورة</p>
        </div>

        <div class="nav-card">
            <div class="nav-icon"><i class="fas fa-cog"></i></div>
            <h4>الإعدادات</h4>
            <p>إعدادات النظام والتخصيص</p>
        </div>
    </div>

    <script>
        // مخطط الاشتراكات
        const ctx = document.getElementById('subscriptionChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'منتهي'],
                datasets: [{
                    data: [{{ stats.active_subscriptions }}, {{ stats.expired_subscriptions }}],
                    backgroundColor: [
                        'rgba(0, 212, 255, 0.8)',
                        'rgba(255, 0, 110, 0.8)'
                    ],
                    borderColor: ['#00d4ff', '#ff006e'],
                    borderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: 'white',
                            padding: 20,
                            font: { size: 16 }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
'''

# المسارات الأساسية

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            login_user(user)
            flash('🎉 مرحباً بك في نظام إدارة الاشتراكات!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('✅ تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    stats = get_dashboard_stats()
    return render_template_string(DASHBOARD_TEMPLATE, stats=stats)

# مسارات بسيطة للصفحات الأخرى

@app.route('/subscriptions')
@login_required
def subscriptions():
    if current_user.is_admin():
        subscriptions = Subscription.query.all()
    else:
        subscriptions = Subscription.query.filter_by(user_id=current_user.id).all()

    return f'''
    <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
        <div style="text-align: center; margin-bottom: 2rem;">
            <h1 style="color: #00d4ff; font-size: 2.5rem; margin-bottom: 1rem;">📋 إدارة الاشتراكات</h1>
            <div style="background: rgba(0, 255, 136, 0.2); border: 2px solid rgba(0, 255, 136, 0.5); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #00ff88; font-weight: 700;">
                ✅ النظام يعمل بنجاح
            </div>
            <br>
            <a href="{url_for('dashboard')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
        </div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
            {"".join([f'''
            <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem;">
                <h5 style="color: #00d4ff;">{sub.name}</h5>
                <p><strong>المزود:</strong> {sub.provider.name if sub.provider else "غير محدد"}</p>
                <p><strong>السعر:</strong> {sub.price} {sub.currency}</p>
                <p><strong>الحالة:</strong> <span style="background: {"#28a745" if sub.status == "active" else "#dc3545"}; padding: 0.3rem 0.8rem; border-radius: 10px; font-size: 0.8rem;">{sub.status}</span></p>
                <p><strong>تاريخ الانتهاء:</strong> {sub.end_date}</p>
            </div>
            ''' for sub in subscriptions])}
        </div>
        {f'<div style="text-align: center; margin-top: 3rem;"><h3>لا توجد اشتراكات حالياً</h3><p>ابدأ بإضافة أول اشتراك لك</p></div>' if not subscriptions else ''}
    </div>
    '''

@app.route('/invoices')
@login_required
def invoices():
    if current_user.is_admin():
        invoices = Invoice.query.all()
    else:
        invoices = Invoice.query.filter_by(user_id=current_user.id).all()

    return f'''
    <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
        <div style="text-align: center; margin-bottom: 2rem;">
            <h1 style="color: #00d4ff; font-size: 2.5rem; margin-bottom: 1rem;">🧾 إدارة الفواتير</h1>
            <div style="background: rgba(0, 255, 136, 0.2); border: 2px solid rgba(0, 255, 136, 0.5); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #00ff88; font-weight: 700;">
                ✅ النظام يعمل بنجاح
            </div>
            <br>
            <a href="{url_for('dashboard')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
        </div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem;">
            {"".join([f'''
            <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem;">
                <h5 style="color: #00d4ff;">فاتورة رقم: {inv.invoice_number}</h5>
                <p><strong>الاشتراك:</strong> {inv.subscription.name}</p>
                <p><strong>المبلغ:</strong> {inv.total_amount} {inv.currency}</p>
                <p><strong>تاريخ الإصدار:</strong> {inv.issue_date}</p>
                <p><strong>الحالة:</strong> <span style="background: {"#28a745" if inv.status == "paid" else "#ffc107" if inv.status == "pending" else "#dc3545"}; padding: 0.3rem 0.8rem; border-radius: 10px; font-size: 0.8rem;">{inv.status}</span></p>
            </div>
            ''' for inv in invoices])}
        </div>
        {f'<div style="text-align: center; margin-top: 3rem;"><h3>لا توجد فواتير حالياً</h3><p>ستظهر الفواتير هنا عند إنشائها</p></div>' if not invoices else ''}
    </div>
    '''

@app.route('/users')
@login_required
def users():
    if not current_user.is_admin():
        return redirect(url_for('dashboard'))

    users = User.query.all()

    return f'''
    <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
        <div style="text-align: center; margin-bottom: 2rem;">
            <h1 style="color: #00d4ff; font-size: 2.5rem; margin-bottom: 1rem;">👥 إدارة المستخدمين</h1>
            <div style="background: rgba(0, 255, 136, 0.2); border: 2px solid rgba(0, 255, 136, 0.5); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #00ff88; font-weight: 700;">
                ✅ النظام يعمل بنجاح
            </div>
            <br>
            <a href="{url_for('dashboard')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
        </div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
            {"".join([f'''
            <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem;">
                <h5 style="color: #00d4ff;">{user.full_name}</h5>
                <p><strong>اسم المستخدم:</strong> {user.username}</p>
                <p><strong>البريد:</strong> {user.email}</p>
                <p><strong>الدور:</strong> <span style="background: {"#ff006e" if user.role == "admin" else "#28a745"}; padding: 0.3rem 0.8rem; border-radius: 10px; font-size: 0.8rem;">{"مدير" if user.role == "admin" else "مستخدم"}</span></p>
                <p><strong>الحالة:</strong> <span style="background: {"#28a745" if user.is_active else "#dc3545"}; padding: 0.3rem 0.8rem; border-radius: 10px; font-size: 0.8rem;">{"نشط" if user.is_active else "غير نشط"}</span></p>
            </div>
            ''' for user in users])}
        </div>
    </div>
    '''

@app.route('/providers')
@login_required
def providers():
    if not current_user.is_admin():
        return redirect(url_for('dashboard'))

    providers = CloudProvider.query.all()

    return f'''
    <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
        <div style="text-align: center; margin-bottom: 2rem;">
            <h1 style="color: #00d4ff; font-size: 2.5rem; margin-bottom: 1rem;">☁️ مزودي الخدمة</h1>
            <div style="background: rgba(0, 255, 136, 0.2); border: 2px solid rgba(0, 255, 136, 0.5); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #00ff88; font-weight: 700;">
                ✅ النظام يعمل بنجاح
            </div>
            <br>
            <a href="{url_for('dashboard')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
        </div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
            {"".join([f'''
            <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem;">
                <h5 style="color: #00d4ff;">{provider.name}</h5>
                <p><strong>الرمز:</strong> {provider.slug}</p>
                <p><strong>الموقع:</strong> <a href="{provider.website}" target="_blank" style="color: #00d4ff;">{provider.website}</a></p>
                <p><strong>الوصف:</strong> {provider.description or "لا يوجد وصف"}</p>
                <p><strong>الحالة:</strong> <span style="background: {"#28a745" if provider.is_active else "#dc3545"}; padding: 0.3rem 0.8rem; border-radius: 10px; font-size: 0.8rem;">{"نشط" if provider.is_active else "غير نشط"}</span></p>
            </div>
            ''' for provider in providers])}
        </div>
    </div>
    '''

# تهيئة قاعدة البيانات البسيطة
def init_simple_database():
    with app.app_context():
        try:
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات")

            if User.query.count() > 0:
                print("✅ البيانات موجودة مسبقاً")
                return

            # إنشاء المستخدمين
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام - محمد ياسر الجبوري',
                role='admin',
                is_active=True
            )
            admin.set_password('123456')
            db.session.add(admin)

            user1 = User(
                username='user1',
                email='<EMAIL>',
                full_name='أحمد محمد علي',
                role='user',
                is_active=True
            )
            user1.set_password('123456')
            db.session.add(user1)

            user2 = User(
                username='user2',
                email='<EMAIL>',
                full_name='فاطمة أحمد حسن',
                role='user',
                is_active=True
            )
            user2.set_password('123456')
            db.session.add(user2)

            db.session.flush()
            print("✅ تم إنشاء المستخدمين")

            # إنشاء مزودي الخدمة
            providers_data = [
                ('Amazon Web Services', 'aws', 'https://aws.amazon.com', 'خدمات الحوسبة السحابية من أمازون'),
                ('Microsoft Azure', 'azure', 'https://azure.microsoft.com', 'منصة الحوسبة السحابية من مايكروسوفت'),
                ('Google Cloud Platform', 'gcp', 'https://cloud.google.com', 'خدمات الحوسبة السحابية من جوجل'),
                ('DigitalOcean', 'digitalocean', 'https://digitalocean.com', 'خدمات الخوادم الافتراضية'),
                ('Vultr', 'vultr', 'https://vultr.com', 'خدمات الخوادر السحابية عالية الأداء')
            ]

            for name, slug, website, description in providers_data:
                provider = CloudProvider(
                    name=name,
                    slug=slug,
                    website=website,
                    description=description,
                    is_active=True
                )
                db.session.add(provider)

            db.session.flush()
            print("✅ تم إنشاء مزودي الخدمة")

            # إنشاء الاشتراكات التجريبية
            subscriptions_data = [
                (2, 1, 'خادم ويب أساسي', 'خادم ويب للموقع الشخصي', 25.99, 'USD', 'active'),
                (2, 2, 'قاعدة بيانات متقدمة', 'قاعدة بيانات للتطبيق الرئيسي', 89.99, 'USD', 'active'),
                (3, 3, 'تخزين سحابي', 'مساحة تخزين للملفات', 15.50, 'USD', 'active'),
                (3, 4, 'خادم تطوير', 'خادم للتطوير والاختبار', 12.00, 'USD', 'expired'),
                (1, 5, 'خادم الإنتاج', 'خادم الإنتاج الرئيسي', 160.00, 'USD', 'active')
            ]

            for user_id, provider_id, name, description, price, currency, status in subscriptions_data:
                start_date = date.today() - timedelta(days=30)
                end_date = date.today() + timedelta(days=30) if status == 'active' else date.today() - timedelta(days=5)

                subscription = Subscription(
                    user_id=user_id,
                    provider_id=provider_id,
                    name=name,
                    description=description,
                    price=price,
                    currency=currency,
                    start_date=start_date,
                    end_date=end_date,
                    status=status
                )
                db.session.add(subscription)

            db.session.flush()
            print("✅ تم إنشاء الاشتراكات التجريبية")

            # إنشاء الفواتير التجريبية
            invoices_data = [
                (2, 1, 28.59, 'USD', 'paid'),
                (2, 2, 98.99, 'USD', 'pending'),
                (3, 3, 16.74, 'USD', 'pending'),
                (1, 5, 176.00, 'USD', 'paid')
            ]

            for user_id, subscription_id, amount, currency, status in invoices_data:
                invoice = Invoice(
                    invoice_number=generate_invoice_number(),
                    user_id=user_id,
                    subscription_id=subscription_id,
                    total_amount=amount,
                    currency=currency,
                    issue_date=date.today() - timedelta(days=15),
                    due_date=date.today() + timedelta(days=15),
                    status=status
                )
                db.session.add(invoice)

            db.session.flush()
            print("✅ تم إنشاء الفواتير التجريبية")

            db.session.commit()
            print("✅ تم حفظ جميع البيانات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            db.session.rollback()

# تشغيل النظام
if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام إدارة الاشتراكات البسيط...")
    print("=" * 70)
    print("🎯 نظام إدارة الاشتراكات السحابية البسيط")
    print("💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️")
    print("🏢 شركة AdenLink - العراق 🇮🇶")
    print("=" * 70)

    try:
        print("📊 تهيئة قاعدة البيانات...")
        init_simple_database()

        print("\n✅ تم تهيئة النظام بنجاح!")
        print("=" * 70)
        print("🌐 معلومات الوصول:")
        print("🔗 الرابط: http://localhost:5000")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: 123456")
        print("=" * 70)
        print("🎮 الميزات المتاحة:")
        print("📊 • لوحة تحكم متطورة مع مخطط تفاعلي")
        print("📋 • إدارة الاشتراكات")
        print("🧾 • إدارة الفواتير")
        print("👥 • إدارة المستخدمين (للمديرين)")
        print("☁️ • إدارة مزودي الخدمة (للمديرين)")
        print("🎨 • تصميم متجاوب ومتطور")
        print("🔐 • نظام تسجيل دخول آمن")
        print("⚡ • أداء سريع ومحسن")
        print("=" * 70)
        print("🚀 النظام جاهز للاستخدام!")
        print("=" * 70)

        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True
        )

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("🔧 يرجى التحقق من:")
        print("   • تثبيت جميع المكتبات المطلوبة")
        print("   • عدم استخدام البورت 5000 من تطبيق آخر")
        print("   • صلاحيات الكتابة في مجلد التطبيق")

print("✅ تم إعداد النظام البسيط بالكامل!")
