#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام إدارة الاشتراكات المتكامل والمتطور
مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
شركة AdenLink - اليافعي
النسخة المتكاملة مع جميع الأقسام المتفرعة
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta, timezone
import json

print("🚀 بدء تشغيل النظام المتكامل والمتطور...")

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'ultimate-advanced-subscription-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ultimate_advanced_subscriptions.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# نماذج قاعدة البيانات المتطورة

# نموذج المستخدم المحسن
class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user', nullable=False)  # admin, user
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    phone = db.Column(db.String(20))
    company = db.Column(db.String(100))
    avatar = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    last_login = db.Column(db.DateTime)
    email_verified = db.Column(db.Boolean, default=False)

    # العلاقات
    subscriptions = db.relationship('Subscription', backref='user', lazy=True, cascade='all, delete-orphan')
    invoices = db.relationship('Invoice', backref='user', lazy=True, cascade='all, delete-orphan')
    sent_messages = db.relationship('Message', backref='sender', lazy=True, cascade='all, delete-orphan')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

# نموذج مزودي الخدمة السحابية
class CloudProvider(db.Model):
    __tablename__ = 'cloud_providers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    logo = db.Column(db.String(255))
    website = db.Column(db.String(255))
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    subscriptions = db.relationship('Subscription', backref='provider', lazy=True)

# نموذج الاشتراكات المتطور
class Subscription(db.Model):
    __tablename__ = 'subscriptions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_providers.id'), nullable=False)

    # معلومات أساسية
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    service_type = db.Column(db.String(50))  # compute, storage, database, etc.
    
    # معلومات الاتصال
    api_key = db.Column(db.String(500))
    server_ip = db.Column(db.String(45))
    port = db.Column(db.Integer)
    username = db.Column(db.String(100))
    password = db.Column(db.String(255))
    endpoint_url = db.Column(db.String(255))
    region = db.Column(db.String(50))

    # معلومات التسعير والفترة
    price = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    billing_cycle = db.Column(db.String(20), default='monthly')  # monthly, quarterly, yearly
    
    # التواريخ
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # الحالة والأولوية
    status = db.Column(db.String(20), default='active')  # active, expired, suspended
    priority = db.Column(db.String(10), default='medium')  # low, medium, high
    tags = db.Column(db.String(255))  # comma-separated tags
    notes = db.Column(db.Text)

    # العلاقات
    invoices = db.relationship('Invoice', backref='subscription', lazy=True, cascade='all, delete-orphan')

# نموذج الفواتير المتطور
class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=False)

    # معلومات المبالغ
    subtotal = db.Column(db.Float, nullable=False, default=0.0)
    tax_rate = db.Column(db.Float, default=0.0)  # percentage
    tax_amount = db.Column(db.Float, default=0.0)
    discount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')

    # التواريخ
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.Date)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # الحالة ومعلومات الدفع
    status = db.Column(db.String(20), default='pending')  # pending, paid, overdue, cancelled
    payment_method = db.Column(db.String(50))
    payment_reference = db.Column(db.String(100))
    notes = db.Column(db.Text)

# نموذج طرق الدفع
class PaymentMethod(db.Model):
    __tablename__ = 'payment_methods'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(50), nullable=False)  # credit_card, bank_transfer, paypal, etc.
    description = db.Column(db.Text)
    processing_fee = db.Column(db.Float, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# نموذج قوالب الرسائل
class MessageTemplate(db.Model):
    __tablename__ = 'message_templates'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    template_type = db.Column(db.String(50), nullable=False)  # welcome, renewal, payment, etc.
    variables = db.Column(db.Text)  # JSON string of available variables
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# نموذج الرسائل المرسلة
class Message(db.Model):
    __tablename__ = 'messages'

    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    recipient_email = db.Column(db.String(255), nullable=False)
    recipient_name = db.Column(db.String(100))
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    template_id = db.Column(db.Integer, db.ForeignKey('message_templates.id'))
    
    # معلومات الإرسال
    sent_at = db.Column(db.DateTime, default=datetime.utcnow)
    delivery_status = db.Column(db.String(20), default='sent')  # sent, delivered, failed
    message_type = db.Column(db.String(50), default='email')  # email, sms, notification
    
    # معلومات إضافية
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'))
    notes = db.Column(db.Text)

    # العلاقات
    template = db.relationship('MessageTemplate', backref='messages', lazy=True)
    related_subscription = db.relationship('Subscription', backref='messages', lazy=True)

print("✅ تم إعداد نماذج قاعدة البيانات المتطورة")

# دوال مساعدة

def generate_invoice_number():
    """إنشاء رقم فاتورة فريد"""
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return f"INV-{timestamp}"

def get_dashboard_stats():
    """إحصائيات لوحة التحكم"""
    try:
        if current_user.is_admin():
            total_subscriptions = Subscription.query.count()
            active_subscriptions = Subscription.query.filter_by(status='active').count()
            total_invoices = Invoice.query.count()
            pending_invoices = Invoice.query.filter_by(status='pending').count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(status='paid').scalar() or 0
            total_users = User.query.count()
        else:
            total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
            active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
            total_invoices = Invoice.query.filter_by(user_id=current_user.id).count()
            pending_invoices = Invoice.query.filter_by(user_id=current_user.id, status='pending').count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(user_id=current_user.id, status='paid').scalar() or 0
            total_users = 1

        return {
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'expired_subscriptions': total_subscriptions - active_subscriptions,
            'total_invoices': total_invoices,
            'pending_invoices': pending_invoices,
            'paid_invoices': total_invoices - pending_invoices,
            'total_revenue': total_revenue,
            'total_users': total_users,
            'monthly_revenue': total_revenue / 12 if total_revenue > 0 else 0
        }
    except Exception as e:
        print(f"خطأ في إحصائيات لوحة التحكم: {e}")
        return {
            'total_subscriptions': 0,
            'active_subscriptions': 0,
            'expired_subscriptions': 0,
            'total_invoices': 0,
            'pending_invoices': 0,
            'paid_invoices': 0,
            'total_revenue': 0,
            'total_users': 0,
            'monthly_revenue': 0
        }

def get_subscription_charts_data():
    """بيانات المخططات التفاعلية"""
    try:
        if current_user.is_admin():
            subscriptions = Subscription.query.all()
        else:
            subscriptions = Subscription.query.filter_by(user_id=current_user.id).all()

        # توزيع حسب الحالة
        status_data = {}
        for sub in subscriptions:
            status_data[sub.status] = status_data.get(sub.status, 0) + 1

        # توزيع حسب المزودين
        provider_data = {}
        for sub in subscriptions:
            provider_name = sub.provider.name if sub.provider else 'غير محدد'
            provider_data[provider_name] = provider_data.get(provider_name, 0) + 1

        # توزيع حسب نوع الفوترة
        billing_data = {}
        for sub in subscriptions:
            billing_data[sub.billing_cycle] = billing_data.get(sub.billing_cycle, 0) + 1

        # الإيرادات الشهرية (آخر 6 أشهر)
        monthly_revenue = []
        for i in range(6):
            month_start = (datetime.now() - timedelta(days=30*i)).replace(day=1)
            month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
            
            if current_user.is_admin():
                revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter(
                    Invoice.status == 'paid',
                    Invoice.paid_date >= month_start,
                    Invoice.paid_date <= month_end
                ).scalar() or 0
            else:
                revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter(
                    Invoice.user_id == current_user.id,
                    Invoice.status == 'paid',
                    Invoice.paid_date >= month_start,
                    Invoice.paid_date <= month_end
                ).scalar() or 0
            
            monthly_revenue.append({
                'month': month_start.strftime('%Y-%m'),
                'revenue': float(revenue)
            })

        return {
            'status_data': status_data,
            'provider_data': provider_data,
            'billing_data': billing_data,
            'monthly_revenue': list(reversed(monthly_revenue))
        }
    except Exception as e:
        print(f"خطأ في بيانات المخططات: {e}")
        return {
            'status_data': {},
            'provider_data': {},
            'billing_data': {},
            'monthly_revenue': []
        }

print("✅ تم إعداد الدوال المساعدة")

# قوالب HTML المتطورة

# قالب تسجيل الدخول المتطور
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            animation: slideIn 0.8s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo-icon {
            font-size: 4rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        
        .system-title {
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #ffffff, #e0e0e0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .system-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }
        
        .developer-info {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            color: white;
        }
        
        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .form-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.4);
            color: white;
        }
        
        .btn-login:active {
            transform: translateY(0);
        }
        
        .demo-credentials {
            background: rgba(0, 212, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: center;
        }
        
        .demo-title {
            color: #00d4ff;
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .demo-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }
        
        .demo-label {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 600;
        }
        
        .demo-value {
            color: #00d4ff;
            font-weight: 700;
            font-family: 'Courier New', monospace;
        }
        
        .alert {
            border-radius: 15px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background: rgba(255, 0, 110, 0.2);
            color: #ff006e;
            border: 2px solid rgba(255, 0, 110, 0.3);
        }
        
        .features-list {
            margin-top: 1rem;
            text-align: right;
        }
        
        .feature-item {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            margin-bottom: 0.3rem;
        }
        
        .feature-icon {
            color: #00d4ff;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <div class="logo-icon">
                <i class="fas fa-rocket"></i>
            </div>
            <h1 class="system-title">النظام المتكامل والمتطور</h1>
            <p class="system-subtitle">نظام إدارة الاشتراكات الشامل</p>
            <p class="developer-info">مطور بواسطة: المهندس محمد ياسر الجبوري ❤️</p>
            <p class="developer-info">شركة AdenLink - اليافعي</p>
        </div>
        
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <div class="form-floating">
                <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
                <label for="username"><i class="fas fa-user me-2"></i>اسم المستخدم</label>
            </div>
            
            <div class="form-floating">
                <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                <label for="password"><i class="fas fa-lock me-2"></i>كلمة المرور</label>
            </div>
            
            <button type="submit" class="btn btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
            </button>
        </form>
        
        <div class="demo-credentials">
            <div class="demo-title">
                <i class="fas fa-key me-2"></i>بيانات التجربة
            </div>
            <div class="demo-item">
                <span class="demo-label">اسم المستخدم:</span>
                <span class="demo-value">admin</span>
            </div>
            <div class="demo-item">
                <span class="demo-label">كلمة المرور:</span>
                <span class="demo-value">123456</span>
            </div>
            
            <div class="features-list">
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>أقسام متفرعة متطورة
                </div>
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>مخططات تفاعلية متقدمة
                </div>
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>نظام رسائل إلكترونية
                </div>
                <div class="feature-item">
                    <i class="fas fa-check feature-icon"></i>تقارير وتحليلات شاملة
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

# قالب لوحة التحكم المتطورة مع الشريط الجانبي المتفرع
DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المتطورة - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* الشريط الجانبي المتطور */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-left: 2px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .system-name {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.3rem;
        }

        .developer-name {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* قائمة التنقل المتفرعة */
        .nav-menu {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 1rem;
        }

        .nav-section-title {
            padding: 0.8rem 1.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .nav-link:hover {
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
            border-left-color: #00d4ff;
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: rgba(0, 212, 255, 0.2);
            color: #00d4ff;
            border-left-color: #00d4ff;
        }

        .nav-link i {
            width: 20px;
            margin-left: 1rem;
            font-size: 1.1rem;
        }

        .nav-link .badge {
            margin-right: auto;
            background: linear-gradient(135deg, #ff006e, #b537f2);
            border: none;
            font-size: 0.7rem;
        }

        /* القوائم الفرعية */
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.2);
        }

        .nav-submenu.show {
            max-height: 500px;
        }

        .nav-submenu .nav-link {
            padding: 0.8rem 1.5rem 0.8rem 3rem;
            font-size: 0.9rem;
            border-left: none;
            border-right: 3px solid transparent;
        }

        .nav-submenu .nav-link:hover {
            border-right-color: #00d4ff;
            border-left-color: transparent;
            transform: translateX(5px);
        }

        .nav-toggle {
            background: none;
            border: none;
            color: inherit;
            font-size: 0.8rem;
            margin-right: auto;
            transition: transform 0.3s ease;
        }

        .nav-toggle.rotated {
            transform: rotate(180deg);
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        /* الشريط العلوي */
        .top-bar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 700;
        }

        .user-details h6 {
            margin: 0;
            font-weight: 700;
        }

        .user-details small {
            color: rgba(255, 255, 255, 0.7);
        }

        .btn-logout {
            background: linear-gradient(135deg, #ff006e, #b537f2);
            border: none;
            border-radius: 12px;
            padding: 0.8rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .btn-logout:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 0, 110, 0.3);
            color: white;
        }

        /* بطاقات الإحصائيات */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
            border-color: rgba(0, 212, 255, 0.3);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 600;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-icon.primary {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
        }

        .stat-icon.success {
            background: linear-gradient(135deg, #00ff88, #00cc6a);
        }

        .stat-icon.warning {
            background: linear-gradient(135deg, #ffaa00, #ff8800);
        }

        .stat-icon.danger {
            background: linear-gradient(135deg, #ff006e, #cc0055);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stat-change.positive {
            color: #00ff88;
        }

        .stat-change.negative {
            color: #ff006e;
        }

        /* الرسوم البيانية */
        .charts-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-align: center;
            color: white;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        /* التجاوب */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                width: 100%;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
                padding: 1rem;
            }

            .top-bar {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .charts-section {
                grid-template-columns: 1fr;
            }
        }

        /* زر القائمة للموبايل */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: rgba(0, 212, 255, 0.2);
            border: 2px solid #00d4ff;
            border-radius: 12px;
            padding: 0.8rem;
            color: #00d4ff;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            background: rgba(0, 212, 255, 0.3);
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- زر القائمة للموبايل -->
    <button class="mobile-menu-btn" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- الشريط الجانبي المتطور -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-rocket"></i>
            </div>
            <div class="system-name">النظام المتكامل</div>
            <div class="developer-name">محمد ياسر الجبوري</div>
        </div>

        <nav class="nav-menu">
            <!-- القسم الرئيسي -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="fas fa-home me-2"></i>الرئيسية
                </div>
                <div class="nav-item">
                    <a href="{{ url_for('dashboard') }}" class="nav-link active">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </div>
            </div>

            <!-- قسم إدارة الاشتراكات -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="fas fa-server me-2"></i>إدارة الاشتراكات
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="toggleSubmenu('subscriptions-menu')">
                        <i class="fas fa-server"></i>
                        <span>الاشتراكات</span>
                        <button class="nav-toggle">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </a>
                    <div class="nav-submenu" id="subscriptions-menu">
                        <a href="{{ url_for('subscription_charts') }}" class="nav-link">
                            <i class="fas fa-chart-pie"></i>
                            <span>المخططات التفاعلية</span>
                        </a>
                        <a href="{{ url_for('subscriptions') }}" class="nav-link">
                            <i class="fas fa-list"></i>
                            <span>قائمة الاشتراكات</span>
                        </a>
                        <a href="{{ url_for('add_subscription') }}" class="nav-link">
                            <i class="fas fa-plus"></i>
                            <span>إضافة اشتراك</span>
                        </a>
                        <a href="{{ url_for('subscription_analytics') }}" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span>التحليلات</span>
                        </a>
                        <a href="{{ url_for('subscription_reports') }}" class="nav-link">
                            <i class="fas fa-file-alt"></i>
                            <span>التقارير</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- قسم إدارة الفواتير -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="toggleSubmenu('invoices-menu')">
                        <i class="fas fa-file-invoice"></i>
                        <span>الفواتير</span>
                        <button class="nav-toggle">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </a>
                    <div class="nav-submenu" id="invoices-menu">
                        <a href="{{ url_for('invoices') }}" class="nav-link">
                            <i class="fas fa-list"></i>
                            <span>قائمة الفواتير</span>
                        </a>
                        <a href="{{ url_for('add_invoice') }}" class="nav-link">
                            <i class="fas fa-plus"></i>
                            <span>إنشاء فاتورة</span>
                        </a>
                        <a href="{{ url_for('invoice_reports') }}" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>تقارير الفواتير</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- قسم مركز التواصل -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="fas fa-envelope me-2"></i>مركز التواصل
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="toggleSubmenu('communication-menu')">
                        <i class="fas fa-envelope"></i>
                        <span>الرسائل</span>
                        <span class="badge">جديد</span>
                        <button class="nav-toggle">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </a>
                    <div class="nav-submenu" id="communication-menu">
                        <a href="{{ url_for('message_center') }}" class="nav-link">
                            <i class="fas fa-inbox"></i>
                            <span>مركز الرسائل</span>
                        </a>
                        <a href="{{ url_for('send_message') }}" class="nav-link">
                            <i class="fas fa-paper-plane"></i>
                            <span>إرسال رسالة</span>
                        </a>
                        <a href="{{ url_for('message_templates') }}" class="nav-link">
                            <i class="fas fa-file-alt"></i>
                            <span>قوالب الرسائل</span>
                        </a>
                    </div>
                </div>
            </div>

            {% if current_user.is_admin() %}
            <!-- قسم الإدارة -->
            <div class="nav-section">
                <div class="nav-section-title">
                    <i class="fas fa-cog me-2"></i>الإدارة
                </div>
                <div class="nav-item">
                    <a href="{{ url_for('users') }}" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>إدارة المستخدمين</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="{{ url_for('providers') }}" class="nav-link">
                        <i class="fas fa-cloud"></i>
                        <span>مزودي الخدمة</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="{{ url_for('payment_methods') }}" class="nav-link">
                        <i class="fas fa-credit-card"></i>
                        <span>طرق الدفع</span>
                    </a>
                </div>
            </div>
            {% endif %}
        </nav>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-bar">
            <div>
                <h1 class="page-title">لوحة التحكم المتطورة</h1>
                <p class="text-muted mb-0">مرحباً بك في النظام المتكامل</p>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    {{ current_user.full_name[0] }}
                </div>
                <div class="user-details">
                    <h6>{{ current_user.full_name }}</h6>
                    <small>{{ current_user.role|title }}</small>
                </div>
                <a href="{{ url_for('logout') }}" class="btn-logout">
                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                </a>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">إجمالي الاشتراكات</div>
                    <div class="stat-icon primary">
                        <i class="fas fa-server"></i>
                    </div>
                </div>
                <div class="stat-number">{{ stats.total_subscriptions }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12% من الشهر الماضي</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">الاشتراكات النشطة</div>
                    <div class="stat-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-number">{{ stats.active_subscriptions }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+8% من الشهر الماضي</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">إجمالي الفواتير</div>
                    <div class="stat-icon warning">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                </div>
                <div class="stat-number">{{ stats.total_invoices }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+15% من الشهر الماضي</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">إجمالي الإيرادات</div>
                    <div class="stat-icon danger">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
                <div class="stat-number">${{ "%.0f"|format(stats.total_revenue) }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+25% من الشهر الماضي</span>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="charts-section">
            <div class="chart-card">
                <h3 class="chart-title">توزيع الاشتراكات حسب الحالة</h3>
                <div class="chart-container">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>

            <div class="chart-card">
                <h3 class="chart-title">الإيرادات الشهرية</h3>
                <div class="chart-container">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تبديل القائمة الجانبية للموبايل
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // تبديل القوائم الفرعية
        function toggleSubmenu(menuId) {
            const submenu = document.getElementById(menuId);
            const toggle = event.currentTarget.querySelector('.nav-toggle');

            submenu.classList.toggle('show');
            toggle.classList.toggle('rotated');
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const menuBtn = document.querySelector('.mobile-menu-btn');

            if (!sidebar.contains(event.target) && !menuBtn.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });

        // الرسوم البيانية
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني للحالة
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['نشط', 'منتهي', 'موقوف'],
                    datasets: [{
                        data: [{{ stats.active_subscriptions }}, {{ stats.expired_subscriptions }}, 2],
                        backgroundColor: [
                            'rgba(0, 212, 255, 0.8)',
                            'rgba(255, 0, 110, 0.8)',
                            'rgba(255, 170, 0, 0.8)'
                        ],
                        borderColor: [
                            '#00d4ff',
                            '#ff006e',
                            '#ffaa00'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: 'white',
                                padding: 20
                            }
                        }
                    }
                }
            });

            // رسم بياني للإيرادات
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'الإيرادات ($)',
                        data: [1200, 1900, 3000, 5000, 2000, 3000],
                        borderColor: '#00d4ff',
                        backgroundColor: 'rgba(0, 212, 255, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: 'white'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: 'white'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: 'white'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
'''

# المسارات الأساسية

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            user.last_login = datetime.now(timezone.utc)
            db.session.commit()

            flash('🎉 مرحباً بك في النظام المتكامل! تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('✅ تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    try:
        stats = get_dashboard_stats()
        return render_template_string(DASHBOARD_TEMPLATE, stats=stats)
    except Exception as e:
        print(f"خطأ في لوحة التحكم: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return render_template_string(DASHBOARD_TEMPLATE, stats={
            'total_subscriptions': 0,
            'active_subscriptions': 0,
            'expired_subscriptions': 0,
            'total_invoices': 0,
            'pending_invoices': 0,
            'paid_invoices': 0,
            'total_revenue': 0,
            'total_users': 0,
            'monthly_revenue': 0
        })

# مسارات إدارة الاشتراكات

@app.route('/subscription_charts')
@login_required
def subscription_charts():
    """صفحة المخططات التفاعلية للاشتراكات"""
    try:
        charts_data = get_subscription_charts_data()
        return render_template_string(SUBSCRIPTION_CHARTS_TEMPLATE, charts_data=charts_data)
    except Exception as e:
        print(f"خطأ في مخططات الاشتراكات: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/subscriptions')
@login_required
def subscriptions():
    """صفحة قائمة الاشتراكات"""
    try:
        if current_user.is_admin():
            subscriptions = Subscription.query.all()
        else:
            subscriptions = Subscription.query.filter_by(user_id=current_user.id).all()

        return render_template_string(SUBSCRIPTIONS_LIST_TEMPLATE, subscriptions=subscriptions)
    except Exception as e:
        print(f"خطأ في قائمة الاشتراكات: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/add_subscription', methods=['GET', 'POST'])
@login_required
def add_subscription():
    """صفحة إضافة اشتراك جديد"""
    if request.method == 'POST':
        try:
            # إنشاء اشتراك جديد
            subscription = Subscription(
                user_id=current_user.id,
                provider_id=request.form.get('provider_id'),
                name=request.form.get('name'),
                description=request.form.get('description'),
                service_type=request.form.get('service_type'),
                price=float(request.form.get('price', 0)),
                currency=request.form.get('currency', 'USD'),
                billing_cycle=request.form.get('billing_cycle', 'monthly'),
                start_date=datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date(),
                end_date=datetime.strptime(request.form.get('end_date'), '%Y-%m-%d').date(),
                server_ip=request.form.get('server_ip'),
                port=int(request.form.get('port')) if request.form.get('port') else None,
                username=request.form.get('username'),
                password=request.form.get('password'),
                api_key=request.form.get('api_key'),
                region=request.form.get('region'),
                priority=request.form.get('priority', 'medium'),
                notes=request.form.get('notes')
            )

            db.session.add(subscription)
            db.session.commit()

            flash('✅ تم إضافة الاشتراك بنجاح', 'success')
            return redirect(url_for('subscriptions'))

        except Exception as e:
            print(f"خطأ في إضافة الاشتراك: {e}")
            flash(f'حدث خطأ: {str(e)}', 'error')

    try:
        providers = CloudProvider.query.filter_by(is_active=True).all()
        return render_template_string(ADD_SUBSCRIPTION_TEMPLATE, providers=providers)
    except Exception as e:
        print(f"خطأ في صفحة إضافة الاشتراك: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/subscription_analytics')
@login_required
def subscription_analytics():
    """صفحة تحليلات الاشتراكات"""
    try:
        analytics_data = get_subscription_analytics_data()
        return render_template_string(SUBSCRIPTION_ANALYTICS_TEMPLATE, analytics_data=analytics_data)
    except Exception as e:
        print(f"خطأ في تحليلات الاشتراكات: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/subscription_reports')
@login_required
def subscription_reports():
    """صفحة تقارير الاشتراكات"""
    try:
        reports_data = get_subscription_reports_data()
        return render_template_string(SUBSCRIPTION_REPORTS_TEMPLATE, reports_data=reports_data)
    except Exception as e:
        print(f"خطأ في تقارير الاشتراكات: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# مسارات إدارة الفواتير

@app.route('/invoices')
@login_required
def invoices():
    """صفحة قائمة الفواتير"""
    try:
        if current_user.is_admin():
            invoices = Invoice.query.all()
        else:
            invoices = Invoice.query.filter_by(user_id=current_user.id).all()

        return render_template_string(INVOICES_LIST_TEMPLATE, invoices=invoices)
    except Exception as e:
        print(f"خطأ في قائمة الفواتير: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/add_invoice', methods=['GET', 'POST'])
@login_required
def add_invoice():
    """صفحة إنشاء فاتورة جديدة"""
    if request.method == 'POST':
        try:
            # حساب المبالغ
            subtotal = float(request.form.get('subtotal', 0))
            tax_rate = float(request.form.get('tax_rate', 0))
            discount = float(request.form.get('discount', 0))

            tax_amount = (subtotal * tax_rate) / 100
            total_amount = subtotal + tax_amount - discount

            # إنشاء فاتورة جديدة
            invoice = Invoice(
                invoice_number=generate_invoice_number(),
                user_id=current_user.id,
                subscription_id=request.form.get('subscription_id'),
                subtotal=subtotal,
                tax_rate=tax_rate,
                tax_amount=tax_amount,
                discount=discount,
                total_amount=total_amount,
                currency=request.form.get('currency', 'USD'),
                issue_date=datetime.strptime(request.form.get('issue_date'), '%Y-%m-%d').date(),
                due_date=datetime.strptime(request.form.get('due_date'), '%Y-%m-%d').date(),
                payment_method=request.form.get('payment_method'),
                notes=request.form.get('notes')
            )

            db.session.add(invoice)
            db.session.commit()

            flash('✅ تم إنشاء الفاتورة بنجاح', 'success')
            return redirect(url_for('invoices'))

        except Exception as e:
            print(f"خطأ في إنشاء الفاتورة: {e}")
            flash(f'حدث خطأ: {str(e)}', 'error')

    try:
        if current_user.is_admin():
            subscriptions = Subscription.query.filter_by(status='active').all()
        else:
            subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').all()

        payment_methods = PaymentMethod.query.filter_by(is_active=True).all()
        return render_template_string(ADD_INVOICE_TEMPLATE, subscriptions=subscriptions, payment_methods=payment_methods)
    except Exception as e:
        print(f"خطأ في صفحة إنشاء الفاتورة: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/invoice_reports')
@login_required
def invoice_reports():
    """صفحة تقارير الفواتير"""
    try:
        reports_data = get_invoice_reports_data()
        return render_template_string(INVOICE_REPORTS_TEMPLATE, reports_data=reports_data)
    except Exception as e:
        print(f"خطأ في تقارير الفواتير: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# الدوال المساعدة الإضافية

def get_subscription_analytics_data():
    """بيانات تحليلات الاشتراكات"""
    try:
        if current_user.is_admin():
            subscriptions = Subscription.query.all()
        else:
            subscriptions = Subscription.query.filter_by(user_id=current_user.id).all()

        # تحليل الأداء
        total_cost = sum(sub.price for sub in subscriptions)
        avg_cost = total_cost / len(subscriptions) if subscriptions else 0

        # توزيع حسب المنطقة
        regions = {}
        for sub in subscriptions:
            region = sub.region or 'غير محدد'
            regions[region] = regions.get(region, 0) + 1

        # توزيع حسب الأولوية
        priorities = {}
        for sub in subscriptions:
            priorities[sub.priority] = priorities.get(sub.priority, 0) + 1

        return {
            'total_cost': total_cost,
            'avg_cost': avg_cost,
            'regions': regions,
            'priorities': priorities,
            'subscriptions_count': len(subscriptions)
        }
    except Exception as e:
        print(f"خطأ في تحليلات الاشتراكات: {e}")
        return {}

def get_subscription_reports_data():
    """بيانات تقارير الاشتراكات"""
    try:
        if current_user.is_admin():
            subscriptions = Subscription.query.all()
        else:
            subscriptions = Subscription.query.filter_by(user_id=current_user.id).all()

        # تقرير الاشتراكات المنتهية قريباً
        expiring_soon = []
        for sub in subscriptions:
            days_left = (sub.end_date - date.today()).days
            if 0 <= days_left <= 30:
                expiring_soon.append({
                    'subscription': sub,
                    'days_left': days_left
                })

        # تقرير الاشتراكات عالية التكلفة
        high_cost = [sub for sub in subscriptions if sub.price > 100]

        return {
            'expiring_soon': expiring_soon,
            'high_cost': high_cost,
            'total_subscriptions': len(subscriptions)
        }
    except Exception as e:
        print(f"خطأ في تقارير الاشتراكات: {e}")
        return {}

def get_invoice_reports_data():
    """بيانات تقارير الفواتير"""
    try:
        if current_user.is_admin():
            invoices = Invoice.query.all()
        else:
            invoices = Invoice.query.filter_by(user_id=current_user.id).all()

        # تحليل الفواتير
        total_amount = sum(inv.total_amount for inv in invoices)
        paid_amount = sum(inv.total_amount for inv in invoices if inv.status == 'paid')
        pending_amount = sum(inv.total_amount for inv in invoices if inv.status == 'pending')

        # الفواتير المتأخرة
        overdue_invoices = [inv for inv in invoices if inv.due_date < date.today() and inv.status == 'pending']

        return {
            'total_amount': total_amount,
            'paid_amount': paid_amount,
            'pending_amount': pending_amount,
            'overdue_invoices': overdue_invoices,
            'total_invoices': len(invoices)
        }
    except Exception as e:
        print(f"خطأ في تقارير الفواتير: {e}")
        return {}

# قالب المخططات التفاعلية
SUBSCRIPTION_CHARTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المخططات التفاعلية - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 2rem;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
        }

        .chart-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 2rem;
            text-align: center;
            color: white;
        }

        .chart-container {
            position: relative;
            height: 400px;
        }

        .back-btn {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 212, 255, 0.3);
            color: white;
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }

            body {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-chart-pie me-3"></i>
            المخططات التفاعلية المتطورة
        </h1>
        <p class="lead mb-3">رؤى عميقة وتحليلات شاملة لاشتراكاتك</p>
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للوحة التحكم
        </a>
    </div>

    <div class="charts-grid">
        <!-- مخطط توزيع الحالة -->
        <div class="chart-card">
            <h3 class="chart-title">
                <i class="fas fa-chart-pie me-2"></i>
                توزيع الاشتراكات حسب الحالة
            </h3>
            <div class="chart-container">
                <canvas id="statusChart"></canvas>
            </div>
        </div>

        <!-- مخطط توزيع المزودين -->
        <div class="chart-card">
            <h3 class="chart-title">
                <i class="fas fa-chart-bar me-2"></i>
                توزيع الاشتراكات حسب المزودين
            </h3>
            <div class="chart-container">
                <canvas id="providerChart"></canvas>
            </div>
        </div>

        <!-- مخطط نوع الفوترة -->
        <div class="chart-card">
            <h3 class="chart-title">
                <i class="fas fa-chart-donut me-2"></i>
                توزيع حسب نوع الفوترة
            </h3>
            <div class="chart-container">
                <canvas id="billingChart"></canvas>
            </div>
        </div>

        <!-- مخطط الإيرادات الشهرية -->
        <div class="chart-card">
            <h3 class="chart-title">
                <i class="fas fa-chart-line me-2"></i>
                الإيرادات الشهرية
            </h3>
            <div class="chart-container">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // بيانات المخططات من الخادم
        const chartsData = {{ charts_data|tojson }};

        // إعدادات الألوان
        const colors = {
            primary: ['#00d4ff', '#0099cc', '#006699'],
            secondary: ['#ff006e', '#cc0055', '#990041'],
            success: ['#00ff88', '#00cc6a', '#009951'],
            warning: ['#ffaa00', '#cc8800', '#996600'],
            info: ['#17a2b8', '#138496', '#0f6674'],
            gradient: [
                'rgba(0, 212, 255, 0.8)',
                'rgba(255, 0, 110, 0.8)',
                'rgba(0, 255, 136, 0.8)',
                'rgba(255, 170, 0, 0.8)',
                'rgba(181, 55, 242, 0.8)'
            ]
        };

        // مخطط الحالة
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(chartsData.status_data || {}),
                datasets: [{
                    data: Object.values(chartsData.status_data || {}),
                    backgroundColor: colors.gradient,
                    borderColor: colors.primary,
                    borderWidth: 3,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: 'white',
                            padding: 20,
                            font: {
                                size: 14,
                                family: 'Cairo'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#00d4ff',
                        borderWidth: 1
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 2000
                }
            }
        });

        // مخطط المزودين
        const providerCtx = document.getElementById('providerChart').getContext('2d');
        new Chart(providerCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(chartsData.provider_data || {}),
                datasets: [{
                    label: 'عدد الاشتراكات',
                    data: Object.values(chartsData.provider_data || {}),
                    backgroundColor: colors.gradient,
                    borderColor: colors.primary[0],
                    borderWidth: 2,
                    borderRadius: 10,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white',
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: 'white',
                            font: {
                                family: 'Cairo'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: 'white',
                            font: {
                                family: 'Cairo'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        // مخطط نوع الفوترة
        const billingCtx = document.getElementById('billingChart').getContext('2d');
        new Chart(billingCtx, {
            type: 'pie',
            data: {
                labels: Object.keys(chartsData.billing_data || {}),
                datasets: [{
                    data: Object.values(chartsData.billing_data || {}),
                    backgroundColor: colors.gradient,
                    borderColor: 'white',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: 'white',
                            padding: 20,
                            font: {
                                size: 14,
                                family: 'Cairo'
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 2000
                }
            }
        });

        // مخطط الإيرادات
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: (chartsData.monthly_revenue || []).map(item => item.month),
                datasets: [{
                    label: 'الإيرادات ($)',
                    data: (chartsData.monthly_revenue || []).map(item => item.revenue),
                    borderColor: colors.primary[0],
                    backgroundColor: 'rgba(0, 212, 255, 0.1)',
                    borderWidth: 4,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: colors.primary[0],
                    pointBorderColor: 'white',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white',
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: 'white',
                            font: {
                                family: 'Cairo'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: 'white',
                            font: {
                                family: 'Cairo'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    </script>
</body>
</html>
'''

# مسارات مركز التواصل

@app.route('/message_center')
@login_required
def message_center():
    """مركز الرسائل"""
    try:
        if current_user.is_admin():
            messages = Message.query.order_by(Message.sent_at.desc()).all()
        else:
            messages = Message.query.filter_by(sender_id=current_user.id).order_by(Message.sent_at.desc()).all()

        return render_template_string(MESSAGE_CENTER_TEMPLATE, messages=messages)
    except Exception as e:
        print(f"خطأ في مركز الرسائل: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/send_message', methods=['GET', 'POST'])
@login_required
def send_message():
    """إرسال رسالة"""
    if request.method == 'POST':
        try:
            # معالجة إرسال الرسالة
            recipient_type = request.form.get('recipient_type')
            subject = request.form.get('subject')
            body = request.form.get('body')
            template_id = request.form.get('template_id') if request.form.get('template_id') else None

            recipients = []

            if recipient_type == 'specific':
                # عميل محدد
                subscription_id = request.form.get('subscription_id')
                subscription = Subscription.query.get(subscription_id)
                if subscription:
                    recipients.append({
                        'email': subscription.user.email,
                        'name': subscription.user.full_name,
                        'subscription_id': subscription.id
                    })

            elif recipient_type == 'all_active':
                # جميع العملاء النشطين
                active_subscriptions = Subscription.query.filter_by(status='active').all()
                for sub in active_subscriptions:
                    recipients.append({
                        'email': sub.user.email,
                        'name': sub.user.full_name,
                        'subscription_id': sub.id
                    })

            elif recipient_type == 'expiring_soon':
                # العملاء المنتهية اشتراكاتهم قريباً
                expiring_date = date.today() + timedelta(days=30)
                expiring_subscriptions = Subscription.query.filter(
                    Subscription.end_date <= expiring_date,
                    Subscription.status == 'active'
                ).all()
                for sub in expiring_subscriptions:
                    recipients.append({
                        'email': sub.user.email,
                        'name': sub.user.full_name,
                        'subscription_id': sub.id
                    })

            elif recipient_type == 'custom':
                # إيميل مخصص
                custom_email = request.form.get('custom_email')
                custom_name = request.form.get('custom_name', 'عميل')
                recipients.append({
                    'email': custom_email,
                    'name': custom_name,
                    'subscription_id': None
                })

            # إرسال الرسائل
            sent_count = 0
            for recipient in recipients:
                try:
                    # معالجة المتغيرات في النص
                    processed_body = process_message_variables(body, recipient)

                    # إنشاء سجل الرسالة
                    message = Message(
                        sender_id=current_user.id,
                        recipient_email=recipient['email'],
                        recipient_name=recipient['name'],
                        subject=subject,
                        body=processed_body,
                        template_id=template_id,
                        subscription_id=recipient['subscription_id']
                    )

                    db.session.add(message)
                    sent_count += 1

                except Exception as e:
                    print(f"خطأ في إرسال رسالة إلى {recipient['email']}: {e}")

            db.session.commit()
            flash(f'✅ تم إرسال {sent_count} رسالة بنجاح', 'success')
            return redirect(url_for('message_center'))

        except Exception as e:
            print(f"خطأ في إرسال الرسائل: {e}")
            flash(f'حدث خطأ: {str(e)}', 'error')

    try:
        # جلب البيانات المطلوبة للنموذج
        if current_user.is_admin():
            subscriptions = Subscription.query.filter_by(status='active').all()
        else:
            subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').all()

        templates = MessageTemplate.query.filter_by(is_active=True).all()
        return render_template_string(SEND_MESSAGE_TEMPLATE, subscriptions=subscriptions, templates=templates)
    except Exception as e:
        print(f"خطأ في صفحة إرسال الرسائل: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/message_templates')
@login_required
def message_templates():
    """قوالب الرسائل"""
    try:
        templates = MessageTemplate.query.filter_by(is_active=True).all()
        return render_template_string(MESSAGE_TEMPLATES_TEMPLATE, templates=templates)
    except Exception as e:
        print(f"خطأ في قوالب الرسائل: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/get_template/<int:template_id>')
@login_required
def get_template(template_id):
    """جلب قالب رسالة"""
    try:
        template = MessageTemplate.query.get_or_404(template_id)
        return jsonify({
            'subject': template.subject,
            'body': template.body,
            'variables': json.loads(template.variables) if template.variables else []
        })
    except Exception as e:
        print(f"خطأ في جلب القالب: {e}")
        return jsonify({'error': str(e)}), 500

def process_message_variables(body, recipient):
    """معالجة المتغيرات في نص الرسالة"""
    try:
        # المتغيرات الأساسية
        variables = {
            '{customer_name}': recipient['name'],
            '{current_date}': date.today().strftime('%Y-%m-%d')
        }

        # إذا كان هناك اشتراك مرتبط
        if recipient['subscription_id']:
            subscription = Subscription.query.get(recipient['subscription_id'])
            if subscription:
                variables.update({
                    '{subscription_name}': subscription.name,
                    '{cloud_name}': subscription.provider.name if subscription.provider else 'غير محدد',
                    '{price}': str(subscription.price),
                    '{currency}': subscription.currency,
                    '{end_date}': subscription.end_date.strftime('%Y-%m-%d'),
                    '{server_ip}': subscription.server_ip or 'غير محدد'
                })

        # استبدال المتغيرات
        processed_body = body
        for variable, value in variables.items():
            processed_body = processed_body.replace(variable, value)

        return processed_body
    except Exception as e:
        print(f"خطأ في معالجة المتغيرات: {e}")
        return body

# مسارات إدارية إضافية

@app.route('/users')
@login_required
def users():
    """إدارة المستخدمين (للمديرين فقط)"""
    if not current_user.is_admin():
        flash('❌ ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        users = User.query.all()
        return render_template_string(USERS_TEMPLATE, users=users)
    except Exception as e:
        print(f"خطأ في إدارة المستخدمين: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/providers')
@login_required
def providers():
    """إدارة مزودي الخدمة (للمديرين فقط)"""
    if not current_user.is_admin():
        flash('❌ ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        providers = CloudProvider.query.all()
        return render_template_string(PROVIDERS_TEMPLATE, providers=providers)
    except Exception as e:
        print(f"خطأ في إدارة مزودي الخدمة: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/payment_methods')
@login_required
def payment_methods():
    """إدارة طرق الدفع (للمديرين فقط)"""
    if not current_user.is_admin():
        flash('❌ ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        payment_methods = PaymentMethod.query.all()
        return render_template_string(PAYMENT_METHODS_TEMPLATE, payment_methods=payment_methods)
    except Exception as e:
        print(f"خطأ في إدارة طرق الدفع: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# القوالب المبسطة للصفحات الأخرى

# قالب قائمة الاشتراكات
SUBSCRIPTIONS_LIST_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الاشتراكات - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .page-header { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 20px; padding: 2rem; margin-bottom: 2rem; text-align: center; }
        .subscription-card { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem; margin-bottom: 1rem; transition: all 0.3s ease; }
        .subscription-card:hover { transform: translateY(-3px); box-shadow: 0 10px 25px rgba(0, 212, 255, 0.2); }
        .back-btn { background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600; }
    </style>
</head>
<body>
    <div class="page-header">
        <h1><i class="fas fa-list me-3"></i>قائمة الاشتراكات</h1>
        <a href="{{ url_for('dashboard') }}" class="back-btn"><i class="fas fa-arrow-right me-2"></i>العودة</a>
    </div>

    <div class="row">
        {% for subscription in subscriptions %}
        <div class="col-md-6 col-lg-4">
            <div class="subscription-card">
                <h5>{{ subscription.name }}</h5>
                <p><strong>المزود:</strong> {{ subscription.provider.name if subscription.provider else 'غير محدد' }}</p>
                <p><strong>السعر:</strong> {{ subscription.price }} {{ subscription.currency }}</p>
                <p><strong>الحالة:</strong>
                    <span class="badge {% if subscription.status == 'active' %}bg-success{% else %}bg-danger{% endif %}">
                        {{ subscription.status }}
                    </span>
                </p>
                <p><strong>تاريخ الانتهاء:</strong> {{ subscription.end_date }}</p>
            </div>
        </div>
        {% endfor %}
    </div>
</body>
</html>
'''

# قالب إضافة اشتراك
ADD_SUBSCRIPTION_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة اشتراك - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .form-container { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 20px; padding: 2rem; }
        .form-control { background: rgba(255, 255, 255, 0.1); border: 2px solid rgba(255, 255, 255, 0.2); color: white; }
        .form-control:focus { background: rgba(255, 255, 255, 0.15); border-color: #00d4ff; color: white; }
        .btn-primary { background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; }
    </style>
</head>
<body>
    <div class="form-container">
        <h2 class="text-center mb-4"><i class="fas fa-plus me-2"></i>إضافة اشتراك جديد</h2>

        <form method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">اسم الاشتراك</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">المزود</label>
                        <select class="form-control" name="provider_id" required>
                            <option value="">اختر المزود</option>
                            {% for provider in providers %}
                            <option value="{{ provider.id }}">{{ provider.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">السعر</label>
                        <input type="number" step="0.01" class="form-control" name="price" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">العملة</label>
                        <select class="form-control" name="currency">
                            <option value="USD">USD</option>
                            <option value="EUR">EUR</option>
                            <option value="IQD">IQD</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ البداية</label>
                        <input type="date" class="form-control" name="start_date" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ النهاية</label>
                        <input type="date" class="form-control" name="end_date" required>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">الوصف</label>
                <textarea class="form-control" name="description" rows="3"></textarea>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>حفظ الاشتراك
                </button>
                <a href="{{ url_for('subscriptions') }}" class="btn btn-secondary btn-lg ms-2">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</body>
</html>
'''

# قالب مركز الرسائل
MESSAGE_CENTER_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز الرسائل - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .page-header { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 20px; padding: 2rem; margin-bottom: 2rem; text-align: center; }
        .message-card { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem; margin-bottom: 1rem; }
        .btn-primary { background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 10px; }
    </style>
</head>
<body>
    <div class="page-header">
        <h1><i class="fas fa-envelope me-3"></i>مركز الرسائل</h1>
        <a href="{{ url_for('send_message') }}" class="btn btn-primary">
            <i class="fas fa-paper-plane me-2"></i>إرسال رسالة جديدة
        </a>
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-right me-2"></i>العودة
        </a>
    </div>

    <div class="row">
        {% for message in messages %}
        <div class="col-12">
            <div class="message-card">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5>{{ message.subject }}</h5>
                        <p><strong>إلى:</strong> {{ message.recipient_email }}</p>
                        <p><strong>التاريخ:</strong> {{ message.sent_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        <p class="text-truncate" style="max-width: 500px;">{{ message.body[:100] }}...</p>
                    </div>
                    <div>
                        <span class="badge {% if message.delivery_status == 'sent' %}bg-success{% else %}bg-warning{% endif %}">
                            {{ message.delivery_status }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</body>
</html>
'''

# قالب إرسال رسالة
SEND_MESSAGE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إرسال رسالة - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .form-container { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 20px; padding: 2rem; }
        .form-control, .form-select { background: rgba(255, 255, 255, 0.1); border: 2px solid rgba(255, 255, 255, 0.2); color: white; }
        .form-control:focus, .form-select:focus { background: rgba(255, 255, 255, 0.15); border-color: #00d4ff; color: white; }
        .btn-primary { background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; }
        .variables-help { background: rgba(0, 212, 255, 0.1); border: 2px solid rgba(0, 212, 255, 0.3); border-radius: 10px; padding: 1rem; margin-top: 1rem; }
    </style>
</head>
<body>
    <div class="form-container">
        <h2 class="text-center mb-4"><i class="fas fa-paper-plane me-2"></i>إرسال رسالة جديدة</h2>

        <form method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">نوع المستقبل</label>
                        <select class="form-select" name="recipient_type" id="recipientType" onchange="toggleRecipientOptions()" required>
                            <option value="">اختر نوع المستقبل</option>
                            <option value="specific">عميل اشتراك محدد</option>
                            <option value="all_active">جميع العملاء النشطين</option>
                            <option value="expiring_soon">العملاء المنتهية اشتراكاتهم قريباً</option>
                            <option value="custom">إيميل مخصص</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">قالب الرسالة (اختياري)</label>
                        <select class="form-select" name="template_id" id="templateSelect" onchange="loadTemplate()">
                            <option value="">اختر قالب جاهز</option>
                            {% for template in templates %}
                            <option value="{{ template.id }}">{{ template.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <div id="specificSubscription" style="display: none;">
                <div class="mb-3">
                    <label class="form-label">الاشتراك</label>
                    <select class="form-select" name="subscription_id">
                        <option value="">اختر الاشتراك</option>
                        {% for subscription in subscriptions %}
                        <option value="{{ subscription.id }}">{{ subscription.name }} - {{ subscription.user.full_name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <div id="customEmail" style="display: none;">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="custom_email" placeholder="<EMAIL>">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">الاسم</label>
                            <input type="text" class="form-control" name="custom_name" placeholder="اسم المستقبل">
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">موضوع الرسالة</label>
                <input type="text" class="form-control" name="subject" id="messageSubject" required>
            </div>

            <div class="mb-3">
                <label class="form-label">نص الرسالة</label>
                <textarea class="form-control" name="body" id="messageBody" rows="8" required></textarea>
            </div>

            <div class="variables-help">
                <h6><i class="fas fa-info-circle me-2"></i>المتغيرات المتاحة:</h6>
                <div class="row">
                    <div class="col-md-6">
                        <small><code>{customer_name}</code> - اسم العميل</small><br>
                        <small><code>{subscription_name}</code> - اسم الاشتراك</small><br>
                        <small><code>{cloud_name}</code> - اسم المزود</small>
                    </div>
                    <div class="col-md-6">
                        <small><code>{price}</code> - السعر</small><br>
                        <small><code>{currency}</code> - العملة</small><br>
                        <small><code>{end_date}</code> - تاريخ الانتهاء</small>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-paper-plane me-2"></i>إرسال الرسالة
                </button>
                <a href="{{ url_for('message_center') }}" class="btn btn-secondary btn-lg ms-2">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <script>
        function toggleRecipientOptions() {
            const type = document.getElementById('recipientType').value;
            document.getElementById('specificSubscription').style.display = type === 'specific' ? 'block' : 'none';
            document.getElementById('customEmail').style.display = type === 'custom' ? 'block' : 'none';
        }

        function loadTemplate() {
            const templateId = document.getElementById('templateSelect').value;
            if (templateId) {
                fetch(`/get_template/${templateId}`)
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('messageSubject').value = data.subject;
                        document.getElementById('messageBody').value = data.body;
                    })
                    .catch(error => console.error('Error:', error));
            }
        }
    </script>
</body>
</html>
'''

# القوالب المتبقية (مبسطة)

MESSAGE_TEMPLATES_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>قوالب الرسائل - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .template-card { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4"><i class="fas fa-file-alt me-2"></i>قوالب الرسائل</h1>
        <div class="text-center mb-4">
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>العودة</a>
        </div>

        {% for template in templates %}
        <div class="template-card">
            <h5>{{ template.name }}</h5>
            <p><strong>الموضوع:</strong> {{ template.subject }}</p>
            <p><strong>النوع:</strong> {{ template.template_type }}</p>
            <p class="text-truncate">{{ template.body[:100] }}...</p>
        </div>
        {% endfor %}
    </div>
</body>
</html>
'''

SUBSCRIPTION_ANALYTICS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تحليلات الاشتراكات - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .analytics-card { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 2rem; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4"><i class="fas fa-chart-line me-2"></i>تحليلات الاشتراكات</h1>
        <div class="text-center mb-4">
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>العودة</a>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="analytics-card">
                    <h5>إجمالي التكلفة</h5>
                    <h2>${{ "%.2f"|format(analytics_data.total_cost or 0) }}</h2>
                </div>
            </div>
            <div class="col-md-6">
                <div class="analytics-card">
                    <h5>متوسط التكلفة</h5>
                    <h2>${{ "%.2f"|format(analytics_data.avg_cost or 0) }}</h2>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

SUBSCRIPTION_REPORTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقارير الاشتراكات - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .report-card { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 2rem; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4"><i class="fas fa-file-alt me-2"></i>تقارير الاشتراكات</h1>
        <div class="text-center mb-4">
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>العودة</a>
        </div>

        <div class="report-card">
            <h5>الاشتراكات المنتهية قريباً</h5>
            <p>عدد الاشتراكات: {{ reports_data.expiring_soon|length }}</p>
        </div>
    </div>
</body>
</html>
'''

INVOICES_LIST_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>قائمة الفواتير - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .invoice-card { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4"><i class="fas fa-file-invoice me-2"></i>قائمة الفواتير</h1>
        <div class="text-center mb-4">
            <a href="{{ url_for('add_invoice') }}" class="btn btn-primary"><i class="fas fa-plus me-2"></i>إنشاء فاتورة</a>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary ms-2"><i class="fas fa-arrow-right me-2"></i>العودة</a>
        </div>

        {% for invoice in invoices %}
        <div class="invoice-card">
            <div class="row">
                <div class="col-md-8">
                    <h5>فاتورة رقم: {{ invoice.invoice_number }}</h5>
                    <p><strong>الاشتراك:</strong> {{ invoice.subscription.name }}</p>
                    <p><strong>المبلغ:</strong> {{ invoice.total_amount }} {{ invoice.currency }}</p>
                    <p><strong>تاريخ الإصدار:</strong> {{ invoice.issue_date }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge {% if invoice.status == 'paid' %}bg-success{% elif invoice.status == 'pending' %}bg-warning{% else %}bg-danger{% endif %}">
                        {{ invoice.status }}
                    </span>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</body>
</html>
'''

ADD_INVOICE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إنشاء فاتورة - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .form-container { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 20px; padding: 2rem; }
        .form-control, .form-select { background: rgba(255, 255, 255, 0.1); border: 2px solid rgba(255, 255, 255, 0.2); color: white; }
        .form-control:focus, .form-select:focus { background: rgba(255, 255, 255, 0.15); border-color: #00d4ff; color: white; }
    </style>
</head>
<body>
    <div class="form-container">
        <h2 class="text-center mb-4"><i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة جديدة</h2>

        <form method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الاشتراك</label>
                        <select class="form-select" name="subscription_id" required>
                            <option value="">اختر الاشتراك</option>
                            {% for subscription in subscriptions %}
                            <option value="{{ subscription.id }}">{{ subscription.name }} - {{ subscription.user.full_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">المبلغ الفرعي</label>
                        <input type="number" step="0.01" class="form-control" name="subtotal" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ الإصدار</label>
                        <input type="date" class="form-control" name="issue_date" value="{{ date.today() }}" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ الاستحقاق</label>
                        <input type="date" class="form-control" name="due_date" required>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>إنشاء الفاتورة
                </button>
                <a href="{{ url_for('invoices') }}" class="btn btn-secondary btn-lg ms-2">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</body>
</html>
'''

INVOICE_REPORTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقارير الفواتير - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .report-card { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 2rem; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4"><i class="fas fa-chart-bar me-2"></i>تقارير الفواتير</h1>
        <div class="text-center mb-4">
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>العودة</a>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="report-card">
                    <h5>إجمالي المبالغ</h5>
                    <h2>${{ "%.2f"|format(reports_data.total_amount or 0) }}</h2>
                </div>
            </div>
            <div class="col-md-4">
                <div class="report-card">
                    <h5>المبالغ المدفوعة</h5>
                    <h2>${{ "%.2f"|format(reports_data.paid_amount or 0) }}</h2>
                </div>
            </div>
            <div class="col-md-4">
                <div class="report-card">
                    <h5>المبالغ المعلقة</h5>
                    <h2>${{ "%.2f"|format(reports_data.pending_amount or 0) }}</h2>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

USERS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة المستخدمين - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .user-card { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4"><i class="fas fa-users me-2"></i>إدارة المستخدمين</h1>
        <div class="text-center mb-4">
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>العودة</a>
        </div>

        {% for user in users %}
        <div class="user-card">
            <div class="row">
                <div class="col-md-8">
                    <h5>{{ user.full_name }}</h5>
                    <p><strong>اسم المستخدم:</strong> {{ user.username }}</p>
                    <p><strong>البريد:</strong> {{ user.email }}</p>
                    <p><strong>الدور:</strong> {{ user.role }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge {% if user.is_active %}bg-success{% else %}bg-danger{% endif %}">
                        {% if user.is_active %}نشط{% else %}غير نشط{% endif %}
                    </span>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</body>
</html>
'''

PROVIDERS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>مزودي الخدمة - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .provider-card { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4"><i class="fas fa-cloud me-2"></i>مزودي الخدمة</h1>
        <div class="text-center mb-4">
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>العودة</a>
        </div>

        {% for provider in providers %}
        <div class="provider-card">
            <h5>{{ provider.name }}</h5>
            <p><strong>الموقع:</strong> {{ provider.website or 'غير محدد' }}</p>
            <p>{{ provider.description or 'لا يوجد وصف' }}</p>
            <span class="badge {% if provider.is_active %}bg-success{% else %}bg-danger{% endif %}">
                {% if provider.is_active %}نشط{% else %}غير نشط{% endif %}
            </span>
        </div>
        {% endfor %}
    </div>
</body>
</html>
'''

PAYMENT_METHODS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>طرق الدفع - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 2rem; }
        .payment-card { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4"><i class="fas fa-credit-card me-2"></i>طرق الدفع</h1>
        <div class="text-center mb-4">
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary"><i class="fas fa-arrow-right me-2"></i>العودة</a>
        </div>

        {% for method in payment_methods %}
        <div class="payment-card">
            <h5>{{ method.name }}</h5>
            <p><strong>النوع:</strong> {{ method.type }}</p>
            <p><strong>رسوم المعالجة:</strong> {{ method.processing_fee }}%</p>
            <span class="badge {% if method.is_active %}bg-success{% else %}bg-danger{% endif %}">
                {% if method.is_active %}نشط{% else %}غير نشط{% endif %}
            </span>
        </div>
        {% endfor %}
    </div>
</body>
</html>
'''

# تهيئة قاعدة البيانات والبيانات التجريبية

def init_database():
    """تهيئة قاعدة البيانات مع البيانات التجريبية"""
    with app.app_context():
        try:
            # إنشاء الجداول
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات")

            # التحقق من وجود البيانات
            if User.query.count() > 0:
                print("✅ البيانات موجودة مسبقاً")
                return

            # إنشاء المستخدمين
            users_data = [
                {
                    'username': 'admin',
                    'email': '<EMAIL>',
                    'password': '123456',
                    'full_name': 'المدير العام',
                    'role': 'admin',
                    'company': 'AdenLink - اليافعي',
                    'phone': '+964-************'
                },
                {
                    'username': 'user1',
                    'email': '<EMAIL>',
                    'password': '123456',
                    'full_name': 'أحمد محمد علي',
                    'role': 'user',
                    'company': 'شركة التقنيات المتطورة',
                    'phone': '+964-************'
                },
                {
                    'username': 'user2',
                    'email': '<EMAIL>',
                    'password': '123456',
                    'full_name': 'فاطمة أحمد حسن',
                    'role': 'user',
                    'company': 'مؤسسة الحلول الذكية',
                    'phone': '+964-************'
                },
                {
                    'username': 'manager',
                    'email': '<EMAIL>',
                    'password': '123456',
                    'full_name': 'محمد ياسر الجبوري',
                    'role': 'admin',
                    'company': 'AdenLink - اليافعي',
                    'phone': '+964-************'
                }
            ]

            for user_data in users_data:
                user = User(
                    username=user_data['username'],
                    email=user_data['email'],
                    full_name=user_data['full_name'],
                    role=user_data['role'],
                    company=user_data['company'],
                    phone=user_data['phone'],
                    is_active=True,
                    email_verified=True
                )
                user.set_password(user_data['password'])
                db.session.add(user)

            db.session.flush()
            print("✅ تم إنشاء المستخدمين")

            # إنشاء مزودي الخدمة
            providers_data = [
                {
                    'name': 'Amazon Web Services',
                    'slug': 'aws',
                    'website': 'https://aws.amazon.com',
                    'description': 'خدمات الحوسبة السحابية من أمازون'
                },
                {
                    'name': 'Microsoft Azure',
                    'slug': 'azure',
                    'website': 'https://azure.microsoft.com',
                    'description': 'منصة الحوسبة السحابية من مايكروسوفت'
                },
                {
                    'name': 'Google Cloud Platform',
                    'slug': 'gcp',
                    'website': 'https://cloud.google.com',
                    'description': 'خدمات الحوسبة السحابية من جوجل'
                },
                {
                    'name': 'DigitalOcean',
                    'slug': 'digitalocean',
                    'website': 'https://digitalocean.com',
                    'description': 'خدمات الخوادم الافتراضية البسيطة'
                },
                {
                    'name': 'Vultr',
                    'slug': 'vultr',
                    'website': 'https://vultr.com',
                    'description': 'خدمات الخوادم السحابية عالية الأداء'
                }
            ]

            for provider_data in providers_data:
                provider = CloudProvider(
                    name=provider_data['name'],
                    slug=provider_data['slug'],
                    website=provider_data['website'],
                    description=provider_data['description'],
                    is_active=True
                )
                db.session.add(provider)

            db.session.flush()
            print("✅ تم إنشاء مزودي الخدمة")

            # إنشاء طرق الدفع
            payment_methods_data = [
                {
                    'name': 'بطاقة ائتمان',
                    'type': 'credit_card',
                    'description': 'الدفع بالبطاقة الائتمانية',
                    'processing_fee': 2.9,
                    'currency': 'USD'
                },
                {
                    'name': 'PayPal',
                    'type': 'paypal',
                    'description': 'الدفع عبر PayPal',
                    'processing_fee': 3.4,
                    'currency': 'USD'
                },
                {
                    'name': 'تحويل بنكي',
                    'type': 'bank_transfer',
                    'description': 'التحويل البنكي المباشر',
                    'processing_fee': 0.0,
                    'currency': 'USD'
                },
                {
                    'name': 'محفظة إلكترونية',
                    'type': 'e_wallet',
                    'description': 'الدفع بالمحفظة الإلكترونية',
                    'processing_fee': 1.5,
                    'currency': 'USD'
                }
            ]

            for method_data in payment_methods_data:
                method = PaymentMethod(
                    name=method_data['name'],
                    type=method_data['type'],
                    description=method_data['description'],
                    processing_fee=method_data['processing_fee'],
                    currency=method_data['currency'],
                    is_active=True
                )
                db.session.add(method)

            db.session.flush()
            print("✅ تم إنشاء طرق الدفع")

            # إنشاء قوالب الرسائل
            templates_data = [
                {
                    'name': 'رسالة ترحيب',
                    'subject': 'مرحباً بك في خدماتنا، {customer_name}',
                    'body': '''مرحباً {customer_name}،

نرحب بك في خدماتنا السحابية. تم تفعيل اشتراكك "{subscription_name}" بنجاح.

تفاصيل الاشتراك:
- اسم الخدمة: {subscription_name}
- المزود: {cloud_name}
- السعر: {price} {currency}
- تاريخ الانتهاء: {end_date}
- عنوان الخادم: {server_ip}

نتمنى لك تجربة ممتازة مع خدماتنا.

مع أطيب التحيات،
فريق AdenLink - اليافعي''',
                    'template_type': 'welcome',
                    'variables': json.dumps([
                        'customer_name', 'subscription_name', 'cloud_name',
                        'price', 'currency', 'end_date', 'server_ip'
                    ])
                },
                {
                    'name': 'تذكير التجديد',
                    'subject': 'تذكير: اشتراكك {subscription_name} ينتهي قريباً',
                    'body': '''عزيزي {customer_name}،

نود تذكيرك بأن اشتراكك "{subscription_name}" سينتهي في {end_date}.

تفاصيل الاشتراك:
- اسم الخدمة: {subscription_name}
- المزود: {cloud_name}
- السعر: {price} {currency}

يرجى تجديد اشتراكك لتجنب انقطاع الخدمة.

مع أطيب التحيات،
فريق AdenLink - اليافعي''',
                    'template_type': 'renewal',
                    'variables': json.dumps([
                        'customer_name', 'subscription_name', 'cloud_name',
                        'price', 'currency', 'end_date'
                    ])
                },
                {
                    'name': 'استحقاق الدفع',
                    'subject': 'فاتورة مستحقة للاشتراك {subscription_name}',
                    'body': '''عزيزي {customer_name}،

لديك فاتورة مستحقة للاشتراك "{subscription_name}".

تفاصيل الفاتورة:
- اسم الخدمة: {subscription_name}
- المبلغ المستحق: {price} {currency}
- تاريخ الاستحقاق: {end_date}

يرجى سداد المبلغ في أقرب وقت ممكن.

مع أطيب التحيات،
فريق AdenLink - اليافعي''',
                    'template_type': 'payment',
                    'variables': json.dumps([
                        'customer_name', 'subscription_name', 'price', 'currency', 'end_date'
                    ])
                }
            ]

            for template_data in templates_data:
                template = MessageTemplate(
                    name=template_data['name'],
                    subject=template_data['subject'],
                    body=template_data['body'],
                    template_type=template_data['template_type'],
                    variables=template_data['variables'],
                    is_active=True
                )
                db.session.add(template)

            db.session.flush()
            print("✅ تم إنشاء قوالب الرسائل")

            # إنشاء الاشتراكات التجريبية
            subscriptions_data = [
                {
                    'user_id': 2,  # user1
                    'provider_id': 1,  # AWS
                    'name': 'خادم ويب أساسي',
                    'description': 'خادم ويب للموقع الشخصي',
                    'service_type': 'compute',
                    'price': 25.99,
                    'currency': 'USD',
                    'billing_cycle': 'monthly',
                    'start_date': date.today() - timedelta(days=15),
                    'end_date': date.today() + timedelta(days=15),
                    'server_ip': '*************',
                    'port': 80,
                    'username': 'admin',
                    'region': 'us-east-1',
                    'priority': 'medium',
                    'status': 'active'
                },
                {
                    'user_id': 2,  # user1
                    'provider_id': 2,  # Azure
                    'name': 'قاعدة بيانات متقدمة',
                    'description': 'قاعدة بيانات للتطبيق الرئيسي',
                    'service_type': 'database',
                    'price': 89.99,
                    'currency': 'USD',
                    'billing_cycle': 'monthly',
                    'start_date': date.today() - timedelta(days=30),
                    'end_date': date.today() + timedelta(days=60),
                    'server_ip': '*********',
                    'port': 3306,
                    'username': 'dbadmin',
                    'region': 'east-us',
                    'priority': 'high',
                    'status': 'active'
                },
                {
                    'user_id': 3,  # user2
                    'provider_id': 3,  # GCP
                    'name': 'تخزين سحابي',
                    'description': 'مساحة تخزين للملفات',
                    'service_type': 'storage',
                    'price': 15.50,
                    'currency': 'USD',
                    'billing_cycle': 'monthly',
                    'start_date': date.today() - timedelta(days=45),
                    'end_date': date.today() + timedelta(days=45),
                    'region': 'us-central1',
                    'priority': 'low',
                    'status': 'active'
                },
                {
                    'user_id': 3,  # user2
                    'provider_id': 4,  # DigitalOcean
                    'name': 'خادم تطوير',
                    'description': 'خادم للتطوير والاختبار',
                    'service_type': 'compute',
                    'price': 12.00,
                    'currency': 'USD',
                    'billing_cycle': 'monthly',
                    'start_date': date.today() - timedelta(days=60),
                    'end_date': date.today() - timedelta(days=5),
                    'server_ip': '*************',
                    'port': 22,
                    'username': 'root',
                    'region': 'nyc3',
                    'priority': 'medium',
                    'status': 'expired'
                },
                {
                    'user_id': 1,  # admin
                    'provider_id': 5,  # Vultr
                    'name': 'خادم الإنتاج',
                    'description': 'خادم الإنتاج الرئيسي',
                    'service_type': 'compute',
                    'price': 160.00,
                    'currency': 'USD',
                    'billing_cycle': 'monthly',
                    'start_date': date.today() - timedelta(days=10),
                    'end_date': date.today() + timedelta(days=350),
                    'server_ip': '************',
                    'port': 443,
                    'username': 'admin',
                    'region': 'tokyo',
                    'priority': 'high',
                    'status': 'active'
                }
            ]

            for sub_data in subscriptions_data:
                subscription = Subscription(
                    user_id=sub_data['user_id'],
                    provider_id=sub_data['provider_id'],
                    name=sub_data['name'],
                    description=sub_data['description'],
                    service_type=sub_data['service_type'],
                    price=sub_data['price'],
                    currency=sub_data['currency'],
                    billing_cycle=sub_data['billing_cycle'],
                    start_date=sub_data['start_date'],
                    end_date=sub_data['end_date'],
                    server_ip=sub_data.get('server_ip'),
                    port=sub_data.get('port'),
                    username=sub_data.get('username'),
                    region=sub_data.get('region'),
                    priority=sub_data['priority'],
                    status=sub_data['status']
                )
                db.session.add(subscription)

            db.session.flush()
            print("✅ تم إنشاء الاشتراكات التجريبية")

            # إنشاء الفواتير التجريبية
            invoices_data = [
                {
                    'subscription_id': 1,
                    'user_id': 2,
                    'subtotal': 25.99,
                    'tax_rate': 10.0,
                    'discount': 0.0,
                    'currency': 'USD',
                    'issue_date': date.today() - timedelta(days=30),
                    'due_date': date.today() - timedelta(days=15),
                    'status': 'paid',
                    'paid_date': date.today() - timedelta(days=20),
                    'payment_method': 'بطاقة ائتمان'
                },
                {
                    'subscription_id': 2,
                    'user_id': 2,
                    'subtotal': 89.99,
                    'tax_rate': 10.0,
                    'discount': 5.0,
                    'currency': 'USD',
                    'issue_date': date.today() - timedelta(days=15),
                    'due_date': date.today() + timedelta(days=15),
                    'status': 'pending',
                    'payment_method': 'PayPal'
                },
                {
                    'subscription_id': 3,
                    'user_id': 3,
                    'subtotal': 15.50,
                    'tax_rate': 8.0,
                    'discount': 0.0,
                    'currency': 'USD',
                    'issue_date': date.today() - timedelta(days=10),
                    'due_date': date.today() + timedelta(days=20),
                    'status': 'pending',
                    'payment_method': 'تحويل بنكي'
                },
                {
                    'subscription_id': 5,
                    'user_id': 1,
                    'subtotal': 160.00,
                    'tax_rate': 15.0,
                    'discount': 10.0,
                    'currency': 'USD',
                    'issue_date': date.today() - timedelta(days=5),
                    'due_date': date.today() + timedelta(days=25),
                    'status': 'paid',
                    'paid_date': date.today() - timedelta(days=2),
                    'payment_method': 'بطاقة ائتمان'
                }
            ]

            for inv_data in invoices_data:
                # حساب المبالغ
                tax_amount = (inv_data['subtotal'] * inv_data['tax_rate']) / 100
                total_amount = inv_data['subtotal'] + tax_amount - inv_data['discount']

                invoice = Invoice(
                    invoice_number=generate_invoice_number(),
                    user_id=inv_data['user_id'],
                    subscription_id=inv_data['subscription_id'],
                    subtotal=inv_data['subtotal'],
                    tax_rate=inv_data['tax_rate'],
                    tax_amount=tax_amount,
                    discount=inv_data['discount'],
                    total_amount=total_amount,
                    currency=inv_data['currency'],
                    issue_date=inv_data['issue_date'],
                    due_date=inv_data['due_date'],
                    paid_date=inv_data.get('paid_date'),
                    status=inv_data['status'],
                    payment_method=inv_data['payment_method']
                )
                db.session.add(invoice)

            db.session.flush()
            print("✅ تم إنشاء الفواتير التجريبية")

            # إنشاء رسائل تجريبية
            messages_data = [
                {
                    'sender_id': 1,
                    'recipient_email': '<EMAIL>',
                    'recipient_name': 'أحمد محمد علي',
                    'subject': 'مرحباً بك في خدماتنا',
                    'body': 'مرحباً أحمد، نرحب بك في خدماتنا السحابية...',
                    'template_id': 1,
                    'subscription_id': 1,
                    'delivery_status': 'sent'
                },
                {
                    'sender_id': 1,
                    'recipient_email': '<EMAIL>',
                    'recipient_name': 'فاطمة أحمد حسن',
                    'subject': 'تذكير: اشتراكك ينتهي قريباً',
                    'body': 'عزيزة فاطمة، نود تذكيرك بأن اشتراكك سينتهي قريباً...',
                    'template_id': 2,
                    'subscription_id': 3,
                    'delivery_status': 'sent'
                }
            ]

            for msg_data in messages_data:
                message = Message(
                    sender_id=msg_data['sender_id'],
                    recipient_email=msg_data['recipient_email'],
                    recipient_name=msg_data['recipient_name'],
                    subject=msg_data['subject'],
                    body=msg_data['body'],
                    template_id=msg_data['template_id'],
                    subscription_id=msg_data['subscription_id'],
                    delivery_status=msg_data['delivery_status']
                )
                db.session.add(message)

            print("✅ تم إنشاء الرسائل التجريبية")

            # حفظ جميع التغييرات
            db.session.commit()
            print("✅ تم حفظ جميع البيانات التجريبية بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            db.session.rollback()
            raise e

# تشغيل التطبيق

if __name__ == '__main__':
    print("🚀 بدء تشغيل النظام المتكامل والمتطور...")
    print("=" * 60)
    print("🎯 النظام المتكامل لإدارة الاشتراكات السحابية")
    print("💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️")
    print("🏢 شركة AdenLink - اليافعي")
    print("=" * 60)

    try:
        # تهيئة قاعدة البيانات
        print("📊 تهيئة قاعدة البيانات...")
        init_database()

        print("\n✅ تم تهيئة النظام بنجاح!")
        print("=" * 60)
        print("🌐 معلومات الوصول:")
        print("🔗 الرابط: http://localhost:4020")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: 123456")
        print("=" * 60)
        print("🎮 الميزات المتاحة:")
        print("📊 • مخططات تفاعلية متطورة")
        print("📋 • إدارة الاشتراكات الشاملة")
        print("🧾 • إدارة الفواتير المتقدمة")
        print("📧 • مركز التواصل مع العملاء")
        print("👥 • إدارة المستخدمين (للمديرين)")
        print("☁️ • إدارة مزودي الخدمة")
        print("💳 • إدارة طرق الدفع")
        print("📈 • تقارير وتحليلات شاملة")
        print("🎨 • واجهة متجاوبة ومتطورة")
        print("🔐 • نظام صلاحيات متقدم")
        print("=" * 60)
        print("🚀 النظام جاهز للاستخدام!")
        print("=" * 60)

        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=4020,
            debug=True,
            threaded=True
        )

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("🔧 يرجى التحقق من:")
        print("   • تثبيت جميع المكتبات المطلوبة")
        print("   • عدم استخدام المنفذ 4020 من تطبيق آخر")
        print("   • صلاحيات الكتابة في مجلد التطبيق")

print("✅ تم إعداد النظام المتكامل والمتطور بالكامل!")
print("🎊 النظام يحتوي على جميع الميزات المطلوبة:")
print("   📊 أقسام متفرعة متطورة")
print("   🎨 مخططات تفاعلية متقدمة")
print("   📧 نظام رسائل إلكترونية شامل")
print("   📈 تقارير وتحليلات متطورة")
print("   🔐 نظام صلاحيات متقدم")
print("   📱 تصميم متجاوب ومتطور")
print("   🗄️ قاعدة بيانات شاملة")
print("   ⚡ أداء محسن ومستقر")
print("🚀 النظام جاهز للتشغيل!")
