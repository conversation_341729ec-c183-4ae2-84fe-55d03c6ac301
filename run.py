#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة الاشتراكات المتطور
مطور بواسطة: المهندس محمد ياسر الجبوري
الإصدار: 1.0.0
"""

import os
import sys
from datetime import datetime
from app import app, db, User, Subscription

def print_banner():
    """طباعة شعار النظام"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                    🚀 نظام إدارة الاشتراكات المتطور 🚀                    ║
║                                                                              ║
║                    مطور بواسطة: المهندس محمد ياسر الجبوري ❤️                ║
║                              الإصدار: 1.0.0                                ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def setup_database():
    """إعداد قاعدة البيانات والبيانات الأولية"""
    try:
        # إنشاء قاعدة البيانات
        db.create_all()
        print("✅ تم إنشاء قاعدة البيانات بنجاح")

        # إنشاء المستخدم الافتراضي
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام',
                role='admin'
            )
            admin_user.set_password('123456')
            db.session.add(admin_user)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي")
        else:
            print("ℹ️  المستخدم الافتراضي موجود مسبقاً")

        # إحصائيات سريعة
        total_users = User.query.count()
        total_subscriptions = Subscription.query.count()

        print(f"📊 إحصائيات النظام:")
        print(f"   👥 عدد المستخدمين: {total_users}")
        print(f"   📋 عدد الاشتراكات: {total_subscriptions}")

        return True

    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def check_requirements():
    """التحقق من المتطلبات"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_login
        import flask_wtf
        print("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ متطلب مفقود: {e}")
        print("💡 قم بتشغيل: pip install -r requirements.txt")
        return False

def main():
    """الدالة الرئيسية"""
    print_banner()

    # التحقق من المتطلبات
    if not check_requirements():
        sys.exit(1)

    with app.app_context():
        # إعداد قاعدة البيانات
        if not setup_database():
            sys.exit(1)

        print("\n" + "="*80)
        print("🎯 معلومات تسجيل الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: 123456")
        print("="*80)

        print("\n🌐 روابط الوصول:")
        print("   🏠 الصفحة الرئيسية: http://localhost:5000")
        print("   🔐 تسجيل الدخول: http://localhost:5000/login")
        print("   📊 لوحة التحكم: http://localhost:5000/dashboard")
        print("="*80)

        print(f"\n⏰ وقت التشغيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🚀 جاري تشغيل الخادم...")
        print("⚠️  للإيقاف: اضغط Ctrl+C")
        print("="*80 + "\n")

    try:
        # تشغيل التطبيق
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False  # تجنب إعادة التشغيل المزدوج
        )
    except KeyboardInterrupt:
        print("\n\n" + "="*80)
        print("🛑 تم إيقاف الخادم بواسطة المستخدم")
        print("👋 شكراً لاستخدام نظام إدارة الاشتراكات!")
        print("="*80)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
