#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام إدارة الاشتراكات المبسط
AdenLink - العراق
النسخة المبسطة بدون لوحة التحكم المتجاوبة
"""

print("🌟 بدء تشغيل نظام إدارة الاشتراكات المبسط...")

try:
    from flask import Flask, render_template_string, request, redirect, url_for, flash, jsonify
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
    from werkzeug.security import generate_password_hash, check_password_hash
    from datetime import datetime, date, timedelta
    import uuid
    import os
    import json
    
    print("✅ تم تحميل المكتبات بنجاح")
    
    # إعداد التطبيق
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'simple-subscription-system-2024-adenlink'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///simple_subscriptions.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # إعداد قاعدة البيانات
    db = SQLAlchemy(app)
    
    # إعداد نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # نماذج قاعدة البيانات المبسطة
    class User(UserMixin, db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        password_hash = db.Column(db.String(200), nullable=False)
        role = db.Column(db.String(20), default='user')
        company = db.Column(db.String(200))
        phone = db.Column(db.String(20))
        is_active = db.Column(db.Boolean, default=True)
        last_login = db.Column(db.DateTime)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        def set_password(self, password):
            self.password_hash = generate_password_hash(password)
        
        def check_password(self, password):
            return check_password_hash(self.password_hash, password)

    class Customer(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        phone = db.Column(db.String(20))
        company = db.Column(db.String(200))
        address = db.Column(db.Text)
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.now)

    class Subscription(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        subscription_id = db.Column(db.String(50), unique=True, nullable=False)
        customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        
        service_name = db.Column(db.String(200), nullable=False)
        provider_name = db.Column(db.String(200), nullable=False)
        status = db.Column(db.String(20), default='active')
        
        start_date = db.Column(db.Date, nullable=False)
        end_date = db.Column(db.Date, nullable=False)
        
        price = db.Column(db.Float, nullable=False)
        currency = db.Column(db.String(3), default='USD')
        
        server_name = db.Column(db.String(200))
        server_ip = db.Column(db.String(45))
        notes = db.Column(db.Text)
        
        created_at = db.Column(db.DateTime, default=datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
        
        customer = db.relationship('Customer', backref='subscriptions')
        user = db.relationship('User', backref='subscriptions')

    print("✅ تم إعداد النماذج المبسطة بنجاح")
    
    # دوال مساعدة مبسطة
    def generate_subscription_id():
        return f"SUB-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
    
    def get_simple_stats():
        try:
            stats = {
                'total_customers': Customer.query.filter_by(is_active=True).count(),
                'total_subscriptions': Subscription.query.count(),
                'active_subscriptions': Subscription.query.filter_by(status='active').count(),
                'expired_subscriptions': Subscription.query.filter_by(status='expired').count(),
                'monthly_revenue': db.session.query(db.func.sum(Subscription.price)).filter(
                    Subscription.status == 'active'
                ).scalar() or 0,
            }
            return stats
        except Exception as e:
            print(f"خطأ في حساب الإحصائيات: {e}")
            return {
                'total_customers': 0,
                'total_subscriptions': 0,
                'active_subscriptions': 0,
                'expired_subscriptions': 0,
                'monthly_revenue': 0
            }
    
    print("✅ تم إعداد الدوال المساعدة المبسطة بنجاح")
    
    # قالب تسجيل الدخول المبسط
    LOGIN_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🚀 تسجيل الدخول - نظام إدارة الاشتراكات المبسط</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
            }
            
            .login-container {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 20px;
                padding: 3rem;
                width: 100%;
                max-width: 400px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            }
            
            .logo {
                text-align: center;
                margin-bottom: 2rem;
            }
            
            .logo i {
                font-size: 3rem;
                color: #00d4ff;
                margin-bottom: 1rem;
            }
            
            .form-control {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 1rem;
                color: white;
                margin-bottom: 1rem;
            }
            
            .form-control::placeholder {
                color: rgba(255, 255, 255, 0.7);
            }
            
            .form-control:focus {
                background: rgba(255, 255, 255, 0.15);
                border-color: #00d4ff;
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
                color: white;
            }
            
            .btn-login {
                background: linear-gradient(135deg, #00d4ff, #b537f2);
                border: none;
                border-radius: 15px;
                padding: 1rem;
                width: 100%;
                font-weight: 600;
                color: white;
                transition: all 0.3s ease;
            }
            
            .btn-login:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
            }
            
            .alert {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                color: white;
                margin-bottom: 1rem;
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <div class="logo">
                <i class="fas fa-rocket"></i>
                <h2>نظام إدارة الاشتراكات المبسط</h2>
                <p>AdenLink - العراق</p>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST">
                <input type="text" class="form-control" name="username" required 
                       placeholder="اسم المستخدم" autocomplete="username">
                
                <input type="password" class="form-control" name="password" required 
                       placeholder="كلمة المرور" autocomplete="current-password">
                
                <button type="submit" class="btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </button>
            </form>
            
            <div style="text-align: center; margin-top: 2rem; padding: 1rem; background: rgba(255, 255, 255, 0.1); border-radius: 10px;">
                <small>
                    👤 المستخدم: <strong>admin</strong><br>
                    🔑 كلمة المرور: <strong>123456</strong>
                </small>
            </div>
        </div>
    </body>
    </html>
    '''

    # قالب الصفحة الرئيسية المبسطة
    MAIN_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>الصفحة الرئيسية - نظام إدارة الاشتراكات المبسط</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: white;
                padding: 2rem;
            }

            .navbar {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                margin-bottom: 2rem;
                padding: 1rem 2rem;
            }

            .navbar-brand {
                color: white !important;
                font-weight: 700;
                font-size: 1.5rem;
            }

            .nav-link {
                color: rgba(255, 255, 255, 0.8) !important;
                font-weight: 500;
                margin: 0 0.5rem;
                padding: 0.5rem 1rem !important;
                border-radius: 10px;
                transition: all 0.3s ease;
            }

            .nav-link:hover {
                background: rgba(255, 255, 255, 0.2);
                color: white !important;
            }

            .btn-logout {
                background: linear-gradient(135deg, #ff006e, #b537f2);
                border: none;
                border-radius: 10px;
                padding: 0.5rem 1rem;
                color: white;
                text-decoration: none;
                transition: all 0.3s ease;
            }

            .btn-logout:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(255, 0, 110, 0.3);
                color: white;
                text-decoration: none;
            }

            .main-container {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 20px;
                padding: 3rem;
                text-align: center;
                max-width: 800px;
                margin: 0 auto;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            }

            .stats-container {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1.5rem;
                margin: 2rem 0;
            }

            .stat-card {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 1.5rem;
                transition: all 0.3s ease;
            }

            .stat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            }

            .stat-number {
                font-size: 2rem;
                font-weight: 800;
                color: #00d4ff;
                margin-bottom: 0.5rem;
            }

            .stat-label {
                font-size: 1rem;
                color: rgba(255, 255, 255, 0.8);
            }

            .welcome-section {
                margin-bottom: 2rem;
            }

            .welcome-title {
                font-size: 2.5rem;
                font-weight: 800;
                margin-bottom: 1rem;
                color: white;
            }

            .welcome-subtitle {
                font-size: 1.2rem;
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 2rem;
            }

            .features-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-top: 2rem;
            }

            .feature-card {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 2rem;
                text-align: center;
                transition: all 0.3s ease;
            }

            .feature-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            }

            .feature-icon {
                font-size: 3rem;
                color: #00d4ff;
                margin-bottom: 1rem;
            }

            .feature-title {
                font-size: 1.3rem;
                font-weight: 600;
                margin-bottom: 1rem;
                color: white;
            }

            .feature-description {
                color: rgba(255, 255, 255, 0.8);
                line-height: 1.6;
            }

            @media (max-width: 768px) {
                body {
                    padding: 1rem;
                }

                .navbar {
                    padding: 1rem;
                }

                .main-container {
                    padding: 2rem;
                }

                .welcome-title {
                    font-size: 2rem;
                }

                .stats-container {
                    grid-template-columns: 1fr;
                }

                .features-grid {
                    grid-template-columns: 1fr;
                }
            }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg">
            <div class="container-fluid">
                <a class="navbar-brand" href="{{ url_for('main') }}">
                    <i class="fas fa-rocket me-2"></i>نظام إدارة الاشتراكات المبسط
                </a>

                <div class="navbar-nav ms-auto">
                    {% if current_user.is_authenticated %}
                        <span class="nav-link">مرحباً، {{ current_user.full_name }}</span>
                        <a href="{{ url_for('logout') }}" class="btn-logout">
                            <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                        </a>
                    {% endif %}
                </div>
            </div>
        </nav>

        <div class="main-container">
            <div class="welcome-section">
                <h1 class="welcome-title">🎉 مرحباً بك في النظام المبسط</h1>
                <p class="welcome-subtitle">نظام إدارة الاشتراكات بدون لوحة التحكم المعقدة</p>
            </div>

            {% if current_user.is_authenticated %}
            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-number">{{ stats.total_customers }}</div>
                    <div class="stat-label">إجمالي العملاء</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.total_subscriptions }}</div>
                    <div class="stat-label">إجمالي الاشتراكات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.active_subscriptions }}</div>
                    <div class="stat-label">اشتراكات نشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${{ "%.0f"|format(stats.monthly_revenue) }}</div>
                    <div class="stat-label">الإيرادات الشهرية</div>
                </div>
            </div>
            {% endif %}

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="feature-title">إدارة العملاء</h3>
                    <p class="feature-description">إدارة بسيطة وفعالة لجميع العملاء والمشتركين</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <h3 class="feature-title">إدارة الاشتراكات</h3>
                    <p class="feature-description">متابعة وإدارة جميع الاشتراكات بطريقة مبسطة</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">إحصائيات بسيطة</h3>
                    <p class="feature-description">عرض الإحصائيات الأساسية بشكل واضح ومفهوم</p>
                </div>
            </div>

            <div style="margin-top: 3rem; padding: 2rem; background: rgba(0, 212, 255, 0.1); border-radius: 15px; border: 2px solid rgba(0, 212, 255, 0.3);">
                <h4 style="color: #00d4ff; margin-bottom: 1rem;">✅ النظام المبسط</h4>
                <p style="margin: 0; color: rgba(255, 255, 255, 0.9);">
                    تم حذف لوحة التحكم المعقدة والاحتفاظ بالوظائف الأساسية فقط
                </p>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

    # المسارات المبسطة
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('main'))
        return redirect(url_for('login'))

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')

            user = User.query.filter_by(username=username).first()

            if user and user.check_password(password):
                login_user(user)
                user.last_login = datetime.now()
                db.session.commit()

                flash('🎉 مرحباً بك في النظام المبسط! تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('main'))
            else:
                flash('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

        return render_template_string(LOGIN_TEMPLATE)

    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('✅ تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('login'))

    @app.route('/main')
    @login_required
    def main():
        try:
            stats = get_simple_stats()
            return render_template_string(MAIN_TEMPLATE, stats=stats)
        except Exception as e:
            print(f"خطأ في الصفحة الرئيسية: {e}")
            flash(f'حدث خطأ: {str(e)}', 'error')
            return render_template_string(MAIN_TEMPLATE, stats={
                'total_customers': 0,
                'total_subscriptions': 0,
                'active_subscriptions': 0,
                'monthly_revenue': 0
            })

    print("✅ تم إعداد المسارات المبسطة بنجاح")

    # تهيئة قاعدة البيانات المبسطة
    def init_simple_database():
        with app.app_context():
            try:
                db.create_all()
                print("✅ تم إنشاء جداول قاعدة البيانات المبسطة بنجاح")

                # إنشاء مستخدم مدير إذا لم يكن موجوداً
                admin = User.query.filter_by(username='admin').first()
                if not admin:
                    admin = User(
                        username='admin',
                        email='<EMAIL>',
                        full_name='المدير العام - النظام المبسط',
                        role='admin',
                        company='AdenLink - العراق',
                        phone='+964-XXX-XXXX'
                    )
                    admin.set_password('123456')
                    db.session.add(admin)

                    # إنشاء بيانات تجريبية مبسطة
                    create_simple_sample_data()

                    db.session.commit()
                    print("✅ تم إنشاء المستخدم المدير والبيانات المبسطة")
                else:
                    print("✅ المستخدم المدير موجود مسبقاً")

            except Exception as e:
                print(f"❌ خطأ في تهيئة قاعدة البيانات المبسطة: {e}")
                db.session.rollback()

    def create_simple_sample_data():
        """إنشاء بيانات تجريبية مبسطة"""
        try:
            # إنشاء عملاء تجريبيين
            customers = [
                Customer(
                    name='شركة التقنيات البسيطة',
                    email='<EMAIL>',
                    phone='+964-************',
                    company='شركة التقنيات البسيطة المحدودة',
                    address='بغداد، العراق'
                ),
                Customer(
                    name='مؤسسة الحلول السهلة',
                    email='<EMAIL>',
                    phone='+964-************',
                    company='مؤسسة الحلول السهلة',
                    address='أربيل، العراق'
                ),
                Customer(
                    name='شركة الخدمات المبسطة',
                    email='<EMAIL>',
                    phone='+964-************',
                    company='شركة الخدمات المبسطة',
                    address='البصرة، العراق'
                ),
            ]

            for customer in customers:
                db.session.add(customer)

            db.session.flush()

            # إنشاء اشتراكات تجريبية
            subscriptions = [
                Subscription(
                    subscription_id=generate_subscription_id(),
                    customer_id=1,
                    user_id=1,
                    service_name='خدمة الاستضافة الأساسية',
                    provider_name='AdenLink',
                    status='active',
                    start_date=date.today(),
                    end_date=date.today() + timedelta(days=365),
                    price=50.0,
                    currency='USD',
                    server_name='server-01.adenlink.com',
                    server_ip='*************',
                    notes='اشتراك أساسي للاستضافة'
                ),
                Subscription(
                    subscription_id=generate_subscription_id(),
                    customer_id=2,
                    user_id=1,
                    service_name='خدمة النسخ الاحتياطي',
                    provider_name='AdenLink',
                    status='active',
                    start_date=date.today(),
                    end_date=date.today() + timedelta(days=180),
                    price=25.0,
                    currency='USD',
                    server_name='backup-01.adenlink.com',
                    server_ip='*************',
                    notes='خدمة النسخ الاحتياطي اليومي'
                ),
                Subscription(
                    subscription_id=generate_subscription_id(),
                    customer_id=3,
                    user_id=1,
                    service_name='خدمة البريد الإلكتروني',
                    provider_name='AdenLink',
                    status='expired',
                    start_date=date.today() - timedelta(days=365),
                    end_date=date.today() - timedelta(days=30),
                    price=15.0,
                    currency='USD',
                    server_name='mail-01.adenlink.com',
                    server_ip='*************',
                    notes='خدمة البريد الإلكتروني المؤسسي'
                ),
            ]

            for subscription in subscriptions:
                db.session.add(subscription)

            print("✅ تم إنشاء البيانات التجريبية المبسطة بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات المبسطة: {e}")
            db.session.rollback()

    # تشغيل النظام المبسط
    if __name__ == '__main__':
        print("🔄 تهيئة قاعدة البيانات المبسطة...")
        init_simple_database()

        print("=" * 80)
        print("🎉 نظام إدارة الاشتراكات المبسط جاهز للتشغيل!")
        print("=" * 80)
        print("🌟 معلومات الوصول:")
        print("   🔗 الرابط: http://localhost:5092")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: 123456")
        print()
        print("✅ الميزات المبسطة:")
        print("   🚫 تم حذف لوحة التحكم المعقدة")
        print("   ✅ صفحة رئيسية بسيطة وواضحة")
        print("   ✅ إحصائيات أساسية فقط")
        print("   ✅ تصميم مبسط وسهل الاستخدام")
        print("   ✅ أداء سريع ومحسن")
        print("   ✅ واجهة نظيفة ومرتبة")
        print()
        print("🎯 التبسيطات المطبقة:")
        print("   ❌ لا توجد لوحة تحكم معقدة")
        print("   ❌ لا توجد قائمة جانبية")
        print("   ❌ لا توجد تأثيرات معقدة")
        print("   ❌ لا توجد إحصائيات متقدمة")
        print("   ✅ فقط الوظائف الأساسية")
        print("=" * 80)
        print("🚀 بدء تشغيل الخادم المبسط...")

        try:
            app.run(
                debug=True,
                host='0.0.0.0',
                port=5092,
                threaded=True
            )
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف النظام المبسط بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل الخادم المبسط: {e}")

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("\n📦 يرجى تثبيت المكتبات المطلوبة:")
    print("pip install flask flask-sqlalchemy flask-login werkzeug")
    print("\n💡 أو استخدم الأمر التالي لتثبيت جميع المتطلبات:")
    print("pip install flask flask-sqlalchemy flask-login werkzeug")
    print("\n🔧 إذا كنت تستخدم Python 3.12+ قد تحتاج:")
    print("pip install --upgrade flask flask-sqlalchemy flask-login werkzeug")
    print("\n✅ هذا النظام المبسط يحتوي على:")
    print("   🚫 لا توجد لوحة تحكم معقدة")
    print("   ✅ صفحة رئيسية بسيطة")
    print("   ✅ إحصائيات أساسية فقط")
    print("   ✅ تصميم مبسط وسريع")

    input("\n⏸️ اضغط Enter للخروج...")
    exit(1)

except Exception as e:
    print(f"❌ خطأ في إعداد النظام المبسط: {e}")
    import traceback
    traceback.print_exc()

    print("\n🔍 نصائح لحل المشاكل:")
    print("1. تأكد من تثبيت جميع المكتبات المطلوبة")
    print("2. تحقق من إصدار Python (يُفضل 3.8+)")
    print("3. تأكد من أن المنفذ 5092 متاح")
    print("4. جرب إعادة تشغيل النظام")
    print("5. احذف ملف قاعدة البيانات وأعد التشغيل")

    input("\n⏸️ اضغط Enter للخروج...")
    exit(1)

print("\n⏹️ تم إيقاف النظام المبسط")
print("🙏 شكراً لاستخدام نظام إدارة الاشتراكات المبسط!")
print("✨ النظام مبسط وسريع بدون تعقيدات")
print("🚫 تم حذف لوحة التحكم المعقدة كما طلبت")
input("اضغط Enter للخروج...")
