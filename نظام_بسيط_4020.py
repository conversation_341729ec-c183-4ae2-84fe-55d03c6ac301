#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 النظام البسيط - البورت 4020
مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
شركة AdenLink - اليافعي
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta

print("🚀 بدء تشغيل النظام البسيط على البورت 4020...")

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'simple-system-4020'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///simple_4020.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# نماذج قاعدة البيانات البسيطة

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user', nullable=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    price = db.Column(db.Float, nullable=False, default=0.0)
    status = db.Column(db.String(20), default='active')
    
    user = db.relationship('User', backref='subscriptions')

# قوالب HTML

LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - النظام البسيط البورت 4020</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-family: 'Arial', sans-serif;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }
        .system-title {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 1rem;
            text-align: center;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .port-info {
            background: rgba(255, 0, 110, 0.2);
            border: 2px solid rgba(255, 0, 110, 0.5);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            text-align: center;
            color: #ff006e;
            font-weight: 700;
            font-size: 1.2rem;
        }
        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            color: white;
            box-shadow: none;
        }
        .btn-login {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            width: 100%;
            margin-bottom: 1rem;
        }
        .demo-info {
            background: rgba(0, 212, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1 class="system-title">النظام البسيط المتطور</h1>
        
        <div class="port-info">
            🌐 البورت 4020 - يعمل بنجاح!
        </div>
        
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <input type="text" class="form-control" name="username" placeholder="اسم المستخدم" required>
            <input type="password" class="form-control" name="password" placeholder="كلمة المرور" required>
            <button type="submit" class="btn-login">تسجيل الدخول</button>
        </form>
        
        <div class="demo-info">
            <div><strong>بيانات التجربة:</strong></div>
            <div>المستخدم: <strong>admin</strong></div>
            <div>كلمة المرور: <strong>123456</strong></div>
            <div class="mt-2">
                <small>🔗 http://localhost:4020</small>
            </div>
        </div>
        
        <div class="text-center mt-3">
            <small>💻 مطور بواسطة: محمد ياسر الجبوري ❤️</small><br>
            <small>🏢 شركة AdenLink - اليافعي</small>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - النظام البسيط البورت 4020</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 2rem;
        }
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .port-badge {
            background: linear-gradient(135deg, #ff006e, #b537f2);
            padding: 0.8rem 1.5rem;
            border-radius: 15px;
            font-weight: 700;
            font-size: 1.1rem;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.3);
        }
        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }
        .stat-title {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
        }
        .chart-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin-top: 1rem;
        }
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        .nav-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
        }
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.3);
            color: white;
            text-decoration: none;
        }
        .nav-icon {
            font-size: 3rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .btn-logout {
            background: linear-gradient(135deg, #ff006e, #b537f2);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="port-badge">
            🌐 النظام يعمل على البورت 4020
        </div>
        <h1 class="page-title">لوحة التحكم المتطورة</h1>
        <p class="lead">مرحباً {{ current_user.full_name }} في النظام البسيط المتطور</p>
        <a href="{{ url_for('logout') }}" class="btn-logout">
            تسجيل الخروج
        </a>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ subscriptions_count }}</div>
            <div class="stat-title">إجمالي الاشتراكات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ active_count }}</div>
            <div class="stat-title">الاشتراكات النشطة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ users_count }}</div>
            <div class="stat-title">المستخدمين</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">4020</div>
            <div class="stat-title">رقم البورت</div>
        </div>
    </div>
    
    <div class="chart-section">
        <h3 class="text-center">مخطط الاشتراكات التفاعلي</h3>
        <div class="chart-container">
            <canvas id="subscriptionChart"></canvas>
        </div>
    </div>
    
    <div class="nav-grid">
        <a href="{{ url_for('subscriptions') }}" class="nav-card">
            <div class="nav-icon">📋</div>
            <h4>إدارة الاشتراكات</h4>
            <p>عرض وإدارة جميع الاشتراكات</p>
        </a>
        
        <div class="nav-card">
            <div class="nav-icon">📊</div>
            <h4>التقارير والإحصائيات</h4>
            <p>تحليلات شاملة ومتطورة</p>
        </div>
        
        <div class="nav-card">
            <div class="nav-icon">⚙️</div>
            <h4>الإعدادات</h4>
            <p>إعدادات النظام والتخصيص</p>
        </div>
    </div>
    
    <script>
        // مخطط الاشتراكات
        const ctx = document.getElementById('subscriptionChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'غير نشط'],
                datasets: [{
                    data: [{{ active_count }}, {{ subscriptions_count - active_count }}],
                    backgroundColor: [
                        'rgba(0, 212, 255, 0.8)',
                        'rgba(255, 0, 110, 0.8)'
                    ],
                    borderColor: ['#00d4ff', '#ff006e'],
                    borderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: 'white',
                            padding: 20,
                            font: { size: 16 }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
'''

SUBSCRIPTIONS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>الاشتراكات - النظام البسيط البورت 4020</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            font-family: 'Arial', sans-serif;
            padding: 2rem;
        }
        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .subscription-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .subscription-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
        }
        .back-btn {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <h1>📋 قائمة الاشتراكات</h1>
        <div style="background: rgba(255, 0, 110, 0.2); border: 2px solid rgba(255, 0, 110, 0.5); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin: 1rem 0; color: #ff006e; font-weight: 700;">
            🌐 البورت 4020
        </div>
        <br>
        <a href="{{ url_for('dashboard') }}" class="back-btn">العودة للوحة التحكم</a>
    </div>
    
    <div class="row">
        {% for subscription in subscriptions %}
        <div class="col-md-6 col-lg-4">
            <div class="subscription-card">
                <h5 style="color: #00d4ff;">{{ subscription.name }}</h5>
                <p><strong>السعر:</strong> {{ subscription.price }} USD</p>
                <p><strong>الحالة:</strong> 
                    <span style="background: {% if subscription.status == 'active' %}#28a745{% else %}#dc3545{% endif %}; padding: 0.3rem 0.8rem; border-radius: 10px; font-size: 0.8rem;">
                        {{ subscription.status }}
                    </span>
                </p>
                <p><strong>المستخدم:</strong> {{ subscription.user.full_name }}</p>
            </div>
        </div>
        {% endfor %}
    </div>
</body>
</html>
'''

# المسارات

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            flash('🎉 مرحباً بك في النظام البسيط على البورت 4020!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('✅ تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    subscriptions_count = Subscription.query.count()
    active_count = Subscription.query.filter_by(status='active').count()
    users_count = User.query.count()

    return render_template_string(DASHBOARD_TEMPLATE,
                                subscriptions_count=subscriptions_count,
                                active_count=active_count,
                                users_count=users_count)

@app.route('/subscriptions')
@login_required
def subscriptions():
    if current_user.is_admin():
        subscriptions = Subscription.query.all()
    else:
        subscriptions = Subscription.query.filter_by(user_id=current_user.id).all()

    return render_template_string(SUBSCRIPTIONS_TEMPLATE, subscriptions=subscriptions)

# تهيئة قاعدة البيانات
def init_database():
    with app.app_context():
        try:
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات")

            if User.query.count() > 0:
                print("✅ البيانات موجودة مسبقاً")
                return

            # إنشاء المستخدمين
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام - البورت 4020',
                role='admin'
            )
            admin.set_password('123456')
            db.session.add(admin)

            user1 = User(
                username='user1',
                email='<EMAIL>',
                full_name='أحمد محمد علي',
                role='user'
            )
            user1.set_password('123456')
            db.session.add(user1)

            db.session.flush()
            print("✅ تم إنشاء المستخدمين")

            # إنشاء الاشتراكات
            subscriptions_data = [
                (1, 'خادم AWS الأساسي', 25.99, 'active'),
                (1, 'قاعدة بيانات Azure', 89.99, 'active'),
                (2, 'تخزين Google Cloud', 15.50, 'active'),
                (2, 'خادم DigitalOcean', 12.00, 'inactive')
            ]

            for user_id, name, price, status in subscriptions_data:
                subscription = Subscription(
                    user_id=user_id,
                    name=name,
                    price=price,
                    status=status
                )
                db.session.add(subscription)

            db.session.commit()
            print("✅ تم إنشاء البيانات التجريبية")

        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            db.session.rollback()

# تشغيل التطبيق
if __name__ == '__main__':
    print("🚀 بدء تشغيل النظام البسيط على البورت 4020...")
    print("=" * 60)
    print("🎯 النظام البسيط المتطور")
    print("💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️")
    print("🏢 شركة AdenLink - اليافعي")
    print("🌐 البورت: 4020")
    print("=" * 60)

    try:
        print("📊 تهيئة قاعدة البيانات...")
        init_database()

        print("\n✅ تم تهيئة النظام بنجاح!")
        print("=" * 60)
        print("🌐 معلومات الوصول:")
        print("🔗 الرابط: http://localhost:4020")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: 123456")
        print("=" * 60)
        print("🎮 الميزات المتاحة:")
        print("📊 • لوحة تحكم متطورة مع مخطط تفاعلي")
        print("📋 • إدارة الاشتراكات")
        print("🎨 • واجهة متجاوبة ومتطورة")
        print("🔐 • نظام تسجيل دخول آمن")
        print("⚡ • أداء سريع على البورت 4020")
        print("=" * 60)
        print("🚀 النظام جاهز للاستخدام!")
        print("=" * 60)

        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=4020,
            debug=False,
            threaded=True
        )

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")

print("✅ تم إعداد النظام البسيط بالكامل للبورت 4020!")
