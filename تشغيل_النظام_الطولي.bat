@echo off
chcp 65001 >nul
title النظام الطولي المتطور - محمد ياسر الجبوري

echo.
echo ================================================================================
echo 🚀 النظام الطولي المتطور لإدارة الاشتراكات السحابية
echo ================================================================================
echo 💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
echo 🏢 شركة AdenLink - العراق 🇮🇶
echo 📅 تاريخ التطوير: 2024
echo 🎨 التصميم: طولي مع قائمة جانبية
echo ================================================================================
echo.

echo 📋 التحقق من متطلبات النظام...
echo.

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
echo.

echo 📦 تثبيت المكتبات المطلوبة...
echo.

REM تثبيت المكتبات
pip install flask flask-sqlalchemy flask-login werkzeug >nul 2>&1

echo ✅ تم تثبيت جميع المكتبات بنجاح
echo.

echo 🔥 بدء تشغيل النظام الطولي المتطور...
echo.
echo ================================================================================
echo 🌐 معلومات الوصول:
echo ================================================================================
echo 🔗 الرابط: http://localhost:5000
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: 123456
echo ================================================================================
echo 🎨 التصميم الجديد - الطولي:
echo ================================================================================
echo 📋 • قائمة جانبية ثابتة مع روابط سريعة
echo 📊 • الإحصائيات مرتبة بشكل طولي (عمودي)
echo 📋 • بطاقات الاشتراكات بتخطيط أفقي طولي
echo 👥 • بطاقات المستخدمين بتخطيط أفقي طولي
echo 🧾 • الفواتير مرتبة بشكل عمودي
echo ☁️ • مزودي الخدمة بتخطيط طولي
echo 🎯 • كل عنصر تحت الآخر بشكل منظم
echo 📱 • متجاوب مع جميع الأجهزة
echo ================================================================================
echo 🎮 الميزات الطولية المتاحة:
echo ================================================================================
echo 📊 • لوحة تحكم طولية مع إحصائيات مفصلة
echo    - كل إحصائية في سطر منفصل
echo    - أيقونات ووصف تفصيلي
echo    - إجراءات سريعة مرتبة عمودياً
echo 📋 • إدارة الاشتراكات الطولية
echo    - بطاقات أفقية طويلة
echo    - معلومات مرتبة في شبكة
echo    - أزرار الإجراءات عمودية
echo 👥 • إدارة المستخدمين الطولية
echo    - عرض أفقي للمستخدمين
echo    - معلومات مفصلة في كل بطاقة
echo    - ترتيب عمودي للبطاقات
echo 🧾 • إدارة الفواتير الطولية
echo 📊 • مخططات تفاعلية متطورة
echo 📋 • كشف حساب العملاء
echo ☁️ • إدارة مزودي الخدمة (للمديرين)
echo 💳 • إدارة طرق الدفع (للمديرين)
echo ================================================================================
echo 🎯 مميزات التصميم الطولي:
echo ================================================================================
echo ✅ • ترتيب عمودي للعناصر الرئيسية
echo ✅ • بطاقات أفقية طويلة للمحتوى
echo ✅ • قائمة جانبية ثابتة للتنقل
echo ✅ • استغلال أفضل للمساحة العمودية
echo ✅ • سهولة القراءة والتصفح
echo ✅ • تنظيم أفضل للمعلومات
echo ✅ • تجربة مستخدم محسنة
echo ✅ • تصميم عصري ومتطور
echo ================================================================================
echo 👥 المستخدمين التجريبيين:
echo ================================================================================
echo 🔹 admin / 123456 (مدير)
echo 🔹 user1 / 123456 (مستخدم)
echo 🔹 user2 / 123456 (مستخدم)
echo 🔹 user3 / 123456 (مستخدم - معطل)
echo ================================================================================
echo 🚀 النظام الطولي جاهز للاستخدام!
echo ================================================================================
echo.

REM تشغيل النظام
python نظام_الاشتراكات_الشامل.py

echo.
echo ================================================================================
echo 🛑 تم إيقاف النظام
echo ================================================================================
echo 💡 لإعادة التشغيل، قم بتشغيل هذا الملف مرة أخرى
echo 📞 للدعم التقني: محمد ياسر الجبوري
echo 🏢 شركة AdenLink - العراق 🇮🇶
echo 🌐 الرابط: http://localhost:5000
echo 🎨 التصميم: طولي مع قائمة جانبية
echo ================================================================================
pause
