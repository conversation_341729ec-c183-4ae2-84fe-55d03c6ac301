#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات المتقدم - النسخة العاملة
"""

print("🚀 بدء تشغيل نظام إدارة الاشتراكات المتقدم...")

try:
    from flask import Flask, render_template_string, request, redirect, url_for, flash, jsonify
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
    from werkzeug.security import generate_password_hash, check_password_hash
    from datetime import datetime, date, timedelta
    import uuid
    import os
    
    print("✅ تم تحميل المكتبات بنجاح")
    
    # إعداد التطبيق
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'working-advanced-subscription-manager-2024'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///working_advanced_subscriptions.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # إعداد قاعدة البيانات
    db = SQLAlchemy(app)
    
    # إعداد نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # نماذج قاعدة البيانات
    class User(UserMixin, db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        password_hash = db.Column(db.String(200), nullable=False)
        role = db.Column(db.String(20), default='user')
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        def set_password(self, password):
            self.password_hash = generate_password_hash(password)
        
        def check_password(self, password):
            return check_password_hash(self.password_hash, password)

    class Customer(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        phone = db.Column(db.String(20))
        company = db.Column(db.String(200))
        created_at = db.Column(db.DateTime, default=datetime.now)

    class Service(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        description = db.Column(db.Text)
        price = db.Column(db.Float, nullable=False)
        billing_cycle = db.Column(db.String(20), default='monthly')
        created_at = db.Column(db.DateTime, default=datetime.now)

    class Subscription(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
        service_id = db.Column(db.Integer, db.ForeignKey('service.id'), nullable=False)
        subscription_id = db.Column(db.String(50), unique=True, nullable=False)
        status = db.Column(db.String(20), default='active')
        start_date = db.Column(db.Date, nullable=False)
        end_date = db.Column(db.Date, nullable=False)
        price = db.Column(db.Float, nullable=False)
        server_ip = db.Column(db.String(45))
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        customer = db.relationship('Customer', backref='subscriptions')
        service = db.relationship('Service', backref='subscriptions')

    class Invoice(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        invoice_number = db.Column(db.String(50), unique=True, nullable=False)
        customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
        amount = db.Column(db.Float, nullable=False)
        total_amount = db.Column(db.Float, nullable=False)
        status = db.Column(db.String(20), default='pending')
        due_date = db.Column(db.Date, nullable=False)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        customer = db.relationship('Customer', backref='invoices')

    print("✅ تم إعداد النماذج بنجاح")
    
    # دوال مساعدة
    def generate_subscription_id():
        return f"SUB-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
    
    def generate_invoice_number():
        return f"INV-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
    
    def get_dashboard_stats():
        stats = {
            'total_customers': Customer.query.count(),
            'total_subscriptions': Subscription.query.count(),
            'active_subscriptions': Subscription.query.filter_by(status='active').count(),
            'total_invoices': Invoice.query.count(),
            'pending_invoices': Invoice.query.filter_by(status='pending').count(),
            'monthly_revenue': db.session.query(db.func.sum(Invoice.total_amount)).filter(
                Invoice.created_at >= date.today().replace(day=1)
            ).scalar() or 0,
            'expiring_soon': Subscription.query.filter(
                Subscription.status == 'active',
                Subscription.end_date <= date.today() + timedelta(days=7)
            ).count()
        }
        return stats
    
    print("✅ تم إعداد الدوال المساعدة بنجاح")

    # قوالب HTML مبسطة وعاملة
    LOGIN_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - نظام إدارة الاشتراكات المتقدم</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .login-container {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 25px;
                padding: 3rem;
                width: 100%;
                max-width: 450px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
            .logo-section {
                text-align: center;
                margin-bottom: 2rem;
            }
            .logo-icon {
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1rem;
                color: white;
                font-size: 2rem;
                box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            }
            .system-title {
                color: #2c3e50;
                font-weight: 700;
                font-size: 1.8rem;
                margin-bottom: 0.5rem;
            }
            .system-subtitle {
                color: #7f8c8d;
                font-size: 1rem;
                margin-bottom: 0;
            }
            .form-control {
                border: 2px solid #e9ecef;
                border-radius: 15px;
                padding: 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: rgba(255, 255, 255, 0.9);
            }
            .form-control:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
                background: white;
            }
            .btn-login {
                background: linear-gradient(135deg, #667eea, #764ba2);
                border: none;
                border-radius: 15px;
                padding: 1rem 2rem;
                font-weight: 600;
                font-size: 1.1rem;
                width: 100%;
                color: white;
                transition: all 0.3s ease;
                margin-top: 1rem;
            }
            .btn-login:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
                color: white;
            }
            .alert {
                border-radius: 15px;
                border: none;
                margin-bottom: 1.5rem;
            }
            .login-info {
                background: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 12px;
                padding: 1rem;
                margin-top: 1.5rem;
                text-align: center;
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-cloud"></i>
                </div>
                <h1 class="system-title">نظام إدارة الاشتراكات المتقدم</h1>
                <p class="system-subtitle">AdenLink - العراق</p>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }}">
                            <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST">
                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-user me-2"></i>اسم المستخدم
                    </label>
                    <input type="text" class="form-control" name="username" required
                           placeholder="أدخل اسم المستخدم" autocomplete="username">
                </div>

                <div class="mb-3">
                    <label class="form-label">
                        <i class="fas fa-lock me-2"></i>كلمة المرور
                    </label>
                    <input type="password" class="form-control" name="password" required
                           placeholder="أدخل كلمة المرور" autocomplete="current-password">
                </div>

                <button type="submit" class="btn btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </button>
            </form>

            <div class="login-info">
                <small style="color: #0c5460; font-weight: 500;">
                    <i class="fas fa-info-circle me-1"></i>
                    <strong>بيانات التجربة:</strong><br>
                    المستخدم: <code>admin</code> | كلمة المرور: <code>123456</code>
                </small>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

    # قالب لوحة التحكم المبسط
    DASHBOARD_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>لوحة التحكم - نظام إدارة الاشتراكات المتقدم</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            :root {
                --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                --sidebar-width: 320px;
            }

            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                min-height: 100vh;
                color: #2c3e50;
                overflow-x: hidden;
            }

            /* القائمة الجانبية */
            .sidebar {
                position: fixed;
                right: 0;
                top: 0;
                width: var(--sidebar-width);
                height: 100vh;
                background: linear-gradient(180deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
                backdrop-filter: blur(20px);
                border-left: 1px solid rgba(255,255,255,0.3);
                box-shadow: -10px 0 30px rgba(0,0,0,0.1);
                z-index: 1000;
                overflow-y: auto;
                transition: all 0.3s ease;
            }

            .sidebar-header {
                padding: 2rem 1.5rem;
                text-align: center;
                border-bottom: 1px solid rgba(0,0,0,0.1);
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            }

            .sidebar-logo {
                width: 60px;
                height: 60px;
                background: var(--primary-gradient);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1rem;
                color: white;
                font-size: 1.5rem;
                box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            }

            .sidebar-title {
                font-size: 1.2rem;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 0.5rem;
            }

            .sidebar-subtitle {
                font-size: 0.9rem;
                color: #7f8c8d;
                margin-bottom: 0;
            }

            .sidebar-menu {
                padding: 1rem 0;
            }

            .menu-section {
                margin-bottom: 1rem;
            }

            .menu-section-title {
                padding: 1rem 1.5rem;
                font-size: 1rem;
                font-weight: 600;
                color: #2c3e50;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
                border-radius: 0 25px 25px 0;
                margin-left: 1rem;
                display: flex;
                align-items: center;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .menu-section-title:hover {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
                transform: translateX(-5px);
            }

            .menu-section-icon {
                margin-left: 1rem;
                font-size: 1.1rem;
            }

            .menu-section-description {
                font-size: 0.8rem;
                color: #7f8c8d;
                padding: 0.5rem 1.5rem 0.5rem 2rem;
                line-height: 1.4;
                margin-bottom: 0.5rem;
            }

            .menu-items {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .menu-item-link {
                display: flex;
                align-items: center;
                padding: 0.75rem 1.5rem 0.75rem 2.5rem;
                color: #5a6c7d;
                text-decoration: none;
                font-size: 0.9rem;
                font-weight: 500;
                transition: all 0.3s ease;
                border-radius: 0 25px 25px 0;
                margin-left: 1rem;
            }

            .menu-item-link:hover {
                background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1));
                color: #4facfe;
                text-decoration: none;
                transform: translateX(-5px);
            }

            .menu-item-icon {
                margin-left: 0.75rem;
                font-size: 0.9rem;
                width: 16px;
                text-align: center;
            }

            .menu-divider {
                height: 1px;
                background: linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent);
                margin: 1rem 1.5rem;
            }

            .sidebar-footer {
                position: sticky;
                bottom: 0;
                padding: 1.5rem;
                background: linear-gradient(180deg, transparent, rgba(255,255,255,0.95));
                border-top: 1px solid rgba(0,0,0,0.1);
            }

            .current-user {
                display: flex;
                align-items: center;
                padding: 1rem;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
                border-radius: 15px;
                margin-bottom: 1rem;
            }

            .user-avatar {
                width: 45px;
                height: 45px;
                border-radius: 50%;
                background: var(--primary-gradient);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 700;
                margin-left: 1rem;
                font-size: 1.1rem;
            }

            .user-info {
                flex: 1;
            }

            .user-name {
                font-weight: 600;
                color: #2c3e50;
                font-size: 0.95rem;
                margin-bottom: 0.25rem;
            }

            .user-role {
                font-size: 0.8rem;
                color: #7f8c8d;
                margin-bottom: 0;
            }

            .search-input {
                width: 100%;
                padding: 1rem 1rem 1rem 3rem;
                border: 2px solid rgba(102, 126, 234, 0.2);
                border-radius: 25px;
                background: rgba(255,255,255,0.8);
                font-size: 0.9rem;
                transition: all 0.3s ease;
            }

            .search-input:focus {
                outline: none;
                border-color: #667eea;
                background: white;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            }

            /* المحتوى الرئيسي */
            .main-content {
                margin-left: var(--sidebar-width);
                min-height: 100vh;
                padding: 2rem;
            }

            .top-header {
                background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 2rem;
                margin-bottom: 2rem;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                display: flex;
                align-items: center;
                justify-content: space-between;
                border: 1px solid rgba(255,255,255,0.3);
            }

            .header-title {
                font-size: 2rem;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 0.5rem;
            }

            .header-subtitle {
                color: #7f8c8d;
                font-size: 1.1rem;
                margin-bottom: 0;
            }

            .header-btn {
                background: var(--primary-gradient);
                color: white;
                border: none;
                border-radius: 15px;
                padding: 0.75rem 1.5rem;
                font-weight: 600;
                text-decoration: none;
                transition: all 0.3s ease;
                box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin: 0 0.5rem;
            }

            .header-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
                color: white;
                text-decoration: none;
            }

            /* إحصائيات */
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 1.5rem;
                margin-bottom: 2rem;
            }

            .stat-card {
                background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
                border-radius: 25px;
                padding: 2rem;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(255,255,255,0.3);
                backdrop-filter: blur(20px);
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .stat-card:hover {
                transform: translateY(-8px) scale(1.02);
                box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
            }

            .stat-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .stat-number {
                font-size: 2.5rem;
                font-weight: 800;
                color: #2c3e50;
                margin-bottom: 0.5rem;
                line-height: 1;
            }

            .stat-label {
                color: #5a6c7d;
                font-weight: 600;
                font-size: 1rem;
                margin-bottom: 0;
            }

            .stat-icon {
                width: 70px;
                height: 70px;
                border-radius: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 2rem;
                color: white;
                box-shadow: 0 15px 30px rgba(0,0,0,0.2);
            }

            .stat-card.primary .stat-icon { background: var(--primary-gradient); }
            .stat-card.success .stat-icon { background: var(--success-gradient); }
            .stat-card.warning .stat-icon { background: var(--warning-gradient); }
            .stat-card.danger .stat-icon { background: var(--secondary-gradient); }

            /* جداول */
            .advanced-card {
                background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
                border-radius: 25px;
                padding: 2rem;
                margin-bottom: 2rem;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(255,255,255,0.3);
                backdrop-filter: blur(20px);
            }

            .card-title {
                font-size: 1.5rem;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 1.5rem;
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .card-icon {
                width: 50px;
                height: 50px;
                border-radius: 15px;
                background: var(--primary-gradient);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 1.2rem;
            }

            .table {
                margin-bottom: 0;
            }

            .table th {
                background: #f8f9fa;
                font-weight: 600;
                color: #2c3e50;
                border: none;
                padding: 1rem;
            }

            .table td {
                padding: 1rem;
                border: none;
                border-bottom: 1px solid #f8f9fa;
            }

            .status-badge {
                padding: 0.4rem 0.8rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 600;
                text-transform: uppercase;
            }

            .status-badge.active {
                background: linear-gradient(135deg, #2ecc71, #27ae60);
                color: white;
            }

            .status-badge.pending {
                background: linear-gradient(135deg, #f39c12, #e67e22);
                color: white;
            }

            .action-btn {
                background: none;
                border: none;
                color: #7f8c8d;
                font-size: 1rem;
                padding: 0.5rem;
                border-radius: 8px;
                transition: all 0.3s ease;
                cursor: pointer;
                margin: 0 0.25rem;
            }

            .action-btn:hover {
                background: rgba(102, 126, 234, 0.1);
                color: #667eea;
            }

            /* استجابة للشاشات الصغيرة */
            @media (max-width: 1200px) {
                .sidebar {
                    transform: translateX(100%);
                }

                .sidebar.active {
                    transform: translateX(0);
                }

                .main-content {
                    margin-left: 0;
                    padding: 1rem;
                }

                .stats-grid {
                    grid-template-columns: 1fr;
                }

                .top-header {
                    flex-direction: column;
                    gap: 1rem;
                    text-align: center;
                }
            }

            .sidebar-toggle {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1001;
                background: var(--primary-gradient);
                color: white;
                border: none;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
                cursor: pointer;
                box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
                transition: all 0.3s ease;
                display: none;
            }

            @media (max-width: 1200px) {
                .sidebar-toggle {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        </style>
    </head>
    <body>
        <!-- زر إظهار/إخفاء القائمة الجانبية -->
        <button class="sidebar-toggle" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>

        <!-- القائمة الجانبية -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-cloud"></i>
                </div>
                <h3 class="sidebar-title">نظام إدارة الاشتراكات</h3>
                <p class="sidebar-subtitle">AdenLink - العراق</p>
            </div>

            <div class="sidebar-menu">
                <!-- لوحة المعلومات -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-tachometer-alt menu-section-icon"></i>
                        لوحة المعلومات
                    </div>
                    <div class="menu-section-description">
                        عرض إجمالي الإحصائيات، الاشتراكات، التنبيهات، الرسوم البيانية العامة
                    </div>
                </div>

                <div class="menu-divider"></div>

                <!-- إدارة الاشتراكات -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-server menu-section-icon"></i>
                        إدارة الاشتراكات
                    </div>
                    <div class="menu-section-description">
                        إدارة وتفصيل الاشتراكات، وتحتوي على:
                    </div>
                    <ul class="menu-items">
                        <li><a href="#" class="menu-item-link"><i class="fas fa-chart-pie menu-item-icon"></i>مخطط الاشتراكات</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-list menu-item-icon"></i>قائمة الاشتراكات</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-credit-card menu-item-icon"></i>طرق الدفع</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-plus menu-item-icon"></i>إضافة اشتراك جديد</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-chart-line menu-item-icon"></i>تحليلات الاشتراكات</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-file-alt menu-item-icon"></i>تقارير الاشتراكات</a></li>
                    </ul>
                </div>

                <div class="menu-divider"></div>

                <!-- إدارة الفواتير -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-file-invoice menu-section-icon"></i>
                        إدارة الفواتير
                    </div>
                    <div class="menu-section-description">
                        إدارة كل ما يتعلق بالفواتير، وتحتوي على:
                    </div>
                    <ul class="menu-items">
                        <li><a href="#" class="menu-item-link"><i class="fas fa-list menu-item-icon"></i>قائمة الفواتير</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-plus menu-item-icon"></i>إنشاء فاتورة جديدة</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-chart-bar menu-item-icon"></i>تقارير الفواتير</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-receipt menu-item-icon"></i>كشف حساب العملاء ✅</a></li>
                    </ul>
                </div>

                <div class="menu-divider"></div>

                <!-- مركز التواصل -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-envelope menu-section-icon"></i>
                        مركز التواصل
                    </div>
                    <div class="menu-section-description">
                        إرسال تنبيهات أو رسائل للعملاء
                    </div>
                </div>

                <div class="menu-divider"></div>

                <!-- التقارير العامة -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-chart-area menu-section-icon"></i>
                        التقارير العامة
                    </div>
                    <div class="menu-section-description">
                        تقارير شاملة (مثل عدد الاشتراكات الشهري، التحصيل، النشاط)
                    </div>
                </div>

                <div class="menu-divider"></div>

                <!-- إدارة المستخدمين -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-users menu-section-icon"></i>
                        إدارة المستخدمين
                    </div>
                    <div class="menu-section-description">
                        التحكم بالمستخدمين وصلاحياتهم (مدير، موظف، مشاهد…)
                    </div>
                </div>

                <div class="menu-divider"></div>

                <!-- الإدارة المتقدمة -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-cogs menu-section-icon"></i>
                        الإدارة المتقدمة
                    </div>
                    <div class="menu-section-description">
                        صلاحيات متقدمة، الإعدادات، النسخ الاحتياطي، تكامل مع API
                    </div>
                </div>
            </div>

            <div class="sidebar-footer">
                <div class="current-user">
                    <div class="user-avatar">
                        {{ current_user.full_name[0] if current_user.full_name else 'A' }}
                    </div>
                    <div class="user-info">
                        <div class="user-name">{{ current_user.full_name }}</div>
                        <div class="user-role">مدير النظام</div>
                    </div>
                </div>

                <input type="text" class="search-input" placeholder="اكتب هنا للبحث...">
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- الشريط العلوي -->
            <div class="top-header">
                <div>
                    <h1 class="header-title">مرحباً {{ current_user.full_name }}</h1>
                    <p class="header-subtitle">مرحباً بك في نظام إدارة الاشتراكات المتقدم</p>
                </div>
                <div style="display: flex; gap: 0.5rem;">
                    <a href="#" class="header-btn">
                        <i class="fas fa-plus"></i>
                        اشتراك جديد
                    </a>
                    <a href="{{ url_for('logout') }}" class="header-btn" style="background: var(--secondary-gradient);">
                        <i class="fas fa-sign-out-alt"></i>
                        خروج
                    </a>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-content">
                        <div>
                            <div class="stat-number">{{ stats.total_customers }}</div>
                            <div class="stat-label">إجمالي العملاء</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card success">
                    <div class="stat-content">
                        <div>
                            <div class="stat-number">{{ stats.active_subscriptions }}</div>
                            <div class="stat-label">اشتراكات نشطة</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-server"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card warning">
                    <div class="stat-content">
                        <div>
                            <div class="stat-number">{{ stats.expiring_soon }}</div>
                            <div class="stat-label">تنتهي قريباً</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card danger">
                    <div class="stat-content">
                        <div>
                            <div class="stat-number">${{ "%.0f"|format(stats.monthly_revenue) }}</div>
                            <div class="stat-label">إيرادات الشهر</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الاشتراكات الحديثة -->
            <div class="advanced-card">
                <h2 class="card-title">
                    <div class="card-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    الاشتراكات الحديثة
                </h2>

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>الخدمة</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for subscription in recent_subscriptions %}
                            <tr>
                                <td>{{ subscription.customer.name }}</td>
                                <td>{{ subscription.service.name }}</td>
                                <td>{{ subscription.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ subscription.end_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span class="status-badge {{ subscription.status }}">
                                        {{ subscription.status }}
                                    </span>
                                </td>
                                <td>
                                    <button class="action-btn" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- الفواتير المعلقة -->
            <div class="advanced-card">
                <h2 class="card-title">
                    <div class="card-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    الفواتير المعلقة
                </h2>

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in pending_invoices %}
                            <tr>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>{{ invoice.customer.name }}</td>
                                <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span class="status-badge {{ invoice.status }}">
                                        {{ invoice.status }}
                                    </span>
                                </td>
                                <td>
                                    <button class="action-btn" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('active');
            }

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', function(event) {
                const sidebar = document.getElementById('sidebar');
                const toggleBtn = document.querySelector('.sidebar-toggle');

                if (window.innerWidth <= 1200) {
                    if (!sidebar.contains(event.target) && !toggleBtn.contains(event.target)) {
                        sidebar.classList.remove('active');
                    }
                }
            });
        </script>
    </body>
    </html>
    '''

    # المسارات والوظائف
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')

            user = User.query.filter_by(username=username).first()

            if user and user.check_password(password):
                login_user(user)
                flash('تم تسجيل الدخول بنجاح! مرحباً بك في النظام المتقدم 🎉', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

        return render_template_string(LOGIN_TEMPLATE)

    @app.route('/dashboard')
    @login_required
    def dashboard():
        # حساب الإحصائيات
        stats = get_dashboard_stats()

        # أحدث الاشتراكات
        recent_subscriptions = Subscription.query.order_by(Subscription.created_at.desc()).limit(5).all()

        # الفواتير المعلقة
        pending_invoices = Invoice.query.filter_by(status='pending').order_by(Invoice.due_date.asc()).limit(5).all()

        return render_template_string(
            DASHBOARD_TEMPLATE,
            stats=stats,
            recent_subscriptions=recent_subscriptions,
            pending_invoices=pending_invoices
        )

    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('login'))

    # تهيئة قاعدة البيانات
    def init_database():
        with app.app_context():
            db.create_all()

            # إنشاء مستخدم مدير إذا لم يكن موجوداً
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام - AdenLink',
                    role='admin'
                )
                admin.set_password('123456')
                db.session.add(admin)

                # إنشاء بيانات تجريبية
                create_sample_data()

                db.session.commit()
                print("✅ تم إنشاء المستخدم المدير والبيانات التجريبية")
            else:
                print("✅ المستخدم المدير موجود مسبقاً")

    def create_sample_data():
        """إنشاء بيانات تجريبية للنظام"""
        try:
            # إنشاء عملاء تجريبيين
            customers = [
                Customer(name='أحمد محمد علي', email='<EMAIL>', phone='+964-XXX-1111', company='شركة التقنية المتقدمة'),
                Customer(name='فاطمة علي حسن', email='<EMAIL>', phone='+964-XXX-2222', company='مؤسسة الإبداع الرقمي'),
                Customer(name='محمد حسن أحمد', email='<EMAIL>', phone='+964-XXX-3333', company='شركة الحلول الذكية'),
            ]

            for customer in customers:
                db.session.add(customer)

            db.session.flush()

            # إنشاء خدمات تجريبية
            services = [
                Service(name='خادم VPS أساسي', description='خادم افتراضي بمواصفات أساسية', price=25.0, billing_cycle='monthly'),
                Service(name='خادم VPS متقدم', description='خادم افتراضي بمواصفات متقدمة', price=50.0, billing_cycle='monthly'),
                Service(name='استضافة مواقع', description='استضافة مواقع مشتركة', price=10.0, billing_cycle='monthly'),
                Service(name='خادم مخصص', description='خادم مخصص بالكامل', price=200.0, billing_cycle='monthly'),
            ]

            for service in services:
                db.session.add(service)

            db.session.flush()

            # إنشاء اشتراكات تجريبية
            subscriptions = [
                Subscription(
                    customer_id=customers[0].id,
                    service_id=services[0].id,
                    subscription_id=generate_subscription_id(),
                    status='active',
                    start_date=date.today() - timedelta(days=30),
                    end_date=date.today() + timedelta(days=30),
                    price=25.0,
                    server_ip='*************'
                ),
                Subscription(
                    customer_id=customers[1].id,
                    service_id=services[1].id,
                    subscription_id=generate_subscription_id(),
                    status='active',
                    start_date=date.today() - timedelta(days=15),
                    end_date=date.today() + timedelta(days=45),
                    price=50.0,
                    server_ip='*************'
                ),
                Subscription(
                    customer_id=customers[2].id,
                    service_id=services[2].id,
                    subscription_id=generate_subscription_id(),
                    status='active',
                    start_date=date.today() - timedelta(days=5),
                    end_date=date.today() + timedelta(days=25),
                    price=10.0,
                    server_ip='*************'
                ),
            ]

            for subscription in subscriptions:
                db.session.add(subscription)

            db.session.flush()

            # إنشاء فواتير تجريبية
            invoices = [
                Invoice(
                    invoice_number=generate_invoice_number(),
                    customer_id=customers[0].id,
                    amount=25.0,
                    total_amount=27.5,
                    due_date=date.today() + timedelta(days=7),
                    status='pending'
                ),
                Invoice(
                    invoice_number=generate_invoice_number(),
                    customer_id=customers[1].id,
                    amount=50.0,
                    total_amount=55.0,
                    due_date=date.today() + timedelta(days=14),
                    status='pending'
                ),
            ]

            for invoice in invoices:
                db.session.add(invoice)

            print("✅ تم إنشاء البيانات التجريبية بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
            db.session.rollback()

    print("✅ تم إعداد المسارات والوظائف بنجاح")

    # تشغيل النظام
    if __name__ == '__main__':
        print("🔄 تهيئة قاعدة البيانات...")
        init_database()

        print("=" * 80)
        print("🎉 نظام إدارة الاشتراكات المتقدم جاهز للتشغيل!")
        print("=" * 80)
        print("🌐 معلومات الوصول:")
        print("   🔗 الرابط: http://localhost:5090")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: 123456")
        print("   🎨 التصميم: متقدم مع قائمة جانبية تفاعلية")
        print("   ⚡ الميزات: إدارة شاملة للاشتراكات والفواتير")
        print("=" * 80)
        print("🚀 بدء تشغيل الخادم...")

        try:
            app.run(
                debug=True,
                host='0.0.0.0',
                port=5090,
                threaded=True
            )
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل الخادم: {e}")

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("\n📦 يرجى تثبيت المكتبات المطلوبة:")
    print("pip install flask flask-sqlalchemy flask-login")
    print("\n💡 أو استخدم الأمر التالي لتثبيت جميع المتطلبات:")
    print("pip install flask flask-sqlalchemy flask-login werkzeug")

    input("\n⏸️ اضغط Enter للخروج...")
    exit(1)

except Exception as e:
    print(f"❌ خطأ في إعداد النظام: {e}")
    import traceback
    traceback.print_exc()

    input("\n⏸️ اضغط Enter للخروج...")
    exit(1)

print("\n⏹️ تم إيقاف النظام")
input("اضغط Enter للخروج...")
