#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات المتقدم والمتكامل
مطور خصيصاً لـ AdenLink - العراق
"""

print("🚀 بدء تشغيل نظام إدارة الاشتراكات المتقدم...")

try:
    from flask import Flask, render_template_string, request, redirect, url_for, flash, jsonify, session
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
    from werkzeug.security import generate_password_hash, check_password_hash
    from datetime import datetime, date, timedelta
    import json
    import uuid
    import os
    
    print("✅ تم تحميل المكتبات بنجاح")
    
    # إعداد التطبيق
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'advanced-subscription-manager-2024'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///advanced_subscriptions.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # إعداد قاعدة البيانات
    db = SQLAlchemy(app)
    
    # إعداد نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # نماذج قاعدة البيانات المتقدمة
    class User(UserMixin, db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        password_hash = db.Column(db.String(200), nullable=False)
        role = db.Column(db.String(20), default='user')
        phone = db.Column(db.String(20))
        avatar = db.Column(db.String(200))
        is_active = db.Column(db.Boolean, default=True)
        last_login = db.Column(db.DateTime)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        def set_password(self, password):
            self.password_hash = generate_password_hash(password)
        
        def check_password(self, password):
            return check_password_hash(self.password_hash, password)
        
        def is_admin(self):
            return self.role == 'admin'

    class Customer(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        phone = db.Column(db.String(20))
        address = db.Column(db.Text)
        company = db.Column(db.String(200))
        tax_number = db.Column(db.String(50))
        status = db.Column(db.String(20), default='active')
        balance = db.Column(db.Float, default=0.0)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        subscriptions = db.relationship('Subscription', backref='customer', lazy=True)
        invoices = db.relationship('Invoice', backref='customer', lazy=True)

    class Service(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        description = db.Column(db.Text)
        category = db.Column(db.String(100))
        price = db.Column(db.Float, nullable=False)
        currency = db.Column(db.String(3), default='USD')
        billing_cycle = db.Column(db.String(20), default='monthly')  # monthly, yearly, weekly
        setup_fee = db.Column(db.Float, default=0.0)
        features = db.Column(db.Text)  # JSON string
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        subscriptions = db.relationship('Subscription', backref='service', lazy=True)

    class Subscription(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
        service_id = db.Column(db.Integer, db.ForeignKey('service.id'), nullable=False)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        
        # معلومات الاشتراك
        subscription_id = db.Column(db.String(50), unique=True, nullable=False)
        status = db.Column(db.String(20), default='active')  # active, suspended, cancelled, expired
        start_date = db.Column(db.Date, nullable=False)
        end_date = db.Column(db.Date, nullable=False)
        next_billing_date = db.Column(db.Date)
        
        # معلومات الخادم
        server_name = db.Column(db.String(100))
        server_ip = db.Column(db.String(45))
        server_port = db.Column(db.Integer)
        username = db.Column(db.String(100))
        password = db.Column(db.String(255))
        
        # معلومات التسعير
        price = db.Column(db.Float, nullable=False)
        currency = db.Column(db.String(3), default='USD')
        discount = db.Column(db.Float, default=0.0)
        
        # معلومات إضافية
        notes = db.Column(db.Text)
        auto_renewal = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
        
        user = db.relationship('User', backref='subscriptions')
        
        def days_until_expiry(self):
            return (self.end_date - date.today()).days
        
        def is_expiring_soon(self, days=7):
            return self.days_until_expiry() <= days

    class Invoice(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        invoice_number = db.Column(db.String(50), unique=True, nullable=False)
        customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
        subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        
        # معلومات الفاتورة
        amount = db.Column(db.Float, nullable=False)
        tax_amount = db.Column(db.Float, default=0.0)
        discount_amount = db.Column(db.Float, default=0.0)
        total_amount = db.Column(db.Float, nullable=False)
        currency = db.Column(db.String(3), default='USD')
        
        # تواريخ مهمة
        issue_date = db.Column(db.Date, default=date.today)
        due_date = db.Column(db.Date, nullable=False)
        paid_date = db.Column(db.Date)
        
        # حالة الفاتورة
        status = db.Column(db.String(20), default='pending')  # pending, paid, overdue, cancelled
        payment_method = db.Column(db.String(50))
        payment_reference = db.Column(db.String(100))
        
        # معلومات إضافية
        description = db.Column(db.Text)
        notes = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        user = db.relationship('User', backref='invoices')
        subscription = db.relationship('Subscription', backref='invoices')

    class Payment(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=False)
        customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        
        amount = db.Column(db.Float, nullable=False)
        currency = db.Column(db.String(3), default='USD')
        payment_method = db.Column(db.String(50), nullable=False)
        payment_reference = db.Column(db.String(100))
        payment_date = db.Column(db.Date, default=date.today)
        
        status = db.Column(db.String(20), default='completed')  # completed, pending, failed
        notes = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        invoice = db.relationship('Invoice', backref='payments')
        customer = db.relationship('Customer', backref='payments')
        user = db.relationship('User', backref='payments_processed')

    class Notification(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        title = db.Column(db.String(200), nullable=False)
        message = db.Column(db.Text, nullable=False)
        type = db.Column(db.String(20), default='info')  # info, warning, error, success
        is_read = db.Column(db.Boolean, default=False)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        user = db.relationship('User', backref='notifications')

    class ActivityLog(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        action = db.Column(db.String(100), nullable=False)
        description = db.Column(db.Text)
        ip_address = db.Column(db.String(45))
        user_agent = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        user = db.relationship('User', backref='activity_logs')

    print("✅ تم إعداد النماذج بنجاح")
    
    # دوال مساعدة
    def generate_subscription_id():
        return f"SUB-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
    
    def generate_invoice_number():
        return f"INV-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
    
    def log_activity(action, description=None):
        if current_user.is_authenticated:
            log = ActivityLog(
                user_id=current_user.id,
                action=action,
                description=description,
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string
            )
            db.session.add(log)
            db.session.commit()
    
    def create_notification(user_id, title, message, type='info'):
        notification = Notification(
            user_id=user_id,
            title=title,
            message=message,
            type=type
        )
        db.session.add(notification)
        db.session.commit()
    
    def get_dashboard_stats():
        """حساب إحصائيات لوحة التحكم"""
        stats = {
            'total_customers': Customer.query.count(),
            'total_subscriptions': Subscription.query.count(),
            'active_subscriptions': Subscription.query.filter_by(status='active').count(),
            'total_services': Service.query.filter_by(is_active=True).count(),
            'total_invoices': Invoice.query.count(),
            'pending_invoices': Invoice.query.filter_by(status='pending').count(),
            'overdue_invoices': Invoice.query.filter(
                Invoice.status == 'pending',
                Invoice.due_date < date.today()
            ).count(),
            'total_revenue': db.session.query(db.func.sum(Payment.amount)).scalar() or 0,
            'monthly_revenue': db.session.query(db.func.sum(Payment.amount)).filter(
                Payment.payment_date >= date.today().replace(day=1)
            ).scalar() or 0,
            'expiring_soon': Subscription.query.filter(
                Subscription.status == 'active',
                Subscription.end_date <= date.today() + timedelta(days=7)
            ).count()
        }
        return stats
    
    print("✅ تم إعداد الدوال المساعدة بنجاح")

    # قوالب HTML المتقدمة
    BASE_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}نظام إدارة الاشتراكات المتقدم{% endblock %}</title>

        <!-- CSS Libraries -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">

        <style>
            :root {
                --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
                --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

                --sidebar-width: 320px;
                --header-height: 70px;

                --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
                --shadow-medium: 0 5px 20px rgba(0,0,0,0.15);
                --shadow-heavy: 0 10px 30px rgba(0,0,0,0.2);

                --border-radius: 15px;
                --border-radius-lg: 25px;

                --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                min-height: 100vh;
                color: #2c3e50;
                overflow-x: hidden;
            }

            /* خلفية متحركة */
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
                z-index: -1;
                animation: backgroundMove 20s ease-in-out infinite;
            }

            @keyframes backgroundMove {
                0%, 100% { transform: translateY(0px) rotate(0deg); }
                50% { transform: translateY(-20px) rotate(1deg); }
            }

            /* القائمة الجانبية المتقدمة */
            .sidebar {
                position: fixed;
                right: 0;
                top: 0;
                width: var(--sidebar-width);
                height: 100vh;
                background: linear-gradient(180deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
                backdrop-filter: blur(20px);
                border-left: 1px solid rgba(255,255,255,0.3);
                box-shadow: var(--shadow-heavy);
                z-index: 1000;
                overflow-y: auto;
                transition: var(--transition);
                transform: translateX(0);
            }

            .sidebar::-webkit-scrollbar {
                width: 6px;
            }

            .sidebar::-webkit-scrollbar-track {
                background: rgba(0,0,0,0.1);
                border-radius: 10px;
            }

            .sidebar::-webkit-scrollbar-thumb {
                background: var(--primary-gradient);
                border-radius: 10px;
            }

            .sidebar-header {
                padding: 2rem 1.5rem;
                text-align: center;
                border-bottom: 1px solid rgba(0,0,0,0.1);
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            }

            .sidebar-logo {
                width: 60px;
                height: 60px;
                background: var(--primary-gradient);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1rem;
                color: white;
                font-size: 1.5rem;
                box-shadow: var(--shadow-medium);
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            .sidebar-title {
                font-size: 1.2rem;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 0.5rem;
            }

            .sidebar-subtitle {
                font-size: 0.9rem;
                color: #7f8c8d;
                margin-bottom: 0;
            }

            /* عناصر القائمة */
            .sidebar-menu {
                padding: 1rem 0;
            }

            .menu-section {
                margin-bottom: 1rem;
            }

            .menu-section-title {
                padding: 1rem 1.5rem;
                font-size: 1rem;
                font-weight: 600;
                color: #2c3e50;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
                border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
                margin-left: 1rem;
                display: flex;
                align-items: center;
                transition: var(--transition);
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .menu-section-title::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 4px;
                height: 100%;
                background: var(--primary-gradient);
                transform: scaleY(0);
                transition: var(--transition);
            }

            .menu-section-title:hover::before {
                transform: scaleY(1);
            }

            .menu-section-title:hover {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
                transform: translateX(-10px);
                box-shadow: var(--shadow-light);
            }

            .menu-section-icon {
                margin-left: 1rem;
                font-size: 1.1rem;
                width: 20px;
                text-align: center;
            }

            .menu-section-description {
                font-size: 0.8rem;
                color: #7f8c8d;
                padding: 0.5rem 1.5rem 0.5rem 2rem;
                line-height: 1.4;
                margin-bottom: 0.5rem;
            }

            .menu-items {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .menu-item {
                margin-bottom: 0.25rem;
            }

            .menu-item-link {
                display: flex;
                align-items: center;
                padding: 0.75rem 1.5rem 0.75rem 2.5rem;
                color: #5a6c7d;
                text-decoration: none;
                font-size: 0.9rem;
                font-weight: 500;
                transition: var(--transition);
                border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
                margin-left: 1rem;
                position: relative;
                overflow: hidden;
            }

            .menu-item-link::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 3px;
                height: 100%;
                background: var(--success-gradient);
                transform: scaleY(0);
                transition: var(--transition);
            }

            .menu-item-link:hover::before {
                transform: scaleY(1);
            }

            .menu-item-link:hover {
                background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1));
                color: #4facfe;
                text-decoration: none;
                transform: translateX(-8px);
                box-shadow: var(--shadow-light);
            }

            .menu-item-link.active {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
                color: #667eea;
                font-weight: 600;
            }

            .menu-item-link.active::before {
                transform: scaleY(1);
                background: var(--primary-gradient);
            }

            .menu-item-icon {
                margin-left: 0.75rem;
                font-size: 0.9rem;
                width: 16px;
                text-align: center;
            }

            .menu-badge {
                background: var(--danger-gradient);
                color: white;
                font-size: 0.7rem;
                padding: 0.2rem 0.5rem;
                border-radius: 10px;
                margin-right: auto;
                font-weight: 600;
                animation: bounce 1s infinite;
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                40% { transform: translateY(-3px); }
                60% { transform: translateY(-2px); }
            }

            .menu-divider {
                height: 1px;
                background: linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent);
                margin: 1rem 1.5rem;
            }

            /* أسفل القائمة الجانبية */
            .sidebar-footer {
                position: sticky;
                bottom: 0;
                left: 0;
                right: 0;
                padding: 1.5rem;
                background: linear-gradient(180deg, transparent, rgba(255,255,255,0.95));
                border-top: 1px solid rgba(0,0,0,0.1);
                backdrop-filter: blur(10px);
            }

            .current-user {
                display: flex;
                align-items: center;
                padding: 1rem;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
                border-radius: var(--border-radius);
                margin-bottom: 1rem;
                transition: var(--transition);
                cursor: pointer;
            }

            .current-user:hover {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
                transform: translateY(-2px);
                box-shadow: var(--shadow-light);
            }

            .user-avatar {
                width: 45px;
                height: 45px;
                border-radius: 50%;
                background: var(--primary-gradient);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 700;
                margin-left: 1rem;
                font-size: 1.1rem;
                box-shadow: var(--shadow-light);
            }

            .user-info {
                flex: 1;
            }

            .user-name {
                font-weight: 600;
                color: #2c3e50;
                font-size: 0.95rem;
                margin-bottom: 0.25rem;
            }

            .user-role {
                font-size: 0.8rem;
                color: #7f8c8d;
                margin-bottom: 0;
            }

            .user-status {
                width: 10px;
                height: 10px;
                background: #2ecc71;
                border-radius: 50%;
                margin-right: 0.5rem;
                animation: pulse 2s infinite;
            }

            /* مربع البحث المتقدم */
            .search-box {
                position: relative;
                margin-bottom: 1rem;
            }

            .search-input {
                width: 100%;
                padding: 1rem 1rem 1rem 3rem;
                border: 2px solid rgba(102, 126, 234, 0.2);
                border-radius: var(--border-radius-lg);
                background: rgba(255,255,255,0.8);
                font-size: 0.9rem;
                transition: var(--transition);
                backdrop-filter: blur(10px);
            }

            .search-input:focus {
                outline: none;
                border-color: #667eea;
                background: white;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
                transform: translateY(-2px);
            }

            .search-icon {
                position: absolute;
                right: 1rem;
                top: 50%;
                transform: translateY(-50%);
                color: #7f8c8d;
                font-size: 1rem;
                transition: var(--transition);
            }

            .search-input:focus + .search-icon {
                color: #667eea;
            }

            /* المحتوى الرئيسي */
            .main-content {
                margin-left: var(--sidebar-width);
                min-height: 100vh;
                transition: var(--transition);
                padding: 2rem;
            }

            /* الشريط العلوي */
            .top-header {
                background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
                backdrop-filter: blur(20px);
                border-radius: var(--border-radius-lg);
                padding: 1.5rem 2rem;
                margin-bottom: 2rem;
                box-shadow: var(--shadow-medium);
                display: flex;
                align-items: center;
                justify-content: space-between;
                border: 1px solid rgba(255,255,255,0.3);
                position: relative;
                overflow: hidden;
            }

            .top-header::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: var(--primary-gradient);
                background-size: 200% 100%;
                animation: gradientMove 3s ease-in-out infinite;
            }

            @keyframes gradientMove {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            .header-title {
                font-size: 1.8rem;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 0.5rem;
            }

            .header-subtitle {
                color: #7f8c8d;
                font-size: 1rem;
                margin-bottom: 0;
            }

            .header-actions {
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .header-btn {
                background: var(--primary-gradient);
                color: white;
                border: none;
                border-radius: var(--border-radius);
                padding: 0.75rem 1.5rem;
                font-weight: 600;
                text-decoration: none;
                transition: var(--transition);
                box-shadow: var(--shadow-light);
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .header-btn:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-medium);
                color: white;
                text-decoration: none;
            }

            .header-btn.secondary {
                background: var(--secondary-gradient);
            }

            .header-btn.success {
                background: var(--success-gradient);
            }

            /* البطاقات المتقدمة */
            .advanced-card {
                background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
                border-radius: var(--border-radius-lg);
                padding: 2rem;
                margin-bottom: 2rem;
                box-shadow: var(--shadow-medium);
                border: 1px solid rgba(255,255,255,0.3);
                backdrop-filter: blur(20px);
                transition: var(--transition);
                position: relative;
                overflow: hidden;
            }

            .advanced-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: var(--primary-gradient);
                transform: scaleX(0);
                transition: var(--transition);
            }

            .advanced-card:hover::before {
                transform: scaleX(1);
            }

            .advanced-card:hover {
                transform: translateY(-5px);
                box-shadow: var(--shadow-heavy);
            }

            .card-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 1.5rem;
                padding-bottom: 1rem;
                border-bottom: 1px solid rgba(0,0,0,0.1);
            }

            .card-title {
                font-size: 1.3rem;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 0;
                display: flex;
                align-items: center;
                gap: 0.75rem;
            }

            .card-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: var(--primary-gradient);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 1rem;
            }

            /* إحصائيات متقدمة */
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 1.5rem;
                margin-bottom: 2rem;
            }

            .stat-card {
                background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
                border-radius: var(--border-radius-lg);
                padding: 2rem;
                box-shadow: var(--shadow-medium);
                border: 1px solid rgba(255,255,255,0.3);
                backdrop-filter: blur(20px);
                transition: var(--transition);
                position: relative;
                overflow: hidden;
                cursor: pointer;
            }

            .stat-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: var(--primary-gradient);
                transform: scaleX(0);
                transition: var(--transition);
            }

            .stat-card:hover::before {
                transform: scaleX(1);
            }

            .stat-card:hover {
                transform: translateY(-8px) scale(1.02);
                box-shadow: var(--shadow-heavy);
            }

            .stat-card.primary::before { background: var(--primary-gradient); }
            .stat-card.success::before { background: var(--success-gradient); }
            .stat-card.warning::before { background: var(--warning-gradient); }
            .stat-card.danger::before { background: var(--danger-gradient); }

            .stat-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .stat-details {
                flex: 1;
            }

            .stat-number {
                font-size: 2.5rem;
                font-weight: 800;
                color: #2c3e50;
                margin-bottom: 0.5rem;
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                line-height: 1;
            }

            .stat-label {
                color: #5a6c7d;
                font-weight: 600;
                font-size: 1rem;
                margin-bottom: 0.5rem;
            }

            .stat-change {
                font-size: 0.85rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            .stat-change.positive {
                color: #2ecc71;
            }

            .stat-change.negative {
                color: #e74c3c;
            }

            .stat-icon {
                width: 70px;
                height: 70px;
                border-radius: var(--border-radius);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 2rem;
                color: white;
                box-shadow: var(--shadow-medium);
                transition: var(--transition);
            }

            .stat-card:hover .stat-icon {
                transform: rotate(5deg) scale(1.1);
            }

            .stat-card.primary .stat-icon { background: var(--primary-gradient); }
            .stat-card.success .stat-icon { background: var(--success-gradient); }
            .stat-card.warning .stat-icon { background: var(--warning-gradient); }
            .stat-card.danger .stat-icon { background: var(--danger-gradient); }

            /* جداول متقدمة */
            .advanced-table {
                background: white;
                border-radius: var(--border-radius);
                overflow: hidden;
                box-shadow: var(--shadow-light);
                margin-bottom: 2rem;
            }

            .table-header {
                background: var(--primary-gradient);
                color: white;
                padding: 1rem 1.5rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .table-title {
                font-size: 1.1rem;
                margin-bottom: 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .table-actions {
                display: flex;
                gap: 0.5rem;
            }

            .table-btn {
                background: rgba(255,255,255,0.2);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 0.5rem 1rem;
                font-size: 0.85rem;
                transition: var(--transition);
                text-decoration: none;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            .table-btn:hover {
                background: rgba(255,255,255,0.3);
                color: white;
                text-decoration: none;
            }

            .table-content {
                max-height: 400px;
                overflow-y: auto;
            }

            .table-content::-webkit-scrollbar {
                width: 6px;
            }

            .table-content::-webkit-scrollbar-track {
                background: #f1f1f1;
            }

            .table-content::-webkit-scrollbar-thumb {
                background: var(--primary-gradient);
                border-radius: 10px;
            }

            .advanced-table table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 0;
            }

            .advanced-table th,
            .advanced-table td {
                padding: 1rem 1.5rem;
                text-align: right;
                border-bottom: 1px solid #f8f9fa;
                vertical-align: middle;
            }

            .advanced-table th {
                background: #f8f9fa;
                font-weight: 600;
                color: #2c3e50;
                font-size: 0.9rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .advanced-table tbody tr {
                transition: var(--transition);
            }

            .advanced-table tbody tr:hover {
                background: rgba(102, 126, 234, 0.05);
            }

            /* أزرار الحالة */
            .status-badge {
                padding: 0.4rem 0.8rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                border: none;
                display: inline-flex;
                align-items: center;
                gap: 0.25rem;
            }

            .status-badge.active {
                background: linear-gradient(135deg, #2ecc71, #27ae60);
                color: white;
            }

            .status-badge.pending {
                background: linear-gradient(135deg, #f39c12, #e67e22);
                color: white;
            }

            .status-badge.suspended {
                background: linear-gradient(135deg, #e74c3c, #c0392b);
                color: white;
            }

            .status-badge.expired {
                background: linear-gradient(135deg, #95a5a6, #7f8c8d);
                color: white;
            }

            /* أزرار الإجراءات */
            .action-btn {
                background: none;
                border: none;
                color: #7f8c8d;
                font-size: 1rem;
                padding: 0.5rem;
                border-radius: 8px;
                transition: var(--transition);
                cursor: pointer;
                margin: 0 0.25rem;
            }

            .action-btn:hover {
                background: rgba(102, 126, 234, 0.1);
                color: #667eea;
                transform: scale(1.1);
            }

            .action-btn.edit:hover {
                color: #3498db;
            }

            .action-btn.delete:hover {
                color: #e74c3c;
            }

            .action-btn.view:hover {
                color: #2ecc71;
            }

            /* نماذج متقدمة */
            .advanced-form {
                background: white;
                border-radius: var(--border-radius);
                padding: 2rem;
                box-shadow: var(--shadow-light);
                margin-bottom: 2rem;
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 600;
                color: #2c3e50;
                font-size: 0.95rem;
            }

            .form-control {
                width: 100%;
                padding: 0.75rem 1rem;
                border: 2px solid #e9ecef;
                border-radius: var(--border-radius);
                font-size: 0.95rem;
                transition: var(--transition);
                background: white;
            }

            .form-control:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            }

            .form-select {
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
                background-repeat: no-repeat;
                background-position: left 0.75rem center;
                background-size: 16px 12px;
                padding-left: 2.5rem;
            }

            .btn {
                padding: 0.75rem 1.5rem;
                border: none;
                border-radius: var(--border-radius);
                font-weight: 600;
                text-decoration: none;
                transition: var(--transition);
                cursor: pointer;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.95rem;
            }

            .btn-primary {
                background: var(--primary-gradient);
                color: white;
                box-shadow: var(--shadow-light);
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-medium);
                color: white;
                text-decoration: none;
            }

            .btn-success {
                background: var(--success-gradient);
                color: white;
                box-shadow: var(--shadow-light);
            }

            .btn-success:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-medium);
                color: white;
                text-decoration: none;
            }

            .btn-warning {
                background: var(--warning-gradient);
                color: white;
                box-shadow: var(--shadow-light);
            }

            .btn-warning:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-medium);
                color: white;
                text-decoration: none;
            }

            .btn-danger {
                background: var(--danger-gradient);
                color: white;
                box-shadow: var(--shadow-light);
            }

            .btn-danger:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-medium);
                color: white;
                text-decoration: none;
            }

            .btn-outline {
                background: transparent;
                border: 2px solid #667eea;
                color: #667eea;
            }

            .btn-outline:hover {
                background: #667eea;
                color: white;
                text-decoration: none;
            }

            /* تنبيهات متقدمة */
            .alert {
                padding: 1rem 1.5rem;
                border-radius: var(--border-radius);
                margin-bottom: 1rem;
                border: none;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                font-weight: 500;
            }

            .alert-success {
                background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(39, 174, 96, 0.1));
                color: #27ae60;
                border-right: 4px solid #2ecc71;
            }

            .alert-warning {
                background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.1));
                color: #e67e22;
                border-right: 4px solid #f39c12;
            }

            .alert-danger {
                background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
                color: #c0392b;
                border-right: 4px solid #e74c3c;
            }

            .alert-info {
                background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.1));
                color: #2980b9;
                border-right: 4px solid #3498db;
            }

            /* استجابة للشاشات الصغيرة */
            @media (max-width: 1200px) {
                .sidebar {
                    transform: translateX(100%);
                }

                .sidebar.active {
                    transform: translateX(0);
                }

                .main-content {
                    margin-left: 0;
                    padding: 1rem;
                }

                .stats-grid {
                    grid-template-columns: 1fr;
                }

                .top-header {
                    flex-direction: column;
                    gap: 1rem;
                    text-align: center;
                }

                .header-actions {
                    width: 100%;
                    justify-content: center;
                }
            }

            @media (max-width: 768px) {
                .advanced-card {
                    padding: 1.5rem;
                }

                .stat-card {
                    padding: 1.5rem;
                }

                .stat-content {
                    flex-direction: column;
                    text-align: center;
                    gap: 1rem;
                }

                .advanced-table th,
                .advanced-table td {
                    padding: 0.75rem;
                    font-size: 0.9rem;
                }
            }

            /* تأثيرات التحميل */
            .loading {
                opacity: 0;
                animation: fadeInUp 0.6s ease forwards;
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* زر التبديل للقائمة الجانبية */
            .sidebar-toggle {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1001;
                background: var(--primary-gradient);
                color: white;
                border: none;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
                cursor: pointer;
                box-shadow: var(--shadow-medium);
                transition: var(--transition);
                display: none;
            }

            .sidebar-toggle:hover {
                transform: scale(1.1);
                box-shadow: var(--shadow-heavy);
            }

            @media (max-width: 1200px) {
                .sidebar-toggle {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        </style>

        {% block extra_css %}{% endblock %}
    </head>
    <body>
        {% block content %}{% endblock %}

        <!-- JavaScript Libraries -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

        <script>
            // تأثيرات التحميل
            document.addEventListener('DOMContentLoaded', function() {
                const elements = document.querySelectorAll('.loading');
                elements.forEach((el, index) => {
                    setTimeout(() => {
                        el.style.animationDelay = `${index * 0.1}s`;
                        el.classList.add('animate__animated', 'animate__fadeInUp');
                    }, index * 100);
                });
            });

            // التحكم في القائمة الجانبية
            function toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('active');
            }

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', function(event) {
                const sidebar = document.getElementById('sidebar');
                const toggleBtn = document.querySelector('.sidebar-toggle');

                if (window.innerWidth <= 1200) {
                    if (!sidebar.contains(event.target) && !toggleBtn.contains(event.target)) {
                        sidebar.classList.remove('active');
                    }
                }
            });

            // تأثيرات تفاعلية
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // البحث المتقدم
            function initSearch() {
                const searchInput = document.querySelector('.search-input');
                if (searchInput) {
                    searchInput.addEventListener('input', function() {
                        const query = this.value.toLowerCase();
                        const menuItems = document.querySelectorAll('.menu-item-link');

                        menuItems.forEach(item => {
                            const text = item.textContent.toLowerCase();
                            const menuItem = item.closest('.menu-item');

                            if (text.includes(query)) {
                                menuItem.style.display = 'block';
                                item.innerHTML = item.textContent.replace(
                                    new RegExp(query, 'gi'),
                                    match => `<mark style="background: #fff3cd; padding: 0 2px; border-radius: 3px;">${match}</mark>`
                                );
                            } else {
                                menuItem.style.display = 'none';
                            }
                        });
                    });
                }
            }

            // تهيئة البحث عند تحميل الصفحة
            document.addEventListener('DOMContentLoaded', initSearch);

            // تحديث الوقت الحقيقي
            function updateRealTime() {
                const now = new Date();
                const timeElements = document.querySelectorAll('.real-time');

                timeElements.forEach(el => {
                    el.textContent = now.toLocaleString('ar-EG');
                });
            }

            setInterval(updateRealTime, 1000);

            // إشعارات متقدمة
            function showNotification(title, message, type = 'info') {
                // يمكن تطوير نظام إشعارات متقدم هنا
                console.log(`${type.toUpperCase()}: ${title} - ${message}`);
            }

            // تحديث تلقائي للبيانات
            function autoRefresh() {
                // يمكن إضافة تحديث تلقائي للإحصائيات هنا
                console.log('تحديث البيانات...');
            }

            // تحديث كل 30 ثانية
            setInterval(autoRefresh, 30000);
        </script>

        {% block extra_js %}{% endblock %}
    </body>
    </html>
    '''

    # قالب تسجيل الدخول
    LOGIN_TEMPLATE = '''
    {% extends "base.html" %}

    {% block title %}تسجيل الدخول - نظام إدارة الاشتراكات{% endblock %}

    {% block content %}
    <div style="min-height: 100vh; display: flex; align-items: center; justify-content: center; padding: 2rem;">
        <div style="background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
                    border-radius: 25px; padding: 3rem; width: 100%; max-width: 450px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1); backdrop-filter: blur(20px);
                    border: 1px solid rgba(255,255,255,0.3);">

            <div style="text-align: center; margin-bottom: 2rem;">
                <div style="width: 80px; height: 80px; background: var(--primary-gradient);
                           border-radius: 50%; display: flex; align-items: center; justify-content: center;
                           margin: 0 auto 1rem; color: white; font-size: 2rem; box-shadow: var(--shadow-medium);">
                    <i class="fas fa-cloud"></i>
                </div>
                <h1 style="font-size: 1.8rem; font-weight: 700; color: #2c3e50; margin-bottom: 0.5rem;">
                    نظام إدارة الاشتراكات المتقدم
                </h1>
                <p style="color: #7f8c8d; font-size: 1rem; margin-bottom: 0;">
                    AdenLink - العراق
                </p>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }}">
                            <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user" style="margin-left: 0.5rem;"></i>اسم المستخدم
                    </label>
                    <input type="text" class="form-control" name="username" required
                           placeholder="أدخل اسم المستخدم" autocomplete="username">
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-lock" style="margin-left: 0.5rem;"></i>كلمة المرور
                    </label>
                    <input type="password" class="form-control" name="password" required
                           placeholder="أدخل كلمة المرور" autocomplete="current-password">
                </div>

                <button type="submit" class="btn btn-primary" style="width: 100%; margin-top: 1rem;">
                    <i class="fas fa-sign-in-alt"></i>تسجيل الدخول
                </button>
            </form>

            <div style="background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 12px;
                        padding: 1rem; margin-top: 1.5rem; text-align: center;">
                <small style="color: #0c5460; font-weight: 500;">
                    <i class="fas fa-info-circle" style="margin-left: 0.25rem;"></i>
                    <strong>بيانات التجربة:</strong><br>
                    المستخدم: <code>admin</code> | كلمة المرور: <code>123456</code>
                </small>
            </div>
        </div>
    </div>
    {% endblock %}
    '''

    # قالب لوحة التحكم الرئيسية
    DASHBOARD_TEMPLATE = '''
    {% extends "base.html" %}

    {% block title %}لوحة التحكم - نظام إدارة الاشتراكات{% endblock %}

    {% block content %}
    <!-- زر إظهار/إخفاء القائمة الجانبية -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- القائمة الجانبية -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-cloud"></i>
            </div>
            <h3 class="sidebar-title">نظام إدارة الاشتراكات</h3>
            <p class="sidebar-subtitle">AdenLink - العراق</p>
        </div>

        <div class="sidebar-menu">
            <!-- لوحة المعلومات -->
            <div class="menu-section">
                <div class="menu-section-title">
                    <i class="fas fa-tachometer-alt menu-section-icon"></i>
                    لوحة المعلومات
                </div>
                <div class="menu-section-description">
                    عرض إجمالي الإحصائيات، الاشتراكات، التنبيهات، الرسوم البيانية العامة
                </div>
            </div>

            <div class="menu-divider"></div>

            <!-- إدارة الاشتراكات -->
            <div class="menu-section">
                <div class="menu-section-title">
                    <i class="fas fa-server menu-section-icon"></i>
                    إدارة الاشتراكات
                </div>
                <div class="menu-section-description">
                    إدارة وتفصيل الاشتراكات، وتحتوي على:
                </div>
                <ul class="menu-items">
                    <li class="menu-item">
                        <a href="{{ url_for('subscriptions_overview') }}" class="menu-item-link">
                            <i class="fas fa-chart-pie menu-item-icon"></i>
                            مخطط الاشتراكات
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="{{ url_for('subscriptions_list') }}" class="menu-item-link">
                            <i class="fas fa-list menu-item-icon"></i>
                            قائمة الاشتراكات
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="{{ url_for('payment_methods') }}" class="menu-item-link">
                            <i class="fas fa-credit-card menu-item-icon"></i>
                            طرق الدفع
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="{{ url_for('add_subscription') }}" class="menu-item-link">
                            <i class="fas fa-plus menu-item-icon"></i>
                            إضافة اشتراك جديد
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="{{ url_for('subscription_analytics') }}" class="menu-item-link">
                            <i class="fas fa-chart-line menu-item-icon"></i>
                            تحليلات الاشتراكات
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="{{ url_for('subscription_reports') }}" class="menu-item-link">
                            <i class="fas fa-file-alt menu-item-icon"></i>
                            تقارير الاشتراكات
                        </a>
                    </li>
                </ul>
            </div>

            <div class="menu-divider"></div>

            <!-- إدارة الفواتير -->
            <div class="menu-section">
                <div class="menu-section-title">
                    <i class="fas fa-file-invoice menu-section-icon"></i>
                    إدارة الفواتير
                </div>
                <div class="menu-section-description">
                    إدارة كل ما يتعلق بالفواتير، وتحتوي على:
                </div>
                <ul class="menu-items">
                    <li class="menu-item">
                        <a href="{{ url_for('invoices_list') }}" class="menu-item-link">
                            <i class="fas fa-list menu-item-icon"></i>
                            قائمة الفواتير
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="{{ url_for('create_invoice') }}" class="menu-item-link">
                            <i class="fas fa-plus menu-item-icon"></i>
                            إنشاء فاتورة جديدة
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="{{ url_for('invoice_reports') }}" class="menu-item-link">
                            <i class="fas fa-chart-bar menu-item-icon"></i>
                            تقارير الفواتير
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="{{ url_for('customer_statements') }}" class="menu-item-link">
                            <i class="fas fa-receipt menu-item-icon"></i>
                            كشف حساب العملاء
                            <span class="menu-badge">جديد</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="menu-divider"></div>

            <!-- مركز التواصل -->
            <div class="menu-section">
                <div class="menu-section-title">
                    <i class="fas fa-envelope menu-section-icon"></i>
                    مركز التواصل
                </div>
                <div class="menu-section-description">
                    إرسال تنبيهات أو رسائل للعملاء
                </div>
                <ul class="menu-items">
                    <li class="menu-item">
                        <a href="{{ url_for('send_notifications') }}" class="menu-item-link">
                            <i class="fas fa-paper-plane menu-item-icon"></i>
                            إرسال إشعارات
                        </a>
                    </li>
                </ul>
            </div>

            <div class="menu-divider"></div>

            <!-- التقارير العامة -->
            <div class="menu-section">
                <div class="menu-section-title">
                    <i class="fas fa-chart-area menu-section-icon"></i>
                    التقارير العامة
                </div>
                <div class="menu-section-description">
                    تقارير شاملة (مثل عدد الاشتراكات الشهري، التحصيل، النشاط)
                </div>
                <ul class="menu-items">
                    <li class="menu-item">
                        <a href="{{ url_for('general_reports') }}" class="menu-item-link">
                            <i class="fas fa-chart-bar menu-item-icon"></i>
                            التقارير الشاملة
                        </a>
                    </li>
                </ul>
            </div>

            <div class="menu-divider"></div>

            <!-- إدارة المستخدمين -->
            <div class="menu-section">
                <div class="menu-section-title">
                    <i class="fas fa-users menu-section-icon"></i>
                    إدارة المستخدمين
                </div>
                <div class="menu-section-description">
                    التحكم بالمستخدمين وصلاحياتهم (مدير، موظف، مشاهد…)
                </div>
                <ul class="menu-items">
                    <li class="menu-item">
                        <a href="{{ url_for('users_management') }}" class="menu-item-link">
                            <i class="fas fa-users-cog menu-item-icon"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                </ul>
            </div>

            <div class="menu-divider"></div>

            <!-- الإدارة المتقدمة -->
            <div class="menu-section">
                <div class="menu-section-title">
                    <i class="fas fa-cogs menu-section-icon"></i>
                    الإدارة المتقدمة
                </div>
                <div class="menu-section-description">
                    صلاحيات متقدمة، الإعدادات، النسخ الاحتياطي، تكامل مع API
                </div>
                <ul class="menu-items">
                    <li class="menu-item">
                        <a href="{{ url_for('system_settings') }}" class="menu-item-link">
                            <i class="fas fa-cog menu-item-icon"></i>
                            إعدادات النظام
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="{{ url_for('backup_restore') }}" class="menu-item-link">
                            <i class="fas fa-database menu-item-icon"></i>
                            النسخ الاحتياطي
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="sidebar-footer">
            <div class="current-user">
                <div class="user-avatar">
                    {{ current_user.full_name[0] if current_user.full_name else 'A' }}
                </div>
                <div class="user-info">
                    <div class="user-name">{{ current_user.full_name }}</div>
                    <div class="user-role">مدير النظام</div>
                </div>
                <div class="user-status"></div>
            </div>

            <div class="search-box">
                <input type="text" class="search-input" placeholder="اكتب هنا للبحث...">
                <i class="fas fa-search search-icon"></i>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-header loading">
            <div>
                <h1 class="header-title">مرحباً {{ current_user.full_name }}</h1>
                <p class="header-subtitle">مرحباً بك في نظام إدارة الاشتراكات المتقدم - <span class="real-time"></span></p>
            </div>
            <div class="header-actions">
                <a href="{{ url_for('add_subscription') }}" class="header-btn">
                    <i class="fas fa-plus"></i>
                    اشتراك جديد
                </a>
                <a href="{{ url_for('create_invoice') }}" class="header-btn secondary">
                    <i class="fas fa-file-invoice"></i>
                    فاتورة جديدة
                </a>
                <a href="{{ url_for('logout') }}" class="header-btn" style="background: var(--danger-gradient);">
                    <i class="fas fa-sign-out-alt"></i>
                    خروج
                </a>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid loading">
            <div class="stat-card primary">
                <div class="stat-content">
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.total_customers }}</div>
                        <div class="stat-label">إجمالي العملاء</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12% من الشهر الماضي
                        </div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card success">
                <div class="stat-content">
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.active_subscriptions }}</div>
                        <div class="stat-label">اشتراكات نشطة</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +8% من الشهر الماضي
                        </div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-server"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-content">
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.expiring_soon }}</div>
                        <div class="stat-label">تنتهي قريباً</div>
                        <div class="stat-change negative">
                            <i class="fas fa-exclamation-triangle"></i>
                            يحتاج متابعة
                        </div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card danger">
                <div class="stat-content">
                    <div class="stat-details">
                        <div class="stat-number">${{ "%.2f"|format(stats.monthly_revenue) }}</div>
                        <div class="stat-label">إيرادات الشهر</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +15% من الشهر الماضي
                        </div>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- الاشتراكات الحديثة -->
        <div class="advanced-card loading">
            <div class="card-header">
                <h2 class="card-title">
                    <div class="card-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    الاشتراكات الحديثة
                </h2>
                <div class="header-actions">
                    <a href="{{ url_for('subscriptions_list') }}" class="btn btn-outline">
                        <i class="fas fa-eye"></i>
                        عرض الكل
                    </a>
                </div>
            </div>

            <div class="advanced-table">
                <div class="table-header">
                    <div class="table-title">
                        <i class="fas fa-list"></i>
                        آخر الاشتراكات
                    </div>
                    <div class="table-actions">
                        <a href="{{ url_for('add_subscription') }}" class="table-btn">
                            <i class="fas fa-plus"></i>
                            جديد
                        </a>
                    </div>
                </div>
                <div class="table-content">
                    <table>
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>الخدمة</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for subscription in recent_subscriptions %}
                            <tr>
                                <td>{{ subscription.customer.name }}</td>
                                <td>{{ subscription.service.name }}</td>
                                <td>{{ subscription.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ subscription.end_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span class="status-badge {{ subscription.status }}">
                                        {{ subscription.status }}
                                    </span>
                                </td>
                                <td>
                                    <button class="action-btn view" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn edit" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn delete" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- الفواتير المعلقة -->
        <div class="advanced-card loading">
            <div class="card-header">
                <h2 class="card-title">
                    <div class="card-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    الفواتير المعلقة
                </h2>
                <div class="header-actions">
                    <a href="{{ url_for('invoices_list') }}" class="btn btn-outline">
                        <i class="fas fa-eye"></i>
                        عرض الكل
                    </a>
                </div>
            </div>

            <div class="advanced-table">
                <div class="table-header">
                    <div class="table-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        فواتير تحتاج متابعة
                    </div>
                    <div class="table-actions">
                        <a href="{{ url_for('create_invoice') }}" class="table-btn">
                            <i class="fas fa-plus"></i>
                            جديد
                        </a>
                    </div>
                </div>
                <div class="table-content">
                    <table>
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in pending_invoices %}
                            <tr>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>{{ invoice.customer.name }}</td>
                                <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span class="status-badge {{ invoice.status }}">
                                        {{ invoice.status }}
                                    </span>
                                </td>
                                <td>
                                    <button class="action-btn view" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn edit" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn delete" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endblock %}
    '''

    # المسارات والوظائف
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')

            user = User.query.filter_by(username=username).first()

            if user and user.check_password(password):
                login_user(user)
                user.last_login = datetime.now()
                db.session.commit()

                log_activity('تسجيل دخول', f'تم تسجيل الدخول بنجاح')
                flash('تم تسجيل الدخول بنجاح! مرحباً بك في النظام المتقدم 🎉', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

        return render_template_string(LOGIN_TEMPLATE.replace('{% extends "base.html" %}', '').replace('{% block title %}', '<!-- ').replace('{% endblock %}', ' -->').replace('{% block content %}', '').replace('{% endblock %}', ''))

    @app.route('/dashboard')
    @login_required
    def dashboard():
        # حساب الإحصائيات
        stats = get_dashboard_stats()

        # أحدث الاشتراكات
        recent_subscriptions = Subscription.query.order_by(Subscription.created_at.desc()).limit(5).all()

        # الفواتير المعلقة
        pending_invoices = Invoice.query.filter_by(status='pending').order_by(Invoice.due_date.asc()).limit(5).all()

        log_activity('عرض لوحة التحكم', 'تم الوصول إلى لوحة التحكم الرئيسية')

        return render_template_string(
            BASE_TEMPLATE.replace('{% block content %}{% endblock %}', DASHBOARD_TEMPLATE.replace('{% extends "base.html" %}', '').replace('{% block title %}', '<!-- ').replace('{% endblock %}', ' -->').replace('{% block content %}', '').replace('{% endblock %}', '')),
            stats=stats,
            recent_subscriptions=recent_subscriptions,
            pending_invoices=pending_invoices
        )

    @app.route('/logout')
    @login_required
    def logout():
        log_activity('تسجيل خروج', 'تم تسجيل الخروج من النظام')
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('login'))

    # مسارات إدارة الاشتراكات
    @app.route('/subscriptions')
    @login_required
    def subscriptions_list():
        subscriptions = Subscription.query.order_by(Subscription.created_at.desc()).all()
        return jsonify({
            'status': 'success',
            'data': [
                {
                    'id': sub.id,
                    'customer': sub.customer.name,
                    'service': sub.service.name,
                    'status': sub.status,
                    'start_date': sub.start_date.isoformat(),
                    'end_date': sub.end_date.isoformat(),
                    'price': sub.price
                } for sub in subscriptions
            ]
        })

    @app.route('/subscriptions/overview')
    @login_required
    def subscriptions_overview():
        return jsonify({'message': 'مخطط الاشتراكات - قيد التطوير'})

    @app.route('/subscriptions/add')
    @login_required
    def add_subscription():
        return jsonify({'message': 'إضافة اشتراك جديد - قيد التطوير'})

    @app.route('/subscriptions/analytics')
    @login_required
    def subscription_analytics():
        return jsonify({'message': 'تحليلات الاشتراكات - قيد التطوير'})

    @app.route('/subscriptions/reports')
    @login_required
    def subscription_reports():
        return jsonify({'message': 'تقارير الاشتراكات - قيد التطوير'})

    # مسارات إدارة الفواتير
    @app.route('/invoices')
    @login_required
    def invoices_list():
        invoices = Invoice.query.order_by(Invoice.created_at.desc()).all()
        return jsonify({
            'status': 'success',
            'data': [
                {
                    'id': inv.id,
                    'invoice_number': inv.invoice_number,
                    'customer': inv.customer.name,
                    'amount': inv.total_amount,
                    'status': inv.status,
                    'due_date': inv.due_date.isoformat()
                } for inv in invoices
            ]
        })

    @app.route('/invoices/create')
    @login_required
    def create_invoice():
        return jsonify({'message': 'إنشاء فاتورة جديدة - قيد التطوير'})

    @app.route('/invoices/reports')
    @login_required
    def invoice_reports():
        return jsonify({'message': 'تقارير الفواتير - قيد التطوير'})

    @app.route('/customers/statements')
    @login_required
    def customer_statements():
        return jsonify({'message': 'كشف حساب العملاء - قيد التطوير'})

    # مسارات أخرى
    @app.route('/payment-methods')
    @login_required
    def payment_methods():
        return jsonify({'message': 'طرق الدفع - قيد التطوير'})

    @app.route('/notifications/send')
    @login_required
    def send_notifications():
        return jsonify({'message': 'إرسال إشعارات - قيد التطوير'})

    @app.route('/reports/general')
    @login_required
    def general_reports():
        return jsonify({'message': 'التقارير العامة - قيد التطوير'})

    @app.route('/users/management')
    @login_required
    def users_management():
        return jsonify({'message': 'إدارة المستخدمين - قيد التطوير'})

    @app.route('/system/settings')
    @login_required
    def system_settings():
        return jsonify({'message': 'إعدادات النظام - قيد التطوير'})

    @app.route('/system/backup')
    @login_required
    def backup_restore():
        return jsonify({'message': 'النسخ الاحتياطي - قيد التطوير'})

    # API للإحصائيات المباشرة
    @app.route('/api/stats')
    @login_required
    def api_stats():
        stats = get_dashboard_stats()
        return jsonify(stats)

    @app.route('/api/notifications')
    @login_required
    def api_notifications():
        notifications = Notification.query.filter_by(
            user_id=current_user.id,
            is_read=False
        ).order_by(Notification.created_at.desc()).limit(10).all()

        return jsonify({
            'status': 'success',
            'data': [
                {
                    'id': notif.id,
                    'title': notif.title,
                    'message': notif.message,
                    'type': notif.type,
                    'created_at': notif.created_at.isoformat()
                } for notif in notifications
            ]
        })

    # تهيئة قاعدة البيانات
    def init_database():
        with app.app_context():
            db.create_all()

            # إنشاء مستخدم مدير إذا لم يكن موجوداً
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام - AdenLink',
                    role='admin',
                    phone='+964-XXX-XXXX'
                )
                admin.set_password('123456')
                db.session.add(admin)

                # إنشاء بيانات تجريبية
                create_sample_data()

                db.session.commit()
                print("✅ تم إنشاء المستخدم المدير والبيانات التجريبية")
            else:
                print("✅ المستخدم المدير موجود مسبقاً")

    def create_sample_data():
        """إنشاء بيانات تجريبية للنظام"""
        try:
            # إنشاء عملاء تجريبيين
            customers = [
                Customer(name='أحمد محمد', email='<EMAIL>', phone='+964-XXX-1111', company='شركة التقنية المتقدمة'),
                Customer(name='فاطمة علي', email='<EMAIL>', phone='+964-XXX-2222', company='مؤسسة الإبداع الرقمي'),
                Customer(name='محمد حسن', email='<EMAIL>', phone='+964-XXX-3333', company='شركة الحلول الذكية'),
            ]

            for customer in customers:
                db.session.add(customer)

            db.session.flush()  # للحصول على IDs

            # إنشاء خدمات تجريبية
            services = [
                Service(name='خادم VPS أساسي', description='خادم افتراضي بمواصفات أساسية', category='VPS', price=25.0, billing_cycle='monthly'),
                Service(name='خادم VPS متقدم', description='خادم افتراضي بمواصفات متقدمة', category='VPS', price=50.0, billing_cycle='monthly'),
                Service(name='استضافة مواقع', description='استضافة مواقع مشتركة', category='Hosting', price=10.0, billing_cycle='monthly'),
                Service(name='خادم مخصص', description='خادم مخصص بالكامل', category='Dedicated', price=200.0, billing_cycle='monthly'),
            ]

            for service in services:
                db.session.add(service)

            db.session.flush()

            # إنشاء اشتراكات تجريبية
            subscriptions = [
                Subscription(
                    customer_id=customers[0].id,
                    service_id=services[0].id,
                    user_id=1,
                    subscription_id=generate_subscription_id(),
                    status='active',
                    start_date=date.today() - timedelta(days=30),
                    end_date=date.today() + timedelta(days=30),
                    next_billing_date=date.today() + timedelta(days=30),
                    price=25.0,
                    server_name='server1.adenlink.com',
                    server_ip='*************'
                ),
                Subscription(
                    customer_id=customers[1].id,
                    service_id=services[1].id,
                    user_id=1,
                    subscription_id=generate_subscription_id(),
                    status='active',
                    start_date=date.today() - timedelta(days=15),
                    end_date=date.today() + timedelta(days=45),
                    next_billing_date=date.today() + timedelta(days=45),
                    price=50.0,
                    server_name='server2.adenlink.com',
                    server_ip='*************'
                ),
                Subscription(
                    customer_id=customers[2].id,
                    service_id=services[2].id,
                    user_id=1,
                    subscription_id=generate_subscription_id(),
                    status='active',
                    start_date=date.today() - timedelta(days=5),
                    end_date=date.today() + timedelta(days=25),
                    next_billing_date=date.today() + timedelta(days=25),
                    price=10.0,
                    server_name='web1.adenlink.com',
                    server_ip='*************'
                ),
            ]

            for subscription in subscriptions:
                db.session.add(subscription)

            db.session.flush()

            # إنشاء فواتير تجريبية
            invoices = [
                Invoice(
                    invoice_number=generate_invoice_number(),
                    customer_id=customers[0].id,
                    subscription_id=subscriptions[0].id,
                    user_id=1,
                    amount=25.0,
                    tax_amount=2.5,
                    total_amount=27.5,
                    due_date=date.today() + timedelta(days=7),
                    status='pending',
                    description='فاتورة خادم VPS أساسي'
                ),
                Invoice(
                    invoice_number=generate_invoice_number(),
                    customer_id=customers[1].id,
                    subscription_id=subscriptions[1].id,
                    user_id=1,
                    amount=50.0,
                    tax_amount=5.0,
                    total_amount=55.0,
                    due_date=date.today() + timedelta(days=14),
                    status='pending',
                    description='فاتورة خادم VPS متقدم'
                ),
            ]

            for invoice in invoices:
                db.session.add(invoice)

            print("✅ تم إنشاء البيانات التجريبية بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
            db.session.rollback()

    print("✅ تم إعداد المسارات والوظائف بنجاح")

    # تشغيل النظام
    if __name__ == '__main__':
        print("🔄 تهيئة قاعدة البيانات...")
        init_database()

        print("=" * 80)
        print("🎉 نظام إدارة الاشتراكات المتقدم جاهز للتشغيل!")
        print("=" * 80)
        print("🌐 معلومات الوصول:")
        print("   🔗 الرابط: http://localhost:5090")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: 123456")
        print("   🎨 التصميم: متقدم مع قائمة جانبية تفاعلية")
        print("   ⚡ الميزات: إدارة شاملة للاشتراكات والفواتير")
        print("=" * 80)
        print("🚀 بدء تشغيل الخادم...")

        try:
            app.run(
                debug=True,
                host='0.0.0.0',
                port=5090,
                threaded=True
            )
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل الخادم: {e}")

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("\n📦 يرجى تثبيت المكتبات المطلوبة:")
    print("pip install flask flask-sqlalchemy flask-login")
    print("\n💡 أو استخدم الأمر التالي لتثبيت جميع المتطلبات:")
    print("pip install flask flask-sqlalchemy flask-login werkzeug")

    input("\n⏸️ اضغط Enter للخروج...")
    exit(1)

except Exception as e:
    print(f"❌ خطأ في إعداد النظام: {e}")
    import traceback
    traceback.print_exc()

    input("\n⏸️ اضغط Enter للخروج...")
    exit(1)

print("\n⏹️ تم إيقاف النظام")
input("اضغط Enter للخروج...")
