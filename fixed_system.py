#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات - النسخة المصححة
"""

print("🚀 بدء تشغيل نظام إدارة الاشتراكات المصحح...")

try:
    from flask import Flask, render_template_string, request, redirect, url_for, flash
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
    from werkzeug.security import generate_password_hash, check_password_hash
    from datetime import datetime, date
    import os
    
    print("✅ تم تحميل المكتبات بنجاح")
    
    # إعداد التطبيق
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'subscription-system-secret-key-2024-fixed'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///fixed_subscriptions.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # إعداد قاعدة البيانات
    db = SQLAlchemy(app)
    
    # إعداد نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # نماذج قاعدة البيانات
    class User(UserMixin, db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        password_hash = db.Column(db.String(200), nullable=False)
        role = db.Column(db.String(20), default='user')
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        def set_password(self, password):
            self.password_hash = generate_password_hash(password)
        
        def check_password(self, password):
            return check_password_hash(self.password_hash, password)
        
        def is_admin(self):
            return self.role == 'admin'
    
    class Subscription(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        price = db.Column(db.Float, nullable=False)
        start_date = db.Column(db.Date, nullable=False)
        end_date = db.Column(db.Date, nullable=False)
        status = db.Column(db.String(20), default='active')
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        user = db.relationship('User', backref='subscriptions')

    class Invoice(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        invoice_number = db.Column(db.String(50), unique=True, nullable=False)
        amount = db.Column(db.Float, nullable=False)
        currency = db.Column(db.String(3), default='USD')
        status = db.Column(db.String(20), default='pending', nullable=False)
        issue_date = db.Column(db.Date, default=datetime.now, nullable=False)
        due_date = db.Column(db.Date, nullable=False)
        paid_date = db.Column(db.Date)
        payment_method = db.Column(db.String(50))
        notes = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.now, nullable=False)
        
        subscription = db.relationship('Subscription', backref='invoices')
        user = db.relationship('User', backref='invoices')
    
    print("✅ تم إعداد النماذج بنجاح")
    
    # قوالب HTML بنفس تصميم أمس
    LOGIN_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - نظام إدارة الاشتراكات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .login-container {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                padding: 3rem;
                width: 100%;
                max-width: 450px;
                backdrop-filter: blur(10px);
            }
            
            .logo-section {
                text-align: center;
                margin-bottom: 2rem;
            }
            
            .logo-icon {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                width: 80px;
                height: 80px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1rem;
                font-size: 2rem;
            }
            
            .system-title {
                color: #2c3e50;
                font-weight: 700;
                font-size: 1.8rem;
                margin-bottom: 0.5rem;
            }
            
            .system-subtitle {
                color: #7f8c8d;
                font-size: 1rem;
                margin-bottom: 0;
            }
            
            .form-group {
                margin-bottom: 1.5rem;
            }
            
            .form-label {
                color: #2c3e50;
                font-weight: 600;
                margin-bottom: 0.5rem;
                display: block;
            }
            
            .form-control {
                border: 2px solid #e9ecef;
                border-radius: 12px;
                padding: 1rem;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: rgba(255, 255, 255, 0.9);
            }
            
            .form-control:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
                background: white;
            }
            
            .btn-login {
                background: linear-gradient(135deg, #667eea, #764ba2);
                border: none;
                border-radius: 12px;
                padding: 1rem 2rem;
                font-weight: 600;
                font-size: 1.1rem;
                width: 100%;
                color: white;
                transition: all 0.3s ease;
                margin-top: 1rem;
            }
            
            .btn-login:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
                color: white;
            }
            
            .login-info {
                background: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 8px;
                padding: 1rem;
                margin-top: 1.5rem;
                text-align: center;
            }
            
            .login-info small {
                color: #0c5460;
                font-weight: 500;
            }
            
            .alert {
                border-radius: 12px;
                border: none;
                margin-bottom: 1.5rem;
            }
            
            .alert-success {
                background: linear-gradient(135deg, #d4edda, #c3e6cb);
                color: #155724;
            }
            
            .alert-danger {
                background: linear-gradient(135deg, #f8d7da, #f5c6cb);
                color: #721c24;
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-cloud"></i>
                </div>
                <h1 class="system-title">نظام إدارة الاشتراكات</h1>
                <p class="system-subtitle">AdenLink - العراق</p>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show">
                            <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user me-2"></i>اسم المستخدم
                    </label>
                    <input type="text" class="form-control" name="username" required 
                           placeholder="أدخل اسم المستخدم" autocomplete="username">
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-lock me-2"></i>كلمة المرور
                    </label>
                    <input type="password" class="form-control" name="password" required 
                           placeholder="أدخل كلمة المرور" autocomplete="current-password">
                </div>
                
                <button type="submit" class="btn btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </button>
            </form>
            
            <div class="login-info">
                <small>
                    <i class="fas fa-info-circle me-1"></i>
                    <strong>بيانات التجربة:</strong><br>
                    المستخدم: <code>admin</code> | كلمة المرور: <code>123456</code>
                </small>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    '''

    DASHBOARD_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>لوحة التحكم - نظام إدارة الاشتراكات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                min-height: 100vh;
                color: #333;
                overflow-x: hidden;
            }

            /* تأثيرات متحركة للخلفية */
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
                z-index: -1;
                animation: backgroundMove 20s ease-in-out infinite;
            }

            @keyframes backgroundMove {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-20px); }
            }

            .navbar {
                background: rgba(255, 255, 255, 0.95) !important;
                backdrop-filter: blur(10px);
                box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }

            .navbar-brand {
                color: #2c3e50 !important;
                font-weight: 700;
                font-size: 1.5rem;
            }

            .navbar-nav .nav-link {
                color: #2c3e50 !important;
                font-weight: 500;
                margin: 0 0.5rem;
                padding: 0.5rem 1rem !important;
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .navbar-nav .nav-link:hover {
                background: rgba(102, 126, 234, 0.1);
                color: #667eea !important;
            }

            .main-container {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 30px;
                margin: 2rem auto;
                padding: 3rem;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
                backdrop-filter: blur(20px);
                max-width: 900px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                position: relative;
                overflow: hidden;
            }

            .main-container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #667eea);
                background-size: 200% 100%;
                animation: gradientMove 3s ease-in-out infinite;
            }

            @keyframes gradientMove {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            /* القائمة الجانبية */
            .sidebar {
                position: fixed;
                right: 0;
                top: 0;
                width: 320px;
                height: 100vh;
                background: linear-gradient(180deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
                backdrop-filter: blur(20px);
                border-left: 1px solid rgba(255,255,255,0.3);
                box-shadow: -10px 0 30px rgba(0,0,0,0.1);
                z-index: 1000;
                overflow-y: auto;
                transition: all 0.3s ease;
            }

            .sidebar-header {
                padding: 2rem 1.5rem 1rem;
                border-bottom: 1px solid rgba(0,0,0,0.1);
                text-align: center;
            }

            .sidebar-title {
                font-size: 1.3rem;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 0.5rem;
                background: linear-gradient(135deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .sidebar-subtitle {
                font-size: 0.9rem;
                color: #7f8c8d;
                margin-bottom: 0;
            }

            .sidebar-menu {
                padding: 1rem 0;
            }

            .menu-section {
                margin-bottom: 1.5rem;
            }

            .menu-section-title {
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
                font-weight: 600;
                color: #2c3e50;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
                border-radius: 0 25px 25px 0;
                margin-left: 1rem;
                display: flex;
                align-items: center;
                transition: all 0.3s ease;
            }

            .menu-section-title:hover {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
                transform: translateX(-5px);
            }

            .menu-section-icon {
                margin-left: 0.75rem;
                font-size: 1.1rem;
            }

            .menu-section-description {
                font-size: 0.8rem;
                color: #7f8c8d;
                padding: 0.5rem 1.5rem;
                margin-bottom: 0.75rem;
                line-height: 1.4;
            }

            .menu-items {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .menu-item {
                margin-bottom: 0.25rem;
            }

            .menu-item-link {
                display: flex;
                align-items: center;
                padding: 0.75rem 1.5rem 0.75rem 2rem;
                color: #5a6c7d;
                text-decoration: none;
                font-size: 0.9rem;
                font-weight: 500;
                transition: all 0.3s ease;
                border-radius: 0 25px 25px 0;
                margin-left: 1rem;
            }

            .menu-item-link:hover {
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
                color: #667eea;
                text-decoration: none;
                transform: translateX(-5px);
            }

            .menu-item-icon {
                margin-left: 0.75rem;
                font-size: 0.9rem;
                width: 16px;
                text-align: center;
            }

            .menu-divider {
                height: 1px;
                background: linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent);
                margin: 1rem 1.5rem;
            }

            .sidebar-footer {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                padding: 1.5rem;
                background: linear-gradient(180deg, transparent, rgba(255,255,255,0.9));
                border-top: 1px solid rgba(0,0,0,0.1);
            }

            .current-user {
                display: flex;
                align-items: center;
                padding: 0.75rem;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
                border-radius: 15px;
                margin-bottom: 1rem;
            }

            .user-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea, #764ba2);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 600;
                margin-left: 0.75rem;
            }

            .user-info {
                flex: 1;
            }

            .user-name {
                font-weight: 600;
                color: #2c3e50;
                font-size: 0.9rem;
                margin-bottom: 0.25rem;
            }

            .user-role {
                font-size: 0.8rem;
                color: #7f8c8d;
                margin-bottom: 0;
            }

            .search-box {
                position: relative;
            }

            .search-input {
                width: 100%;
                padding: 0.75rem 1rem 0.75rem 2.5rem;
                border: 2px solid rgba(102, 126, 234, 0.2);
                border-radius: 25px;
                background: rgba(255,255,255,0.8);
                font-size: 0.9rem;
                transition: all 0.3s ease;
            }

            .search-input:focus {
                outline: none;
                border-color: #667eea;
                background: white;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            }

            .search-icon {
                position: absolute;
                right: 1rem;
                top: 50%;
                transform: translateY(-50%);
                color: #7f8c8d;
                font-size: 0.9rem;
            }

            /* تعديل المحتوى الرئيسي */
            .main-content {
                margin-left: 340px;
                transition: all 0.3s ease;
            }

            /* إخفاء/إظهار القائمة الجانبية */
            .sidebar-toggle {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1001;
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                border: none;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
                cursor: pointer;
                box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
                transition: all 0.3s ease;
                display: none;
            }

            .sidebar-toggle:hover {
                transform: scale(1.1);
                box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            }

            /* استجابة للشاشات الصغيرة */
            @media (max-width: 1200px) {
                .sidebar {
                    transform: translateX(100%);
                }

                .sidebar.active {
                    transform: translateX(0);
                }

                .main-content {
                    margin-left: 0;
                }

                .sidebar-toggle {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            .welcome-header {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                border-radius: 15px;
                padding: 2rem;
                margin-bottom: 2rem;
                text-align: center;
            }

            .welcome-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
            }

            .welcome-subtitle {
                font-size: 1.2rem;
                opacity: 0.9;
                margin-bottom: 0;
            }

            .stats-container {
                margin-bottom: 3rem;
            }

            .stat-card {
                background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
                border-radius: 20px;
                padding: 2rem;
                margin-bottom: 1.5rem;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                transition: all 0.4s ease;
                border: 1px solid rgba(255,255,255,0.2);
                backdrop-filter: blur(10px);
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .stat-card:hover {
                transform: translateY(-8px) scale(1.02);
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
                background: linear-gradient(135deg, #fff, rgba(255,255,255,0.95));
            }

            .stat-content {
                display: flex;
                align-items: center;
                flex: 1;
            }

            .stat-icon {
                width: 70px;
                height: 70px;
                border-radius: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 2rem;
                font-size: 2rem;
                color: white;
                box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            }

            .stat-icon.primary {
                background: linear-gradient(135deg, #667eea, #764ba2);
                box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            }
            .stat-icon.success {
                background: linear-gradient(135deg, #11998e, #38ef7d);
                box-shadow: 0 10px 25px rgba(17, 153, 142, 0.4);
            }
            .stat-icon.warning {
                background: linear-gradient(135deg, #ff6b6b, #ffa726);
                box-shadow: 0 10px 25px rgba(255, 107, 107, 0.4);
            }
            .stat-icon.info {
                background: linear-gradient(135deg, #4facfe, #00f2fe);
                box-shadow: 0 10px 25px rgba(79, 172, 254, 0.4);
            }

            .stat-details {
                flex: 1;
            }

            .stat-number {
                font-size: 2.5rem;
                font-weight: 800;
                color: #2c3e50;
                margin-bottom: 0.5rem;
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .stat-label {
                color: #5a6c7d;
                font-weight: 600;
                font-size: 1.1rem;
                margin-bottom: 0;
            }

            .features-container {
                margin-top: 2rem;
            }

            .feature-card {
                background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
                border-radius: 25px;
                padding: 2.5rem;
                margin-bottom: 2rem;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                transition: all 0.4s ease;
                border: 1px solid rgba(255,255,255,0.2);
                backdrop-filter: blur(10px);
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .feature-card:hover {
                transform: translateY(-10px) scale(1.02);
                box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
                background: linear-gradient(135deg, #fff, rgba(255,255,255,0.95));
            }

            .feature-content {
                display: flex;
                align-items: center;
                flex: 1;
            }

            .feature-icon {
                width: 90px;
                height: 90px;
                border-radius: 25px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 2rem;
                font-size: 2.5rem;
                color: white;
                box-shadow: 0 15px 30px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            }

            .feature-card:nth-child(1) .feature-icon {
                background: linear-gradient(135deg, #667eea, #764ba2);
                box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            }

            .feature-card:nth-child(2) .feature-icon {
                background: linear-gradient(135deg, #11998e, #38ef7d);
                box-shadow: 0 15px 30px rgba(17, 153, 142, 0.4);
            }

            .feature-card:nth-child(3) .feature-icon {
                background: linear-gradient(135deg, #ff6b6b, #ffa726);
                box-shadow: 0 15px 30px rgba(255, 107, 107, 0.4);
            }

            .feature-card:nth-child(4) .feature-icon {
                background: linear-gradient(135deg, #4facfe, #00f2fe);
                box-shadow: 0 15px 30px rgba(79, 172, 254, 0.4);
            }

            .feature-card:nth-child(5) .feature-icon {
                background: linear-gradient(135deg, #a8edea, #fed6e3);
                box-shadow: 0 15px 30px rgba(168, 237, 234, 0.4);
            }

            .feature-card:nth-child(6) .feature-icon {
                background: linear-gradient(135deg, #ffecd2, #fcb69f);
                box-shadow: 0 15px 30px rgba(255, 236, 210, 0.4);
            }

            .feature-card:hover .feature-icon {
                transform: rotate(5deg) scale(1.1);
            }

            .feature-details {
                flex: 1;
            }

            .feature-title {
                font-size: 1.6rem;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 1rem;
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .feature-description {
                color: #5a6c7d;
                margin-bottom: 1.5rem;
                line-height: 1.8;
                font-size: 1.1rem;
            }

            .feature-btn {
                background: linear-gradient(135deg, #667eea, #764ba2);
                border: none;
                border-radius: 30px;
                padding: 1rem 2rem;
                color: white;
                text-decoration: none;
                font-weight: 600;
                font-size: 1.1rem;
                transition: all 0.3s ease;
                display: inline-block;
                box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            }

            .feature-btn:hover {
                transform: translateY(-3px);
                box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
                color: white;
                text-decoration: none;
                background: linear-gradient(135deg, #764ba2, #667eea);
            }

            .success-alert {
                background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9));
                border: 1px solid rgba(17, 153, 142, 0.3);
                border-radius: 25px;
                padding: 3rem;
                margin-top: 3rem;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                backdrop-filter: blur(10px);
                transition: all 0.4s ease;
            }

            .success-alert:hover {
                transform: translateY(-5px);
                box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
            }

            .success-alert .alert-icon {
                font-size: 4rem;
                background: linear-gradient(135deg, #11998e, #38ef7d);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: 1.5rem;
                display: block;
            }

            .success-alert h4 {
                color: #2c3e50;
                font-weight: 700;
                font-size: 1.8rem;
                margin-bottom: 1rem;
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .success-alert p {
                color: #5a6c7d;
                margin-bottom: 0;
                font-size: 1.2rem;
                line-height: 1.6;
            }
        </style>
    </head>
    <body>
        <!-- زر إظهار/إخفاء القائمة الجانبية -->
        <button class="sidebar-toggle" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>

        <!-- القائمة الجانبية -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3 class="sidebar-title">نظام إدارة الاشتراكات</h3>
                <p class="sidebar-subtitle">AdenLink - العراق</p>
            </div>

            <div class="sidebar-menu">
                <!-- لوحة المعلومات -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-tachometer-alt menu-section-icon"></i>
                        لوحة المعلومات
                    </div>
                    <div class="menu-section-description">
                        عرض إجمالي الإحصائيات، الاشتراكات، التنبيهات، الرسوم البيانية العامة
                    </div>
                </div>

                <div class="menu-divider"></div>

                <!-- إدارة الاشتراكات -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-server menu-section-icon"></i>
                        إدارة الاشتراكات
                    </div>
                    <div class="menu-section-description">
                        إدارة وتفصيل الاشتراكات، وتحتوي على:
                    </div>
                    <ul class="menu-items">
                        <li class="menu-item">
                            <a href="#" class="menu-item-link">
                                <i class="fas fa-chart-pie menu-item-icon"></i>
                                مخطط الاشتراكات
                            </a>
                        </li>
                        <li class="menu-item">
                            <a href="#" class="menu-item-link">
                                <i class="fas fa-list menu-item-icon"></i>
                                قائمة الاشتراكات
                            </a>
                        </li>
                        <li class="menu-item">
                            <a href="#" class="menu-item-link">
                                <i class="fas fa-credit-card menu-item-icon"></i>
                                طرق الدفع
                            </a>
                        </li>
                        <li class="menu-item">
                            <a href="#" class="menu-item-link">
                                <i class="fas fa-plus menu-item-icon"></i>
                                إضافة اشتراك جديد
                            </a>
                        </li>
                        <li class="menu-item">
                            <a href="#" class="menu-item-link">
                                <i class="fas fa-chart-line menu-item-icon"></i>
                                تحليلات الاشتراكات
                            </a>
                        </li>
                        <li class="menu-item">
                            <a href="#" class="menu-item-link">
                                <i class="fas fa-file-alt menu-item-icon"></i>
                                تقارير الاشتراكات
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="menu-divider"></div>

                <!-- إدارة الفواتير -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-file-invoice menu-section-icon"></i>
                        إدارة الفواتير
                    </div>
                    <div class="menu-section-description">
                        إدارة كل ما يتعلق بالفواتير، وتحتوي على:
                    </div>
                    <ul class="menu-items">
                        <li class="menu-item">
                            <a href="#" class="menu-item-link">
                                <i class="fas fa-list menu-item-icon"></i>
                                قائمة الفواتير
                            </a>
                        </li>
                        <li class="menu-item">
                            <a href="#" class="menu-item-link">
                                <i class="fas fa-plus menu-item-icon"></i>
                                إنشاء فاتورة جديدة
                            </a>
                        </li>
                        <li class="menu-item">
                            <a href="#" class="menu-item-link">
                                <i class="fas fa-chart-bar menu-item-icon"></i>
                                تقارير الفواتير
                            </a>
                        </li>
                        <li class="menu-item">
                            <a href="#" class="menu-item-link">
                                <i class="fas fa-receipt menu-item-icon"></i>
                                كشف حساب العملاء
                                <span style="color: #28a745; font-size: 0.7rem; margin-right: 0.5rem;">✅ جديد</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="menu-divider"></div>

                <!-- مركز التواصل -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-envelope menu-section-icon"></i>
                        مركز التواصل
                    </div>
                    <div class="menu-section-description">
                        إرسال تنبيهات أو رسائل للعملاء
                    </div>
                </div>

                <div class="menu-divider"></div>

                <!-- التقارير العامة -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-chart-area menu-section-icon"></i>
                        التقارير العامة
                    </div>
                    <div class="menu-section-description">
                        تقارير شاملة (مثل عدد الاشتراكات الشهري، التحصيل، النشاط)
                    </div>
                </div>

                <div class="menu-divider"></div>

                <!-- إدارة المستخدمين -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-users menu-section-icon"></i>
                        إدارة المستخدمين
                    </div>
                    <div class="menu-section-description">
                        التحكم بالمستخدمين وصلاحياتهم (مدير، موظف، مشاهد…)
                    </div>
                </div>

                <div class="menu-divider"></div>

                <!-- الإدارة المتقدمة -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-cogs menu-section-icon"></i>
                        الإدارة المتقدمة
                    </div>
                    <div class="menu-section-description">
                        صلاحيات متقدمة، الإعدادات، النسخ الاحتياطي، تكامل مع API
                    </div>
                </div>
            </div>

            <div class="sidebar-footer">
                <div class="current-user">
                    <div class="user-avatar">
                        {{ current_user.full_name[0] }}
                    </div>
                    <div class="user-info">
                        <div class="user-name">{{ current_user.full_name }}</div>
                        <div class="user-role">مدير النظام</div>
                    </div>
                </div>

                <div class="search-box">
                    <input type="text" class="search-input" placeholder="اكتب هنا للبحث...">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
        </div>

        <!-- الشريط العلوي -->
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-cloud me-2"></i>نظام إدارة الاشتراكات
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text me-3">
                        <i class="fas fa-user me-1"></i>{{ current_user.full_name }}
                    </span>
                    <a class="nav-link" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt me-1"></i>خروج
                    </a>
                </div>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <div class="container">
                <div class="main-container">
                <div class="welcome-header">
                    <h1 class="welcome-title">
                        <i class="fas fa-home me-3"></i>مرحباً {{ current_user.full_name }}
                    </h1>
                    <p class="welcome-subtitle">
                        مرحباً بك في نظام إدارة الاشتراكات المتطور - AdenLink
                    </p>
                </div>

                <!-- إحصائيات سريعة بتصميم طولي -->
                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-details">
                                <div class="stat-number">25</div>
                                <div class="stat-label">إجمالي الاشتراكات</div>
                            </div>
                            <div class="stat-icon primary">
                                <i class="fas fa-server"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-details">
                                <div class="stat-number">18</div>
                                <div class="stat-label">اشتراكات نشطة</div>
                            </div>
                            <div class="stat-icon success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-details">
                                <div class="stat-number">3</div>
                                <div class="stat-label">تنتهي قريباً</div>
                            </div>
                            <div class="stat-icon warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-details">
                                <div class="stat-number">$2,450</div>
                                <div class="stat-label">إجمالي الإيرادات</div>
                            </div>
                            <div class="stat-icon info">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الميزات الرئيسية بتصميم طولي -->
                <div class="features-container">
                    <div class="feature-card">
                        <div class="feature-content">
                            <div class="feature-details">
                                <h3 class="feature-title">إدارة الاشتراكات</h3>
                                <p class="feature-description">
                                    إدارة شاملة لجميع الاشتراكات والخوادم السحابية مع تتبع الحالة والأداء المتقدم
                                </p>
                                <a href="#" class="feature-btn">
                                    <i class="fas fa-arrow-left me-2"></i>عرض الاشتراكات
                                </a>
                            </div>
                            <div class="feature-icon">
                                <i class="fas fa-server"></i>
                            </div>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-content">
                            <div class="feature-details">
                                <h3 class="feature-title">التقارير والإحصائيات</h3>
                                <p class="feature-description">
                                    تقارير تفصيلية ومخططات بيانية تفاعلية لتحليل الأداء والإيرادات بدقة عالية
                                </p>
                                <a href="#" class="feature-btn">
                                    <i class="fas fa-chart-bar me-2"></i>عرض التقارير
                                </a>
                            </div>
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-content">
                            <div class="feature-details">
                                <h3 class="feature-title">نظام الرسائل المتطور</h3>
                                <p class="feature-description">
                                    إرسال رسائل تلقائية وإشعارات للعملاء مع قوالب جاهزة ومتغيرات ديناميكية متقدمة
                                </p>
                                <a href="#" class="feature-btn">
                                    <i class="fas fa-paper-plane me-2"></i>إرسال رسالة
                                </a>
                            </div>
                            <div class="feature-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-content">
                            <div class="feature-details">
                                <h3 class="feature-title">إدارة الفواتير الذكية</h3>
                                <p class="feature-description">
                                    إنشاء وإدارة الفواتير وتتبع المدفوعات مع حساب الضرائب والخصومات التلقائية
                                </p>
                                <a href="#" class="feature-btn">
                                    <i class="fas fa-receipt me-2"></i>عرض الفواتير
                                </a>
                            </div>
                            <div class="feature-icon">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-content">
                            <div class="feature-details">
                                <h3 class="feature-title">الذكاء الاصطناعي</h3>
                                <p class="feature-description">
                                    رؤى ذكية وتوصيات مدعومة بالذكاء الاصطناعي لتحسين الأداء وزيادة الكفاءة
                                </p>
                                <a href="#" class="feature-btn">
                                    <i class="fas fa-robot me-2"></i>الرؤى الذكية
                                </a>
                            </div>
                            <div class="feature-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-content">
                            <div class="feature-details">
                                <h3 class="feature-title">إعدادات النظام المتقدمة</h3>
                                <p class="feature-description">
                                    تخصيص وإعداد النظام حسب احتياجاتك مع إعدادات متقدمة وخيارات شخصية
                                </p>
                                <a href="#" class="feature-btn">
                                    <i class="fas fa-wrench me-2"></i>الإعدادات
                                </a>
                            </div>
                            <div class="feature-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- رسالة النجاح -->
                <div class="success-alert">
                    <div class="alert-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h4>تهانينا! النظام يعمل بنجاح</h4>
                    <p>تم إصلاح جميع المشاكل والنظام جاهز للاستخدام بالتصميم المحدث</p>
                </div>
            </div>
        </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            // التحكم في القائمة الجانبية
            function toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('active');
            }

            // إغلاق القائمة عند النقر خارجها في الشاشات الصغيرة
            document.addEventListener('click', function(event) {
                const sidebar = document.getElementById('sidebar');
                const toggleBtn = document.querySelector('.sidebar-toggle');

                if (window.innerWidth <= 1200) {
                    if (!sidebar.contains(event.target) && !toggleBtn.contains(event.target)) {
                        sidebar.classList.remove('active');
                    }
                }
            });

            // تأثيرات تفاعلية للقائمة
            document.querySelectorAll('.menu-item-link').forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(-10px)';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(-5px)';
                });
            });

            // تأثير البحث
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });

            searchInput.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });

            // تأثيرات تحميل الصفحة
            window.addEventListener('load', function() {
                const menuSections = document.querySelectorAll('.menu-section');
                menuSections.forEach((section, index) => {
                    setTimeout(() => {
                        section.style.opacity = '0';
                        section.style.transform = 'translateX(20px)';
                        section.style.transition = 'all 0.5s ease';

                        setTimeout(() => {
                            section.style.opacity = '1';
                            section.style.transform = 'translateX(0)';
                        }, 100);
                    }, index * 100);
                });
            });
        </script>
    </body>
    </html>
    '''

    # المسارات
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')

            user = User.query.filter_by(username=username).first()

            if user and user.check_password(password):
                login_user(user)
                flash('تم تسجيل الدخول بنجاح! مرحباً بك في النظام المصحح 🎉', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

        return render_template_string(LOGIN_TEMPLATE)

    @app.route('/dashboard')
    @login_required
    def dashboard():
        return render_template_string(DASHBOARD_TEMPLATE)

    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('login'))

    # تهيئة قاعدة البيانات
    def init_db():
        with app.app_context():
            db.create_all()

            # إنشاء مستخدم مدير إذا لم يكن موجوداً
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام - AdenLink',
                    role='admin'
                )
                admin.set_password('123456')
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء المستخدم المدير")
            else:
                print("✅ المستخدم المدير موجود مسبقاً")

    print("✅ تم إعداد المسارات بنجاح")

    # تهيئة قاعدة البيانات
    print("🔄 تهيئة قاعدة البيانات...")
    init_db()

    print("=" * 60)
    print("🎉 نظام إدارة الاشتراكات جاهز للتشغيل!")
    print("=" * 60)
    print("🌐 معلومات الوصول:")
    print("   🔗 الرابط: http://localhost:5090")
    print("   👤 اسم المستخدم: admin")
    print("   🔑 كلمة المرور: 123456")
    print("   🔧 الحالة: تم إصلاح جميع المشاكل")
    print("=" * 60)

    # تشغيل التطبيق
    app.run(debug=True, host='0.0.0.0', port=5090)

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("يرجى تثبيت المكتبات المطلوبة:")
    print("pip install flask flask-sqlalchemy flask-login")

except Exception as e:
    print(f"❌ خطأ في تشغيل النظام: {e}")
    import traceback
    traceback.print_exc()

print("\n⏹️ تم إيقاف النظام")
input("اضغط Enter للخروج...")
