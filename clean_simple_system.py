#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام إدارة الاشتراكات المبسط والنظيف
AdenLink - العراق
بدون لوحة التحكم المعقدة - نظام بسيط وسريع
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta

print("🌟 بدء تشغيل النظام المبسط والنظيف...")

# إعداد التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'clean-simple-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///clean_simple.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# نماذج قاعدة البيانات البسيطة
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(200), nullable=False)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20))

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    service_name = db.Column(db.String(200), nullable=False)
    price = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='active')
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    
    customer = db.relationship('Customer', backref='subscriptions')

print("✅ تم إعداد النماذج البسيطة بنجاح")

# قالب تسجيل الدخول البسيط
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - النظام المبسط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Arial', sans-serif;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 450px;
        }
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo h2 {
            color: #667eea;
            font-weight: bold;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            padding: 12px;
            border-radius: 10px;
        }
        .form-control {
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h2>🚀 النظام المبسط</h2>
            <p class="text-muted">AdenLink - العراق</p>
            <p class="text-success"><small>✅ بدون لوحة تحكم معقدة</small></p>
        </div>
        
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <div class="mb-3">
                <label class="form-label">اسم المستخدم</label>
                <input type="text" class="form-control" name="username" placeholder="أدخل اسم المستخدم" required>
            </div>
            <div class="mb-3">
                <label class="form-label">كلمة المرور</label>
                <input type="password" class="form-control" name="password" placeholder="أدخل كلمة المرور" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
            </button>
        </form>
        
        <div class="text-center mt-4 p-3" style="background: #f8f9fa; border-radius: 10px;">
            <h6 class="text-primary">بيانات التجربة</h6>
            <p class="mb-1"><strong>المستخدم:</strong> admin</p>
            <p class="mb-0"><strong>كلمة المرور:</strong> 123456</p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

# قالب الصفحة الرئيسية البسيطة
MAIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة الرئيسية - النظام المبسط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            color: white;
        }
        .navbar {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .navbar-brand, .nav-link {
            color: white !important;
            font-weight: 600;
        }
        .main-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #00d4ff;
        }
        .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: white;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-rocket me-2"></i>النظام المبسط
            </a>
            <div class="navbar-nav ms-auto">
                <span class="nav-link">
                    <i class="fas fa-user me-1"></i>مرحباً، {{ current_user.full_name }}
                </span>
                <a href="{{ url_for('logout') }}" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="main-card">
            <div class="card-body p-5">
                <div class="text-center mb-5">
                    <h1 class="display-4 mb-3">
                        <i class="fas fa-check-circle text-success me-3"></i>
                        مرحباً بك في النظام المبسط
                    </h1>
                    <p class="lead">تم حذف لوحة التحكم المعقدة كما طلبت - النظام الآن بسيط وسريع</p>
                </div>
                
                <div class="row g-4 mb-5">
                    <div class="col-md-3">
                        <div class="stat-card card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x text-primary mb-3"></i>
                                <div class="stat-number">{{ stats.customers }}</div>
                                <h6 class="card-title">إجمالي العملاء</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-server fa-2x text-info mb-3"></i>
                                <div class="stat-number">{{ stats.subscriptions }}</div>
                                <h6 class="card-title">إجمالي الاشتراكات</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                                <div class="stat-number">{{ stats.active }}</div>
                                <h6 class="card-title">اشتراكات نشطة</h6>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-dollar-sign fa-2x text-warning mb-3"></i>
                                <div class="stat-number">${{ stats.revenue }}</div>
                                <h6 class="card-title">إجمالي الإيرادات</h6>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="alert alert-success d-flex align-items-center">
                            <i class="fas fa-check-circle fa-2x me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-1">تم بنجاح!</h5>
                                <p class="mb-0">حذف لوحة التحكم المتجاوبة المعقدة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info d-flex align-items-center">
                            <i class="fas fa-rocket fa-2x me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-1">النظام محسن!</h5>
                                <p class="mb-0">أداء سريع وواجهة بسيطة ونظيفة</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-5">
                    <div class="alert alert-light text-dark">
                        <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                        <p class="mb-0">
                            هذا النظام مبسط وخالي من التعقيدات - يحتوي على الوظائف الأساسية فقط
                            <br>
                            <small class="text-muted">AdenLink - العراق | النسخة المبسطة 2024</small>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

# المسارات البسيطة
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('main'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user)
            flash('🎉 مرحباً بك في النظام المبسط! تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('main'))
        else:
            flash('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('✅ تم تسجيل الخروج بنجاح من النظام المبسط', 'success')
    return redirect(url_for('login'))

@app.route('/main')
@login_required
def main():
    try:
        stats = {
            'customers': Customer.query.count(),
            'subscriptions': Subscription.query.count(),
            'active': Subscription.query.filter_by(status='active').count(),
            'revenue': int(db.session.query(db.func.sum(Subscription.price)).scalar() or 0)
        }
        return render_template_string(MAIN_TEMPLATE, stats=stats)
    except Exception as e:
        print(f"خطأ في الصفحة الرئيسية: {e}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return render_template_string(MAIN_TEMPLATE, stats={
            'customers': 0, 'subscriptions': 0, 'active': 0, 'revenue': 0
        })

print("✅ تم إعداد المسارات البسيطة بنجاح")

# تهيئة قاعدة البيانات البسيطة
def init_simple_db():
    with app.app_context():
        try:
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات البسيطة")
            
            # إنشاء مستخدم مدير
            if not User.query.filter_by(username='admin').first():
                admin = User(username='admin', full_name='المدير العام - النظام المبسط')
                admin.set_password('123456')
                db.session.add(admin)
                
                # إنشاء بيانات تجريبية بسيطة
                customers = [
                    Customer(name='شركة التقنيات البسيطة', email='<EMAIL>', phone='123456789'),
                    Customer(name='مؤسسة الحلول السهلة', email='<EMAIL>', phone='987654321'),
                    Customer(name='شركة الخدمات المبسطة', email='<EMAIL>', phone='555666777'),
                ]
                
                for customer in customers:
                    db.session.add(customer)
                
                db.session.flush()
                
                subscriptions = [
                    Subscription(
                        customer_id=1, service_name='استضافة ويب بسيطة', price=50.0,
                        start_date=date.today(), end_date=date.today() + timedelta(days=365)
                    ),
                    Subscription(
                        customer_id=2, service_name='نسخ احتياطي', price=25.0,
                        start_date=date.today(), end_date=date.today() + timedelta(days=180)
                    ),
                    Subscription(
                        customer_id=3, service_name='بريد إلكتروني', price=15.0,
                        start_date=date.today(), end_date=date.today() + timedelta(days=90)
                    ),
                ]
                
                for subscription in subscriptions:
                    db.session.add(subscription)
                
                db.session.commit()
                print("✅ تم إنشاء البيانات التجريبية البسيطة")
                
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            db.session.rollback()

# تشغيل النظام المبسط
if __name__ == '__main__':
    print("🔄 تهيئة قاعدة البيانات البسيطة...")
    init_simple_db()
    
    print("=" * 70)
    print("🎉 النظام المبسط والنظيف جاهز للتشغيل!")
    print("=" * 70)
    print("🌐 الرابط: http://localhost:5095")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: 123456")
    print()
    print("✅ المميزات:")
    print("   🚫 تم حذف لوحة التحكم المعقدة")
    print("   ✅ واجهة بسيطة ونظيفة")
    print("   ✅ أداء سريع ومحسن")
    print("   ✅ إحصائيات أساسية فقط")
    print("   ✅ تصميم مبسط وواضح")
    print("=" * 70)
    
    try:
        app.run(debug=True, host='0.0.0.0', port=5095)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام المبسط")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")

print("\n🙏 شكراً لاستخدام النظام المبسط!")
print("✨ تم حذف لوحة التحكم المعقدة كما طلبت")
