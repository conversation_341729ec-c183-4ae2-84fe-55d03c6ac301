#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل بسيط لنظام إدارة الاشتراكات
"""

import os
import sys

# تغيير المجلد الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(current_dir)

print("🚀 بدء تشغيل نظام إدارة الاشتراكات...")
print(f"📂 المجلد الحالي: {os.getcwd()}")

try:
    # تحقق من وجود الملف
    if not os.path.exists('advanced_subscription_system.py'):
        print("❌ ملف النظام غير موجود!")
        sys.exit(1)

    print("✅ تم العثور على ملف النظام")

    # تشغيل النظام الرئيسي
    with open('advanced_subscription_system.py', 'r', encoding='utf-8') as f:
        code = f.read()

    print("✅ تم قراءة ملف النظام")
    print("🔄 بدء تشغيل النظام...")

    exec(code)

except FileNotFoundError as e:
    print(f"❌ ملف غير موجود: {e}")

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("يرجى تثبيت المكتبات المطلوبة:")
    print("pip install flask flask-sqlalchemy flask-login reportlab")

except Exception as e:
    print(f"❌ خطأ في تشغيل النظام: {e}")
    import traceback
    traceback.print_exc()

print("\n⏹️ تم إيقاف النظام")
input("اضغط Enter للخروج...")
