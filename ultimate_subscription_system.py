#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام إدارة الاشتراكات المتطور والشامل
AdenLink - العراق
النسخة النهائية مع جميع الميزات المتقدمة
"""

print("🌟 بدء تشغيل نظام إدارة الاشتراكات المتطور والشامل...")

try:
    from flask import Flask, render_template_string, request, redirect, url_for, flash, jsonify
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
    from werkzeug.security import generate_password_hash, check_password_hash
    from datetime import datetime, date, timedelta
    import uuid
    import os
    import json
    
    print("✅ تم تحميل المكتبات بنجاح")
    
    # إعداد التطبيق
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'ultimate-subscription-system-2024-adenlink'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ultimate_subscriptions.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # إعداد قاعدة البيانات
    db = SQLAlchemy(app)
    
    # إعداد نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    
    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))
    
    # نماذج قاعدة البيانات المتطورة
    class User(UserMixin, db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        password_hash = db.Column(db.String(200), nullable=False)
        role = db.Column(db.String(20), default='user')  # admin, user
        company = db.Column(db.String(200))
        phone = db.Column(db.String(20))
        avatar_url = db.Column(db.String(500))
        is_active = db.Column(db.Boolean, default=True)
        last_login = db.Column(db.DateTime)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        def set_password(self, password):
            self.password_hash = generate_password_hash(password)
        
        def check_password(self, password):
            return check_password_hash(self.password_hash, password)

    class ServiceProvider(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(100), nullable=False)
        code = db.Column(db.String(20), unique=True, nullable=False)
        description = db.Column(db.Text)
        website = db.Column(db.String(200))
        logo_url = db.Column(db.String(500))
        api_endpoint = db.Column(db.String(200))
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.now)

    class PaymentMethod(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(100), nullable=False)
        code = db.Column(db.String(20), unique=True, nullable=False)
        description = db.Column(db.Text)
        processing_fee = db.Column(db.Float, default=0.0)
        fee_type = db.Column(db.String(20), default='percentage')  # percentage, fixed
        is_active = db.Column(db.Boolean, default=True)
        icon_class = db.Column(db.String(50))
        created_at = db.Column(db.DateTime, default=datetime.now)

    class Currency(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(50), nullable=False)
        code = db.Column(db.String(3), unique=True, nullable=False)
        symbol = db.Column(db.String(10), nullable=False)
        exchange_rate = db.Column(db.Float, default=1.0)
        is_default = db.Column(db.Boolean, default=False)
        created_at = db.Column(db.DateTime, default=datetime.now)

    class Customer(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        phone = db.Column(db.String(20))
        company = db.Column(db.String(200))
        address = db.Column(db.Text)
        country = db.Column(db.String(100))
        city = db.Column(db.String(100))
        postal_code = db.Column(db.String(20))
        tax_id = db.Column(db.String(50))
        preferred_currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'))
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        preferred_currency = db.relationship('Currency', backref='customers')

    class Service(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        description = db.Column(db.Text)
        category = db.Column(db.String(100))
        provider_id = db.Column(db.Integer, db.ForeignKey('service_provider.id'), nullable=False)
        base_price = db.Column(db.Float, nullable=False)
        currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'), nullable=False)
        billing_cycle = db.Column(db.String(20), default='monthly')  # monthly, semi_annual, annual
        setup_fee = db.Column(db.Float, default=0.0)
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        provider = db.relationship('ServiceProvider', backref='services')
        currency = db.relationship('Currency', backref='services')

    class Subscription(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        subscription_id = db.Column(db.String(50), unique=True, nullable=False)
        customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
        service_id = db.Column(db.Integer, db.ForeignKey('service.id'), nullable=False)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        
        # تفاصيل الاشتراك
        status = db.Column(db.String(20), default='active')  # active, suspended, expired, cancelled
        priority = db.Column(db.String(20), default='normal')  # low, normal, high, critical
        billing_cycle = db.Column(db.String(20), default='monthly')
        
        # التواريخ
        start_date = db.Column(db.Date, nullable=False)
        end_date = db.Column(db.Date, nullable=False)
        next_billing_date = db.Column(db.Date)
        last_payment_date = db.Column(db.Date)
        
        # التسعير
        price = db.Column(db.Float, nullable=False)
        setup_fee = db.Column(db.Float, default=0.0)
        discount_percentage = db.Column(db.Float, default=0.0)
        currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'), nullable=False)
        
        # معلومات الخادم
        server_name = db.Column(db.String(200))
        server_ip = db.Column(db.String(45))
        server_port = db.Column(db.Integer)
        api_key = db.Column(db.String(500))
        api_secret = db.Column(db.String(500))
        control_panel_url = db.Column(db.String(500))
        
        # ملاحظات وتفاصيل إضافية
        notes = db.Column(db.Text)
        tags = db.Column(db.String(500))  # JSON array of tags
        
        created_at = db.Column(db.DateTime, default=datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
        
        # العلاقات
        customer = db.relationship('Customer', backref='subscriptions')
        service = db.relationship('Service', backref='subscriptions')
        user = db.relationship('User', backref='subscriptions')
        currency = db.relationship('Currency', backref='subscriptions')

    class Invoice(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        invoice_number = db.Column(db.String(50), unique=True, nullable=False)
        customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
        subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'))
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        
        # المبالغ
        subtotal = db.Column(db.Float, nullable=False)
        tax_rate = db.Column(db.Float, default=0.0)
        tax_amount = db.Column(db.Float, default=0.0)
        discount_amount = db.Column(db.Float, default=0.0)
        total_amount = db.Column(db.Float, nullable=False)
        currency_id = db.Column(db.Integer, db.ForeignKey('currency.id'), nullable=False)
        
        # التواريخ
        issue_date = db.Column(db.Date, default=date.today)
        due_date = db.Column(db.Date, nullable=False)
        paid_date = db.Column(db.Date)
        
        # الحالة والدفع
        status = db.Column(db.String(20), default='pending')  # pending, paid, overdue, cancelled
        payment_method_id = db.Column(db.Integer, db.ForeignKey('payment_method.id'))
        payment_reference = db.Column(db.String(200))
        
        # تفاصيل إضافية
        description = db.Column(db.Text)
        notes = db.Column(db.Text)
        
        created_at = db.Column(db.DateTime, default=datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
        
        # العلاقات
        customer = db.relationship('Customer', backref='invoices')
        subscription = db.relationship('Subscription', backref='invoices')
        user = db.relationship('User', backref='invoices')
        currency = db.relationship('Currency', backref='invoices')
        payment_method = db.relationship('PaymentMethod', backref='invoices')

    class Notification(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        title = db.Column(db.String(200), nullable=False)
        message = db.Column(db.Text, nullable=False)
        type = db.Column(db.String(20), default='info')  # info, warning, error, success
        priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
        
        # الحالة والتفاعل
        is_read = db.Column(db.Boolean, default=False)
        read_at = db.Column(db.DateTime)
        action_url = db.Column(db.String(500))
        action_text = db.Column(db.String(100))
        
        # ربط بالكائنات
        related_subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'))
        related_invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'))
        
        created_at = db.Column(db.DateTime, default=datetime.now)
        expires_at = db.Column(db.DateTime)
        
        # العلاقات
        user = db.relationship('User', backref='notifications')
        related_subscription = db.relationship('Subscription', backref='notifications')
        related_invoice = db.relationship('Invoice', backref='notifications')

    class ActivityLog(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        action = db.Column(db.String(100), nullable=False)
        description = db.Column(db.Text)
        ip_address = db.Column(db.String(45))
        user_agent = db.Column(db.String(500))
        
        # ربط بالكائنات
        related_table = db.Column(db.String(50))
        related_id = db.Column(db.Integer)
        
        created_at = db.Column(db.DateTime, default=datetime.now)
        
        # العلاقات
        user = db.relationship('User', backref='activity_logs')

    print("✅ تم إعداد النماذج المتطورة بنجاح")

    # دوال مساعدة متطورة
    def generate_subscription_id():
        """توليد رقم اشتراك فريد"""
        return f"SUB-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"

    def generate_invoice_number():
        """توليد رقم فاتورة فريد"""
        return f"INV-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"

    def log_activity(action, description, related_table=None, related_id=None):
        """تسجيل نشاط المستخدم"""
        try:
            if current_user.is_authenticated:
                activity = ActivityLog(
                    user_id=current_user.id,
                    action=action,
                    description=description,
                    ip_address=request.remote_addr if request else None,
                    user_agent=request.user_agent.string if request else None,
                    related_table=related_table,
                    related_id=related_id
                )
                db.session.add(activity)
                db.session.commit()
        except Exception as e:
            print(f"خطأ في تسجيل النشاط: {e}")

    def create_notification(user_id, title, message, type='info', priority='normal',
                          action_url=None, action_text=None, related_subscription_id=None,
                          related_invoice_id=None, expires_days=30):
        """إنشاء إشعار جديد"""
        try:
            notification = Notification(
                user_id=user_id,
                title=title,
                message=message,
                type=type,
                priority=priority,
                action_url=action_url,
                action_text=action_text,
                related_subscription_id=related_subscription_id,
                related_invoice_id=related_invoice_id,
                expires_at=datetime.now() + timedelta(days=expires_days)
            )
            db.session.add(notification)
            db.session.commit()
            return notification
        except Exception as e:
            print(f"خطأ في إنشاء الإشعار: {e}")
            return None

    def check_expiring_subscriptions():
        """فحص الاشتراكات التي تنتهي قريباً وإرسال تنبيهات"""
        try:
            # البحث عن الاشتراكات التي تنتهي خلال 30 يوم
            expiring_date = date.today() + timedelta(days=30)
            expiring_subscriptions = Subscription.query.filter(
                Subscription.status == 'active',
                Subscription.end_date <= expiring_date,
                Subscription.end_date > date.today()
            ).all()

            for subscription in expiring_subscriptions:
                days_remaining = (subscription.end_date - date.today()).days

                # إنشاء إشعار للمدير
                admin_users = User.query.filter_by(role='admin').all()
                for admin in admin_users:
                    create_notification(
                        user_id=admin.id,
                        title=f"⚠️ اشتراك ينتهي قريباً",
                        message=f"اشتراك العميل {subscription.customer.name} في خدمة {subscription.service.name} سينتهي خلال {days_remaining} يوم",
                        type='warning',
                        priority='high' if days_remaining <= 7 else 'normal',
                        action_url=f"/subscriptions/{subscription.id}",
                        action_text="عرض الاشتراك",
                        related_subscription_id=subscription.id
                    )

            return len(expiring_subscriptions)
        except Exception as e:
            print(f"خطأ في فحص الاشتراكات المنتهية: {e}")
            return 0

    def get_dashboard_stats():
        """حساب إحصائيات لوحة التحكم المتطورة"""
        try:
            stats = {
                # إحصائيات أساسية
                'total_customers': Customer.query.filter_by(is_active=True).count(),
                'total_subscriptions': Subscription.query.count(),
                'active_subscriptions': Subscription.query.filter_by(status='active').count(),
                'suspended_subscriptions': Subscription.query.filter_by(status='suspended').count(),
                'expired_subscriptions': Subscription.query.filter_by(status='expired').count(),

                # إحصائيات الفواتير
                'total_invoices': Invoice.query.count(),
                'pending_invoices': Invoice.query.filter_by(status='pending').count(),
                'paid_invoices': Invoice.query.filter_by(status='paid').count(),
                'overdue_invoices': Invoice.query.filter_by(status='overdue').count(),

                # الإيرادات
                'monthly_revenue': db.session.query(db.func.sum(Invoice.total_amount)).filter(
                    Invoice.status == 'paid',
                    Invoice.paid_date >= date.today().replace(day=1)
                ).scalar() or 0,

                'yearly_revenue': db.session.query(db.func.sum(Invoice.total_amount)).filter(
                    Invoice.status == 'paid',
                    Invoice.paid_date >= date.today().replace(month=1, day=1)
                ).scalar() or 0,

                # الاشتراكات المنتهية قريباً
                'expiring_soon': Subscription.query.filter(
                    Subscription.status == 'active',
                    Subscription.end_date <= date.today() + timedelta(days=30),
                    Subscription.end_date > date.today()
                ).count(),

                'expiring_this_week': Subscription.query.filter(
                    Subscription.status == 'active',
                    Subscription.end_date <= date.today() + timedelta(days=7),
                    Subscription.end_date > date.today()
                ).count(),

                # إحصائيات مزودي الخدمة
                'total_providers': ServiceProvider.query.filter_by(is_active=True).count(),
                'total_services': Service.query.filter_by(is_active=True).count(),

                # إحصائيات الإشعارات
                'unread_notifications': Notification.query.filter_by(
                    user_id=current_user.id if current_user.is_authenticated else 1,
                    is_read=False
                ).count() if current_user.is_authenticated else 0,

                # إحصائيات حسب دورة الفوترة
                'monthly_subscriptions': Subscription.query.filter_by(
                    billing_cycle='monthly', status='active'
                ).count(),
                'semi_annual_subscriptions': Subscription.query.filter_by(
                    billing_cycle='semi_annual', status='active'
                ).count(),
                'annual_subscriptions': Subscription.query.filter_by(
                    billing_cycle='annual', status='active'
                ).count(),

                # إحصائيات حسب الأولوية
                'high_priority_subscriptions': Subscription.query.filter_by(
                    priority='high', status='active'
                ).count(),
                'critical_priority_subscriptions': Subscription.query.filter_by(
                    priority='critical', status='active'
                ).count(),
            }

            # حساب متوسط قيمة الاشتراك
            avg_subscription_value = db.session.query(db.func.avg(Subscription.price)).filter(
                Subscription.status == 'active'
            ).scalar()
            stats['avg_subscription_value'] = round(avg_subscription_value or 0, 2)

            # حساب معدل النمو الشهري
            last_month_subscriptions = Subscription.query.filter(
                Subscription.created_at >= date.today().replace(day=1) - timedelta(days=30),
                Subscription.created_at < date.today().replace(day=1)
            ).count()

            current_month_subscriptions = Subscription.query.filter(
                Subscription.created_at >= date.today().replace(day=1)
            ).count()

            if last_month_subscriptions > 0:
                growth_rate = ((current_month_subscriptions - last_month_subscriptions) / last_month_subscriptions) * 100
                stats['monthly_growth_rate'] = round(growth_rate, 1)
            else:
                stats['monthly_growth_rate'] = 0

            return stats

        except Exception as e:
            print(f"خطأ في حساب الإحصائيات: {e}")
            return {
                'total_customers': 0,
                'total_subscriptions': 0,
                'active_subscriptions': 0,
                'total_invoices': 0,
                'pending_invoices': 0,
                'monthly_revenue': 0,
                'expiring_soon': 0
            }

    print("✅ تم إعداد الدوال المساعدة المتطورة بنجاح")

    # قوالب HTML متطورة مع تصميم Glassmorphism
    LOGIN_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🚀 تسجيل الدخول - نظام إدارة الاشتراكات المتطور</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
        <style>
            :root {
                --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                --neon-blue: #00d4ff;
                --neon-purple: #b537f2;
                --neon-pink: #ff006e;
                --glass-bg: rgba(255, 255, 255, 0.1);
                --glass-border: rgba(255, 255, 255, 0.2);
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;
                position: relative;
            }

            /* خلفية متحركة مع تأثيرات نيون */
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(181, 55, 242, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(255, 0, 110, 0.3) 0%, transparent 50%);
                z-index: -2;
                animation: backgroundPulse 8s ease-in-out infinite;
            }

            @keyframes backgroundPulse {
                0%, 100% {
                    transform: scale(1) rotate(0deg);
                    opacity: 0.8;
                }
                50% {
                    transform: scale(1.1) rotate(2deg);
                    opacity: 1;
                }
            }

            /* جزيئات متحركة */
            .particles {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: -1;
                overflow: hidden;
            }

            .particle {
                position: absolute;
                width: 4px;
                height: 4px;
                background: var(--neon-blue);
                border-radius: 50%;
                animation: float 6s ease-in-out infinite;
                box-shadow: 0 0 10px var(--neon-blue);
            }

            .particle:nth-child(2n) {
                background: var(--neon-purple);
                box-shadow: 0 0 10px var(--neon-purple);
                animation-delay: -2s;
            }

            .particle:nth-child(3n) {
                background: var(--neon-pink);
                box-shadow: 0 0 10px var(--neon-pink);
                animation-delay: -4s;
            }

            @keyframes float {
                0%, 100% {
                    transform: translateY(0px) translateX(0px) scale(1);
                    opacity: 0.7;
                }
                33% {
                    transform: translateY(-20px) translateX(10px) scale(1.1);
                    opacity: 1;
                }
                66% {
                    transform: translateY(-10px) translateX(-10px) scale(0.9);
                    opacity: 0.8;
                }
            }

            /* حاوي تسجيل الدخول مع Glassmorphism */
            .login-container {
                background: var(--glass-bg);
                backdrop-filter: blur(25px);
                border: 2px solid var(--glass-border);
                border-radius: 30px;
                padding: 3rem;
                width: 100%;
                max-width: 480px;
                box-shadow:
                    0 25px 50px rgba(0, 0, 0, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
                position: relative;
                overflow: hidden;
                animation: slideInUp 1s ease-out;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(50px) scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            .login-container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
                background-size: 200% 100%;
                animation: gradientShift 3s ease-in-out infinite;
            }

            @keyframes gradientShift {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            /* قسم الشعار */
            .logo-section {
                text-align: center;
                margin-bottom: 2.5rem;
                position: relative;
            }

            .logo-icon {
                width: 100px;
                height: 100px;
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1.5rem;
                color: white;
                font-size: 2.5rem;
                box-shadow:
                    0 20px 40px rgba(0, 212, 255, 0.3),
                    0 0 30px rgba(181, 55, 242, 0.4);
                animation: logoFloat 3s ease-in-out infinite;
                position: relative;
            }

            .logo-icon::before {
                content: '';
                position: absolute;
                top: -5px;
                left: -5px;
                right: -5px;
                bottom: -5px;
                background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
                border-radius: 50%;
                z-index: -1;
                animation: rotate 4s linear infinite;
                opacity: 0.7;
            }

            @keyframes logoFloat {
                0%, 100% { transform: translateY(0px) scale(1); }
                50% { transform: translateY(-10px) scale(1.05); }
            }

            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .system-title {
                color: white;
                font-weight: 800;
                font-size: 2rem;
                margin-bottom: 0.5rem;
                text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
                animation: titleGlow 2s ease-in-out infinite alternate;
            }

            @keyframes titleGlow {
                from { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
                to { text-shadow: 0 0 30px rgba(0, 212, 255, 0.8); }
            }

            .system-subtitle {
                color: rgba(255, 255, 255, 0.8);
                font-size: 1.1rem;
                font-weight: 500;
                margin-bottom: 0;
            }

            .features-badge {
                display: inline-block;
                background: linear-gradient(135deg, var(--neon-pink), var(--neon-purple));
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.9rem;
                font-weight: 600;
                margin-top: 1rem;
                box-shadow: 0 10px 20px rgba(255, 0, 110, 0.3);
                animation: badgePulse 2s ease-in-out infinite;
            }

            @keyframes badgePulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }

            /* نماذج الإدخال المتطورة */
            .form-group {
                margin-bottom: 1.5rem;
                position: relative;
            }

            .form-label {
                color: white;
                font-weight: 600;
                font-size: 1rem;
                margin-bottom: 0.5rem;
                display: flex;
                align-items: center;
                text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
            }

            .form-label i {
                margin-left: 0.5rem;
                color: var(--neon-blue);
                text-shadow: 0 0 10px var(--neon-blue);
            }

            .form-control {
                background: var(--glass-bg);
                backdrop-filter: blur(10px);
                border: 2px solid var(--glass-border);
                border-radius: 20px;
                padding: 1rem 1.5rem;
                font-size: 1rem;
                color: white;
                transition: all 0.3s ease;
                width: 100%;
            }

            .form-control::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }

            .form-control:focus {
                outline: none;
                border-color: var(--neon-blue);
                box-shadow:
                    0 0 0 0.2rem rgba(0, 212, 255, 0.25),
                    0 0 20px rgba(0, 212, 255, 0.4);
                background: rgba(255, 255, 255, 0.15);
                transform: translateY(-2px);
            }

            /* زر تسجيل الدخول المتطور */
            .btn-login {
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                border: none;
                border-radius: 20px;
                padding: 1rem 2rem;
                font-weight: 700;
                font-size: 1.1rem;
                width: 100%;
                color: white;
                transition: all 0.3s ease;
                margin-top: 1rem;
                position: relative;
                overflow: hidden;
                text-transform: uppercase;
                letter-spacing: 1px;
                box-shadow: 0 15px 30px rgba(0, 212, 255, 0.3);
            }

            .btn-login::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                transition: left 0.5s ease;
            }

            .btn-login:hover::before {
                left: 100%;
            }

            .btn-login:hover {
                transform: translateY(-3px);
                box-shadow:
                    0 20px 40px rgba(0, 212, 255, 0.4),
                    0 0 30px rgba(181, 55, 242, 0.3);
                background: linear-gradient(135deg, var(--neon-purple), var(--neon-pink));
            }

            .btn-login:active {
                transform: translateY(-1px);
            }

            /* تنبيهات متطورة */
            .alert {
                background: var(--glass-bg);
                backdrop-filter: blur(15px);
                border: 2px solid var(--glass-border);
                border-radius: 15px;
                border: none;
                margin-bottom: 1.5rem;
                padding: 1rem 1.5rem;
                color: white;
                animation: alertSlide 0.5s ease-out;
            }

            @keyframes alertSlide {
                from {
                    opacity: 0;
                    transform: translateX(50px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            .alert-success {
                border-left: 4px solid var(--neon-blue);
                box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            }

            .alert-danger {
                border-left: 4px solid var(--neon-pink);
                box-shadow: 0 0 20px rgba(255, 0, 110, 0.3);
            }

            /* معلومات التجربة */
            .login-info {
                background: var(--glass-bg);
                backdrop-filter: blur(15px);
                border: 2px solid var(--glass-border);
                border-radius: 20px;
                padding: 1.5rem;
                margin-top: 2rem;
                text-align: center;
                color: white;
                position: relative;
                overflow: hidden;
            }

            .login-info::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple));
                animation: infoGlow 2s ease-in-out infinite;
            }

            @keyframes infoGlow {
                0%, 100% { opacity: 0.5; }
                50% { opacity: 1; }
            }

            .login-info code {
                background: rgba(0, 212, 255, 0.2);
                color: var(--neon-blue);
                padding: 0.3rem 0.6rem;
                border-radius: 8px;
                font-weight: 600;
                text-shadow: 0 0 10px var(--neon-blue);
            }

            /* تأثيرات إضافية */
            .floating-elements {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: -1;
            }

            .floating-element {
                position: absolute;
                width: 20px;
                height: 20px;
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                animation: floatAround 10s linear infinite;
            }

            @keyframes floatAround {
                0% {
                    transform: translateY(100vh) rotate(0deg);
                    opacity: 0;
                }
                10% {
                    opacity: 1;
                }
                90% {
                    opacity: 1;
                }
                100% {
                    transform: translateY(-100px) rotate(360deg);
                    opacity: 0;
                }
            }

            /* استجابة للشاشات الصغيرة */
            @media (max-width: 768px) {
                .login-container {
                    margin: 1rem;
                    padding: 2rem;
                    max-width: none;
                }

                .logo-icon {
                    width: 80px;
                    height: 80px;
                    font-size: 2rem;
                }

                .system-title {
                    font-size: 1.5rem;
                }

                .system-subtitle {
                    font-size: 1rem;
                }
            }
        </style>
    </head>
    <body>
        <!-- جزيئات متحركة -->
        <div class="particles">
            <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
            <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
            <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
            <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
            <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
            <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
            <div class="particle" style="left: 70%; animation-delay: 0.5s;"></div>
            <div class="particle" style="left: 80%; animation-delay: 1.5s;"></div>
            <div class="particle" style="left: 90%; animation-delay: 2.5s;"></div>
        </div>

        <!-- عناصر عائمة -->
        <div class="floating-elements">
            <div class="floating-element" style="left: 5%; animation-delay: 0s;"></div>
            <div class="floating-element" style="left: 15%; animation-delay: 2s;"></div>
            <div class="floating-element" style="left: 25%; animation-delay: 4s;"></div>
            <div class="floating-element" style="left: 35%; animation-delay: 6s;"></div>
            <div class="floating-element" style="left: 45%; animation-delay: 8s;"></div>
            <div class="floating-element" style="left: 55%; animation-delay: 1s;"></div>
            <div class="floating-element" style="left: 65%; animation-delay: 3s;"></div>
            <div class="floating-element" style="left: 75%; animation-delay: 5s;"></div>
            <div class="floating-element" style="left: 85%; animation-delay: 7s;"></div>
            <div class="floating-element" style="left: 95%; animation-delay: 9s;"></div>
        </div>

        <div class="login-container">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h1 class="system-title">نظام إدارة الاشتراكات المتطور</h1>
                <p class="system-subtitle">AdenLink - العراق</p>
                <div class="features-badge">
                    ✨ 7 مزودين • 6 طرق دفع • 5 عملات ✨
                </div>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }}">
                            <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user"></i>اسم المستخدم
                    </label>
                    <input type="text" class="form-control" name="username" required
                           placeholder="أدخل اسم المستخدم" autocomplete="username">
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-lock"></i>كلمة المرور
                    </label>
                    <input type="password" class="form-control" name="password" required
                           placeholder="أدخل كلمة المرور" autocomplete="current-password">
                </div>

                <button type="submit" class="btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </button>
            </form>

            <div class="login-info">
                <div style="font-weight: 600; margin-bottom: 1rem;">
                    <i class="fas fa-info-circle me-2" style="color: var(--neon-blue);"></i>
                    <strong>بيانات التجربة المتطورة</strong>
                </div>
                <div style="margin-bottom: 0.5rem;">
                    👤 المستخدم: <code>admin</code>
                </div>
                <div style="margin-bottom: 1rem;">
                    🔑 كلمة المرور: <code>123456</code>
                </div>
                <div style="font-size: 0.9rem; opacity: 0.8;">
                    🎯 النظام يحتوي على بيانات تجريبية شاملة جاهزة للاستكشاف
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            // تأثيرات تفاعلية إضافية
            document.addEventListener('DOMContentLoaded', function() {
                // إضافة تأثير الكتابة للعنوان
                const title = document.querySelector('.system-title');
                const originalText = title.textContent;
                title.textContent = '';

                let i = 0;
                const typeWriter = () => {
                    if (i < originalText.length) {
                        title.textContent += originalText.charAt(i);
                        i++;
                        setTimeout(typeWriter, 100);
                    }
                };

                setTimeout(typeWriter, 1000);

                // تأثير التركيز على الحقول
                const inputs = document.querySelectorAll('.form-control');
                inputs.forEach(input => {
                    input.addEventListener('focus', function() {
                        this.parentElement.style.transform = 'scale(1.02)';
                        this.parentElement.style.transition = 'transform 0.3s ease';
                    });

                    input.addEventListener('blur', function() {
                        this.parentElement.style.transform = 'scale(1)';
                    });
                });

                // تأثير النقر على الزر
                const loginBtn = document.querySelector('.btn-login');
                loginBtn.addEventListener('click', function(e) {
                    // إضافة تأثير الموجة
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255, 255, 255, 0.3)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.pointerEvents = 'none';

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // إضافة CSS للتأثير المتموج
            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        </script>
    </body>
    </html>
    '''

    # المسارات والوظائف المتطورة
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')

            user = User.query.filter_by(username=username).first()

            if user and user.check_password(password):
                login_user(user)
                user.last_login = datetime.now()
                db.session.commit()

                log_activity('تسجيل دخول', f'تم تسجيل الدخول بنجاح من IP: {request.remote_addr}')
                flash('🎉 مرحباً بك في النظام المتطور! تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('dashboard'))
            else:
                log_activity('محاولة دخول فاشلة', f'محاولة دخول فاشلة لاسم المستخدم: {username}')
                flash('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

        return render_template_string(LOGIN_TEMPLATE)

    @app.route('/logout')
    @login_required
    def logout():
        log_activity('تسجيل خروج', 'تم تسجيل الخروج من النظام')
        logout_user()
        flash('✅ تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('login'))

    @app.route('/dashboard')
    @login_required
    def dashboard():
        # فحص الاشتراكات المنتهية وإرسال تنبيهات
        check_expiring_subscriptions()

        # حساب الإحصائيات المتطورة
        stats = get_dashboard_stats()

        # أحدث الاشتراكات
        recent_subscriptions = Subscription.query.order_by(Subscription.created_at.desc()).limit(8).all()

        # الفواتير المعلقة
        pending_invoices = Invoice.query.filter_by(status='pending').order_by(Invoice.due_date.asc()).limit(8).all()

        # الإشعارات غير المقروءة
        notifications = Notification.query.filter_by(
            user_id=current_user.id,
            is_read=False
        ).order_by(Notification.created_at.desc()).limit(5).all()

        log_activity('عرض لوحة التحكم', 'تم الوصول إلى لوحة التحكم الرئيسية')

        return render_template_string(
            DASHBOARD_TEMPLATE,
            stats=stats,
            recent_subscriptions=recent_subscriptions,
            pending_invoices=pending_invoices,
            notifications=notifications
        )

    # تهيئة قاعدة البيانات مع البيانات المتطورة
    def init_database():
        with app.app_context():
            db.create_all()

            # إنشاء مستخدم مدير إذا لم يكن موجوداً
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='المدير العام - AdenLink',
                    role='admin',
                    company='AdenLink - العراق',
                    phone='+964-XXX-XXXX',
                    avatar_url='https://via.placeholder.com/150/667eea/ffffff?text=AL'
                )
                admin.set_password('123456')
                db.session.add(admin)

                # إنشاء البيانات المتطورة
                create_advanced_sample_data()

                db.session.commit()
                print("✅ تم إنشاء المستخدم المدير والبيانات المتطورة")
            else:
                print("✅ المستخدم المدير موجود مسبقاً")

    def create_advanced_sample_data():
        """إنشاء بيانات تجريبية متطورة وشاملة"""
        try:
            # إنشاء العملات المدعومة
            currencies = [
                Currency(name='دولار أمريكي', code='USD', symbol='$', exchange_rate=1.0, is_default=True),
                Currency(name='يورو', code='EUR', symbol='€', exchange_rate=0.85),
                Currency(name='دينار عراقي', code='IQD', symbol='د.ع', exchange_rate=1310.0),
                Currency(name='ريال سعودي', code='SAR', symbol='ر.س', exchange_rate=3.75),
                Currency(name='درهم إماراتي', code='AED', symbol='د.إ', exchange_rate=3.67),
            ]

            for currency in currencies:
                db.session.add(currency)

            db.session.flush()

            # إنشاء مزودي الخدمة المتطورين
            providers = [
                ServiceProvider(
                    name='AdenLink',
                    code='ADENLINK',
                    description='مزود الخدمات السحابية الرائد في العراق',
                    website='https://adenlink.com',
                    logo_url='https://via.placeholder.com/100/667eea/ffffff?text=AL',
                    api_endpoint='https://api.adenlink.com/v1'
                ),
                ServiceProvider(
                    name='Amazon Web Services',
                    code='AWS',
                    description='منصة الحوسبة السحابية الرائدة عالمياً',
                    website='https://aws.amazon.com',
                    logo_url='https://via.placeholder.com/100/ff9900/ffffff?text=AWS',
                    api_endpoint='https://ec2.amazonaws.com'
                ),
                ServiceProvider(
                    name='Google Cloud Platform',
                    code='GCP',
                    description='منصة جوجل للحوسبة السحابية',
                    website='https://cloud.google.com',
                    logo_url='https://via.placeholder.com/100/4285f4/ffffff?text=GCP',
                    api_endpoint='https://compute.googleapis.com'
                ),
                ServiceProvider(
                    name='Microsoft Azure',
                    code='AZURE',
                    description='منصة مايكروسوفت للحوسبة السحابية',
                    website='https://azure.microsoft.com',
                    logo_url='https://via.placeholder.com/100/0078d4/ffffff?text=AZ',
                    api_endpoint='https://management.azure.com'
                ),
                ServiceProvider(
                    name='DigitalOcean',
                    code='DO',
                    description='منصة سحابية بسيطة ومرنة للمطورين',
                    website='https://digitalocean.com',
                    logo_url='https://via.placeholder.com/100/0080ff/ffffff?text=DO',
                    api_endpoint='https://api.digitalocean.com/v2'
                ),
                ServiceProvider(
                    name='Vultr',
                    code='VULTR',
                    description='خوادم سحابية عالية الأداء',
                    website='https://vultr.com',
                    logo_url='https://via.placeholder.com/100/007bfc/ffffff?text=VU',
                    api_endpoint='https://api.vultr.com/v2'
                ),
                ServiceProvider(
                    name='Linode',
                    code='LINODE',
                    description='خوادم لينكس السحابية المتطورة',
                    website='https://linode.com',
                    logo_url='https://via.placeholder.com/100/00b04f/ffffff?text=LI',
                    api_endpoint='https://api.linode.com/v4'
                ),
            ]

            for provider in providers:
                db.session.add(provider)

            db.session.flush()

            # إنشاء طرق الدفع المتنوعة
            payment_methods = [
                PaymentMethod(
                    name='بطاقة ائتمان/خصم',
                    code='CARD',
                    description='فيزا، ماستركارد، أمريكان إكسبريس',
                    processing_fee=2.9,
                    fee_type='percentage',
                    icon_class='fas fa-credit-card'
                ),
                PaymentMethod(
                    name='PayPal',
                    code='PAYPAL',
                    description='محفظة PayPal الإلكترونية',
                    processing_fee=3.4,
                    fee_type='percentage',
                    icon_class='fab fa-paypal'
                ),
                PaymentMethod(
                    name='تحويل بنكي',
                    code='BANK',
                    description='تحويل مصرفي مباشر',
                    processing_fee=5.0,
                    fee_type='fixed',
                    icon_class='fas fa-university'
                ),
                PaymentMethod(
                    name='عملات رقمية',
                    code='CRYPTO',
                    description='بيتكوين، إيثريوم، وعملات أخرى',
                    processing_fee=1.5,
                    fee_type='percentage',
                    icon_class='fab fa-bitcoin'
                ),
                PaymentMethod(
                    name='محافظ إلكترونية',
                    code='EWALLET',
                    description='Skrill، Neteller، Perfect Money',
                    processing_fee=2.5,
                    fee_type='percentage',
                    icon_class='fas fa-wallet'
                ),
                PaymentMethod(
                    name='دفع نقدي',
                    code='CASH',
                    description='دفع نقدي في المكتب',
                    processing_fee=0.0,
                    fee_type='fixed',
                    icon_class='fas fa-money-bill-wave'
                ),
            ]

            for method in payment_methods:
                db.session.add(method)

            db.session.flush()

            print("✅ تم إنشاء البيانات الأساسية المتطورة بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات المتطورة: {e}")
            db.session.rollback()

    print("✅ تم إعداد المسارات والوظائف المتطورة بنجاح")

    # قالب لوحة التحكم المتطور مع Glassmorphism
    DASHBOARD_TEMPLATE = '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🚀 لوحة التحكم المتطورة - نظام إدارة الاشتراكات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
        <style>
            :root {
                --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
                --neon-blue: #00d4ff;
                --neon-purple: #b537f2;
                --neon-pink: #ff006e;
                --neon-green: #39ff14;
                --glass-bg: rgba(255, 255, 255, 0.1);
                --glass-border: rgba(255, 255, 255, 0.2);
                --sidebar-width: 350px;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
                min-height: 100vh;
                color: white;
                overflow-x: hidden;
                position: relative;
            }

            /* خلفية متحركة متطورة */
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.4) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(181, 55, 242, 0.4) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(255, 0, 110, 0.4) 0%, transparent 50%),
                    radial-gradient(circle at 60% 80%, rgba(57, 255, 20, 0.3) 0%, transparent 50%);
                z-index: -2;
                animation: backgroundFlow 15s ease-in-out infinite;
            }

            @keyframes backgroundFlow {
                0%, 100% {
                    transform: scale(1) rotate(0deg);
                    opacity: 0.8;
                }
                33% {
                    transform: scale(1.1) rotate(1deg);
                    opacity: 1;
                }
                66% {
                    transform: scale(0.9) rotate(-1deg);
                    opacity: 0.9;
                }
            }

            /* القائمة الجانبية المتطورة */
            .sidebar {
                position: fixed;
                right: 0;
                top: 0;
                width: var(--sidebar-width);
                height: 100vh;
                background: var(--glass-bg);
                backdrop-filter: blur(25px);
                border-left: 2px solid var(--glass-border);
                box-shadow: -20px 0 40px rgba(0, 0, 0, 0.3);
                z-index: 1000;
                overflow-y: auto;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                transform: translateX(0);
            }

            .sidebar::-webkit-scrollbar {
                width: 8px;
            }

            .sidebar::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
            }

            .sidebar::-webkit-scrollbar-thumb {
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                border-radius: 10px;
                box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            }

            .sidebar-header {
                padding: 2rem 1.5rem;
                text-align: center;
                border-bottom: 2px solid var(--glass-border);
                background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(181, 55, 242, 0.1));
                position: relative;
                overflow: hidden;
            }

            .sidebar-header::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
                background-size: 200% 100%;
                animation: gradientShift 3s ease-in-out infinite;
            }

            @keyframes gradientShift {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            .sidebar-logo {
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1rem;
                color: white;
                font-size: 2rem;
                box-shadow:
                    0 20px 40px rgba(0, 212, 255, 0.4),
                    0 0 30px rgba(181, 55, 242, 0.3);
                animation: logoFloat 3s ease-in-out infinite;
                position: relative;
            }

            .sidebar-logo::before {
                content: '';
                position: absolute;
                top: -5px;
                left: -5px;
                right: -5px;
                bottom: -5px;
                background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
                border-radius: 50%;
                z-index: -1;
                animation: rotate 4s linear infinite;
                opacity: 0.7;
            }

            @keyframes logoFloat {
                0%, 100% { transform: translateY(0px) scale(1); }
                50% { transform: translateY(-5px) scale(1.05); }
            }

            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .sidebar-title {
                font-size: 1.3rem;
                font-weight: 800;
                color: white;
                margin-bottom: 0.5rem;
                text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            }

            .sidebar-subtitle {
                font-size: 1rem;
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 1rem;
            }

            .features-count {
                display: flex;
                justify-content: space-around;
                margin-top: 1rem;
            }

            .feature-badge {
                background: var(--glass-bg);
                backdrop-filter: blur(10px);
                border: 1px solid var(--glass-border);
                border-radius: 15px;
                padding: 0.5rem;
                text-align: center;
                flex: 1;
                margin: 0 0.25rem;
                transition: all 0.3s ease;
            }

            .feature-badge:hover {
                transform: translateY(-3px);
                box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
            }

            .feature-number {
                font-size: 1.2rem;
                font-weight: 700;
                color: var(--neon-blue);
                text-shadow: 0 0 10px var(--neon-blue);
            }

            .feature-label {
                font-size: 0.7rem;
                color: rgba(255, 255, 255, 0.8);
                margin-top: 0.25rem;
            }

            /* عناصر القائمة المتطورة */
            .sidebar-menu {
                padding: 1.5rem 0;
            }

            .menu-section {
                margin-bottom: 1.5rem;
            }

            .menu-section-title {
                padding: 1rem 1.5rem;
                font-size: 1rem;
                font-weight: 700;
                color: white;
                background: var(--glass-bg);
                backdrop-filter: blur(15px);
                border: 1px solid var(--glass-border);
                border-radius: 0 25px 25px 0;
                margin-left: 1rem;
                display: flex;
                align-items: center;
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .menu-section-title::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 4px;
                height: 100%;
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                transform: scaleY(0);
                transition: all 0.3s ease;
            }

            .menu-section-title:hover::before {
                transform: scaleY(1);
            }

            .menu-section-title:hover {
                background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(181, 55, 242, 0.2));
                transform: translateX(-10px);
                box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
            }

            .menu-section-icon {
                margin-left: 1rem;
                font-size: 1.2rem;
                color: var(--neon-blue);
                text-shadow: 0 0 10px var(--neon-blue);
            }

            .menu-section-description {
                font-size: 0.85rem;
                color: rgba(255, 255, 255, 0.7);
                padding: 0.5rem 1.5rem 0.5rem 2rem;
                line-height: 1.4;
                margin-bottom: 0.5rem;
            }

            .menu-items {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .menu-item-link {
                display: flex;
                align-items: center;
                padding: 0.75rem 1.5rem 0.75rem 2.5rem;
                color: rgba(255, 255, 255, 0.8);
                text-decoration: none;
                font-size: 0.9rem;
                font-weight: 500;
                transition: all 0.3s ease;
                border-radius: 0 25px 25px 0;
                margin-left: 1rem;
                position: relative;
                overflow: hidden;
            }

            .menu-item-link::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 3px;
                height: 100%;
                background: var(--neon-green);
                transform: scaleY(0);
                transition: all 0.3s ease;
            }

            .menu-item-link:hover::before {
                transform: scaleY(1);
            }

            .menu-item-link:hover {
                background: linear-gradient(135deg, rgba(57, 255, 20, 0.1), rgba(0, 242, 254, 0.1));
                color: var(--neon-green);
                text-decoration: none;
                transform: translateX(-8px);
                box-shadow: 0 5px 15px rgba(57, 255, 20, 0.3);
            }

            .menu-item-icon {
                margin-left: 0.75rem;
                font-size: 0.9rem;
                width: 16px;
                text-align: center;
            }

            .menu-badge {
                background: linear-gradient(135deg, var(--neon-pink), var(--neon-purple));
                color: white;
                font-size: 0.7rem;
                padding: 0.2rem 0.5rem;
                border-radius: 10px;
                margin-right: auto;
                font-weight: 600;
                animation: badgePulse 2s ease-in-out infinite;
                box-shadow: 0 0 10px rgba(255, 0, 110, 0.5);
            }

            @keyframes badgePulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.1); }
            }

            .menu-divider {
                height: 2px;
                background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
                margin: 1rem 1.5rem;
                border-radius: 1px;
            }

            /* أسفل القائمة الجانبية */
            .sidebar-footer {
                position: sticky;
                bottom: 0;
                padding: 1.5rem;
                background: linear-gradient(180deg, transparent, var(--glass-bg));
                backdrop-filter: blur(15px);
                border-top: 2px solid var(--glass-border);
            }

            .current-user {
                display: flex;
                align-items: center;
                padding: 1rem;
                background: var(--glass-bg);
                backdrop-filter: blur(15px);
                border: 1px solid var(--glass-border);
                border-radius: 20px;
                margin-bottom: 1rem;
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .current-user::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple));
                animation: userGlow 3s ease-in-out infinite;
            }

            @keyframes userGlow {
                0%, 100% { opacity: 0.5; }
                50% { opacity: 1; }
            }

            .current-user:hover {
                background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(181, 55, 242, 0.2));
                transform: translateY(-3px);
                box-shadow: 0 15px 30px rgba(0, 212, 255, 0.3);
            }

            .user-avatar {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: 700;
                margin-left: 1rem;
                font-size: 1.2rem;
                box-shadow: 0 10px 20px rgba(0, 212, 255, 0.4);
                position: relative;
            }

            .user-avatar::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: -3px;
                bottom: -3px;
                background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
                border-radius: 50%;
                z-index: -1;
                animation: avatarRotate 6s linear infinite;
                opacity: 0.7;
            }

            @keyframes avatarRotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .user-info {
                flex: 1;
            }

            .user-name {
                font-weight: 700;
                color: white;
                font-size: 1rem;
                margin-bottom: 0.25rem;
                text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
            }

            .user-role {
                font-size: 0.85rem;
                color: rgba(255, 255, 255, 0.7);
                margin-bottom: 0;
            }

            .user-status {
                width: 12px;
                height: 12px;
                background: var(--neon-green);
                border-radius: 50%;
                margin-right: 0.5rem;
                animation: statusPulse 2s ease-in-out infinite;
                box-shadow: 0 0 10px var(--neon-green);
            }

            @keyframes statusPulse {
                0%, 100% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.2); opacity: 0.8; }
            }

            /* المحتوى الرئيسي */
            .main-content {
                margin-left: var(--sidebar-width);
                min-height: 100vh;
                padding: 2rem;
                transition: all 0.4s ease;
            }

            /* الشريط العلوي المتطور */
            .top-header {
                background: var(--glass-bg);
                backdrop-filter: blur(25px);
                border: 2px solid var(--glass-border);
                border-radius: 30px;
                padding: 2rem;
                margin-bottom: 2rem;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;
                overflow: hidden;
                animation: slideInDown 1s ease-out;
            }

            @keyframes slideInDown {
                from {
                    opacity: 0;
                    transform: translateY(-50px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .top-header::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple), var(--neon-pink), var(--neon-green));
                background-size: 300% 100%;
                animation: headerGradient 4s ease-in-out infinite;
            }

            @keyframes headerGradient {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            .header-title {
                font-size: 2.2rem;
                font-weight: 800;
                color: white;
                margin-bottom: 0.5rem;
                text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
                animation: titleGlow 3s ease-in-out infinite alternate;
            }

            @keyframes titleGlow {
                from { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
                to { text-shadow: 0 0 30px rgba(0, 212, 255, 0.8); }
            }

            .header-subtitle {
                color: rgba(255, 255, 255, 0.8);
                font-size: 1.1rem;
                margin-bottom: 0;
                font-weight: 500;
            }

            .header-actions {
                display: flex;
                gap: 1rem;
                align-items: center;
            }

            .header-btn {
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                color: white;
                border: none;
                border-radius: 20px;
                padding: 0.75rem 1.5rem;
                font-weight: 600;
                text-decoration: none;
                transition: all 0.3s ease;
                box-shadow: 0 15px 30px rgba(0, 212, 255, 0.3);
                display: flex;
                align-items: center;
                gap: 0.5rem;
                position: relative;
                overflow: hidden;
            }

            .header-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                transition: left 0.5s ease;
            }

            .header-btn:hover::before {
                left: 100%;
            }

            .header-btn:hover {
                transform: translateY(-3px);
                box-shadow: 0 20px 40px rgba(0, 212, 255, 0.4);
                color: white;
                text-decoration: none;
            }

            .header-btn.secondary {
                background: linear-gradient(135deg, var(--neon-pink), var(--neon-purple));
                box-shadow: 0 15px 30px rgba(255, 0, 110, 0.3);
            }

            .header-btn.secondary:hover {
                box-shadow: 0 20px 40px rgba(255, 0, 110, 0.4);
            }

            /* إحصائيات متطورة */
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 2rem;
                margin-bottom: 2rem;
            }

            .stat-card {
                background: var(--glass-bg);
                backdrop-filter: blur(25px);
                border: 2px solid var(--glass-border);
                border-radius: 30px;
                padding: 2rem;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                cursor: pointer;
                position: relative;
                overflow: hidden;
                animation: fadeInUp 1s ease-out;
                animation-fill-mode: both;
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(50px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .stat-card:nth-child(1) { animation-delay: 0.1s; }
            .stat-card:nth-child(2) { animation-delay: 0.2s; }
            .stat-card:nth-child(3) { animation-delay: 0.3s; }
            .stat-card:nth-child(4) { animation-delay: 0.4s; }

            .stat-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple));
                transform: scaleX(0);
                transition: all 0.3s ease;
            }

            .stat-card:hover::before {
                transform: scaleX(1);
            }

            .stat-card:hover {
                transform: translateY(-10px) scale(1.02);
                box-shadow: 0 35px 70px rgba(0, 0, 0, 0.3);
            }

            .stat-card.primary::before { background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple)); }
            .stat-card.success::before { background: linear-gradient(90deg, var(--neon-green), var(--neon-blue)); }
            .stat-card.warning::before { background: linear-gradient(90deg, #ffd700, #ff8c00); }
            .stat-card.danger::before { background: linear-gradient(90deg, var(--neon-pink), var(--neon-purple)); }

            .stat-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .stat-details {
                flex: 1;
            }

            .stat-number {
                font-size: 3rem;
                font-weight: 800;
                color: white;
                margin-bottom: 0.5rem;
                text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
                line-height: 1;
                animation: numberGlow 2s ease-in-out infinite alternate;
            }

            @keyframes numberGlow {
                from { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
                to { text-shadow: 0 0 30px rgba(0, 212, 255, 0.8); }
            }

            .stat-label {
                color: rgba(255, 255, 255, 0.9);
                font-weight: 600;
                font-size: 1.1rem;
                margin-bottom: 0.5rem;
            }

            .stat-change {
                font-size: 0.9rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            .stat-change.positive {
                color: var(--neon-green);
                text-shadow: 0 0 10px var(--neon-green);
            }

            .stat-change.negative {
                color: var(--neon-pink);
                text-shadow: 0 0 10px var(--neon-pink);
            }

            .stat-icon {
                width: 80px;
                height: 80px;
                border-radius: 25px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 2.5rem;
                color: white;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                transition: all 0.3s ease;
                position: relative;
            }

            .stat-card:hover .stat-icon {
                transform: rotate(10deg) scale(1.1);
            }

            .stat-card.primary .stat-icon {
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                box-shadow: 0 20px 40px rgba(0, 212, 255, 0.4);
            }
            .stat-card.success .stat-icon {
                background: linear-gradient(135deg, var(--neon-green), var(--neon-blue));
                box-shadow: 0 20px 40px rgba(57, 255, 20, 0.4);
            }
            .stat-card.warning .stat-icon {
                background: linear-gradient(135deg, #ffd700, #ff8c00);
                box-shadow: 0 20px 40px rgba(255, 215, 0, 0.4);
            }
            .stat-card.danger .stat-icon {
                background: linear-gradient(135deg, var(--neon-pink), var(--neon-purple));
                box-shadow: 0 20px 40px rgba(255, 0, 110, 0.4);
            }

            /* زر التبديل للقائمة الجانبية */
            .sidebar-toggle {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1001;
                background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
                color: white;
                border: none;
                border-radius: 50%;
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
                cursor: pointer;
                box-shadow: 0 15px 30px rgba(0, 212, 255, 0.4);
                transition: all 0.3s ease;
                display: none;
            }

            .sidebar-toggle:hover {
                transform: scale(1.1);
                box-shadow: 0 20px 40px rgba(0, 212, 255, 0.5);
            }

            /* استجابة للشاشات الصغيرة */
            @media (max-width: 1400px) {
                .sidebar {
                    transform: translateX(100%);
                }

                .sidebar.active {
                    transform: translateX(0);
                }

                .main-content {
                    margin-left: 0;
                    padding: 1rem;
                }

                .sidebar-toggle {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .stats-grid {
                    grid-template-columns: 1fr;
                    gap: 1rem;
                }

                .top-header {
                    flex-direction: column;
                    gap: 1rem;
                    text-align: center;
                    padding: 1.5rem;
                }

                .header-actions {
                    width: 100%;
                    justify-content: center;
                    flex-wrap: wrap;
                }
            }

            @media (max-width: 768px) {
                .stat-card {
                    padding: 1.5rem;
                }

                .stat-content {
                    flex-direction: column;
                    text-align: center;
                    gap: 1rem;
                }

                .stat-number {
                    font-size: 2.5rem;
                }

                .stat-icon {
                    width: 60px;
                    height: 60px;
                    font-size: 2rem;
                }

                .header-title {
                    font-size: 1.8rem;
                }

                .sidebar-header {
                    padding: 1.5rem 1rem;
                }

                .sidebar-logo {
                    width: 60px;
                    height: 60px;
                    font-size: 1.5rem;
                }
            }
        </style>
    </head>
    <body>
        <!-- زر إظهار/إخفاء القائمة الجانبية -->
        <button class="sidebar-toggle" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>

        <!-- القائمة الجانبية المتطورة -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-rocket"></i>
                </div>
                <h3 class="sidebar-title">نظام إدارة الاشتراكات المتطور</h3>
                <p class="sidebar-subtitle">AdenLink - العراق</p>

                <div class="features-count">
                    <div class="feature-badge">
                        <div class="feature-number">7</div>
                        <div class="feature-label">مزودين</div>
                    </div>
                    <div class="feature-badge">
                        <div class="feature-number">6</div>
                        <div class="feature-label">طرق دفع</div>
                    </div>
                    <div class="feature-badge">
                        <div class="feature-number">5</div>
                        <div class="feature-label">عملات</div>
                    </div>
                </div>
            </div>

            <div class="sidebar-menu">
                <!-- لوحة المعلومات -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-tachometer-alt menu-section-icon"></i>
                        لوحة المعلومات المتطورة
                    </div>
                    <div class="menu-section-description">
                        📊 إحصائيات حقيقية • 📈 رسوم بيانية تفاعلية • 🔔 تنبيهات ذكية
                    </div>
                </div>

                <div class="menu-divider"></div>

                <!-- إدارة الاشتراكات المتطورة -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-server menu-section-icon"></i>
                        إدارة الاشتراكات المتطورة
                    </div>
                    <div class="menu-section-description">
                        ✅ 7 مزودي خدمة • 3 أنواع اشتراكات • معلومات شاملة (IP، API، أولوية)
                    </div>
                    <ul class="menu-items">
                        <li><a href="#" class="menu-item-link"><i class="fas fa-chart-pie menu-item-icon"></i>مخطط الاشتراكات التفاعلي</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-list menu-item-icon"></i>قائمة الاشتراكات الشاملة</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-credit-card menu-item-icon"></i>طرق الدفع (6 طرق)</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-plus menu-item-icon"></i>إضافة اشتراك متطور</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-chart-line menu-item-icon"></i>تحليلات متقدمة</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-file-alt menu-item-icon"></i>تقارير مفصلة</a></li>
                    </ul>
                </div>

                <div class="menu-divider"></div>

                <!-- إدارة الفواتير والمدفوعات -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-file-invoice menu-section-icon"></i>
                        إدارة الفواتير والمدفوعات
                    </div>
                    <div class="menu-section-description">
                        💰 6 طرق دفع • 5 عملات مدعومة • حسابات مالية دقيقة
                    </div>
                    <ul class="menu-items">
                        <li><a href="#" class="menu-item-link"><i class="fas fa-list menu-item-icon"></i>قائمة الفواتير</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-plus menu-item-icon"></i>إنشاء فاتورة تلقائية</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-chart-bar menu-item-icon"></i>تقارير مالية</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-receipt menu-item-icon"></i>كشف حساب العملاء <span class="menu-badge">جديد</span></a></li>
                    </ul>
                </div>

                <div class="menu-divider"></div>

                <!-- مركز التواصل والإشعارات -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-bell menu-section-icon"></i>
                        مركز التواصل والإشعارات
                    </div>
                    <div class="menu-section-description">
                        🔔 4 أنواع إشعارات • تنبيهات انتهاء (30 يوم مقدماً) • ربط بالإجراءات
                    </div>
                    <ul class="menu-items">
                        <li><a href="#" class="menu-item-link"><i class="fas fa-paper-plane menu-item-icon"></i>إرسال إشعارات</a></li>
                        <li><a href="#" class="menu-item-link"><i class="fas fa-bell menu-item-icon"></i>إدارة التنبيهات</a></li>
                    </ul>
                </div>

                <div class="menu-divider"></div>

                <!-- التقارير والإحصائيات -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-chart-area menu-section-icon"></i>
                        التقارير والإحصائيات
                    </div>
                    <div class="menu-section-description">
                        📈 إحصائيات تفاعلية • تقارير شاملة • تحليلات متقدمة
                    </div>
                </div>

                <div class="menu-divider"></div>

                <!-- إدارة المستخدمين -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-users menu-section-icon"></i>
                        إدارة المستخدمين
                    </div>
                    <div class="menu-section-description">
                        👥 نظام صلاحيات متقدم • معلومات شاملة • تسجيل دخول آمن
                    </div>
                </div>

                <div class="menu-divider"></div>

                <!-- الأمان المتقدم -->
                <div class="menu-section">
                    <div class="menu-section-title">
                        <i class="fas fa-shield-alt menu-section-icon"></i>
                        الأمان المتقدم
                    </div>
                    <div class="menu-section-description">
                        🔒 تشفير Werkzeug • جلسات آمنة • حماية CSRF/XSS
                    </div>
                </div>
            </div>

            <div class="sidebar-footer">
                <div class="current-user">
                    <div class="user-avatar">
                        {{ current_user.full_name[0] if current_user.full_name else 'A' }}
                    </div>
                    <div class="user-info">
                        <div class="user-name">{{ current_user.full_name }}</div>
                        <div class="user-role">{{ 'المدير العام' if current_user.role == 'admin' else 'مستخدم عادي' }}</div>
                    </div>
                    <div class="user-status"></div>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- الشريط العلوي المتطور -->
            <div class="top-header">
                <div>
                    <h1 class="header-title">🚀 مرحباً {{ current_user.full_name }}</h1>
                    <p class="header-subtitle">مرحباً بك في النظام المتطور مع جميع الميزات المتقدمة</p>
                </div>
                <div class="header-actions">
                    <a href="#" class="header-btn">
                        <i class="fas fa-plus"></i>
                        اشتراك جديد
                    </a>
                    <a href="#" class="header-btn secondary">
                        <i class="fas fa-file-invoice"></i>
                        فاتورة جديدة
                    </a>
                    <a href="{{ url_for('logout') }}" class="header-btn" style="background: linear-gradient(135deg, var(--neon-pink), #ff4757);">
                        <i class="fas fa-sign-out-alt"></i>
                        خروج
                    </a>
                </div>
            </div>

            <!-- إحصائيات متطورة -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-content">
                        <div class="stat-details">
                            <div class="stat-number">{{ stats.total_customers }}</div>
                            <div class="stat-label">إجمالي العملاء</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                +{{ stats.monthly_growth_rate }}% نمو شهري
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card success">
                    <div class="stat-content">
                        <div class="stat-details">
                            <div class="stat-number">{{ stats.active_subscriptions }}</div>
                            <div class="stat-label">اشتراكات نشطة</div>
                            <div class="stat-change positive">
                                <i class="fas fa-server"></i>
                                {{ stats.total_providers }} مزودين
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-server"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card warning">
                    <div class="stat-content">
                        <div class="stat-details">
                            <div class="stat-number">{{ stats.expiring_soon }}</div>
                            <div class="stat-label">تنتهي قريباً</div>
                            <div class="stat-change negative">
                                <i class="fas fa-exclamation-triangle"></i>
                                {{ stats.expiring_this_week }} هذا الأسبوع
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card danger">
                    <div class="stat-content">
                        <div class="stat-details">
                            <div class="stat-number">${{ "%.0f"|format(stats.monthly_revenue) }}</div>
                            <div class="stat-label">إيرادات الشهر</div>
                            <div class="stat-change positive">
                                <i class="fas fa-dollar-sign"></i>
                                متوسط: ${{ stats.avg_subscription_value }}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('active');
            }

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', function(event) {
                const sidebar = document.getElementById('sidebar');
                const toggleBtn = document.querySelector('.sidebar-toggle');

                if (window.innerWidth <= 1400) {
                    if (!sidebar.contains(event.target) && !toggleBtn.contains(event.target)) {
                        sidebar.classList.remove('active');
                    }
                }
            });

            // تأثيرات تفاعلية متطورة
            document.addEventListener('DOMContentLoaded', function() {
                // تأثير الكتابة للعنوان
                const title = document.querySelector('.header-title');
                const originalText = title.textContent;
                title.textContent = '';

                let i = 0;
                const typeWriter = () => {
                    if (i < originalText.length) {
                        title.textContent += originalText.charAt(i);
                        i++;
                        setTimeout(typeWriter, 50);
                    }
                };

                setTimeout(typeWriter, 1000);

                // تأثيرات البطاقات
                const statCards = document.querySelectorAll('.stat-card');
                statCards.forEach((card, index) => {
                    card.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-15px) scale(1.03)';
                        this.style.zIndex = '10';
                    });

                    card.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateY(0) scale(1)';
                        this.style.zIndex = '1';
                    });

                    // تأثير النقر
                    card.addEventListener('click', function() {
                        this.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            this.style.transform = 'translateY(-15px) scale(1.03)';
                        }, 150);
                    });
                });

                // تأثيرات القائمة الجانبية
                const menuItems = document.querySelectorAll('.menu-item-link');
                menuItems.forEach(item => {
                    item.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateX(-12px) scale(1.02)';
                    });

                    item.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateX(0) scale(1)';
                    });
                });

                // تحديث الوقت الحقيقي
                function updateTime() {
                    const now = new Date();
                    const timeElements = document.querySelectorAll('.real-time');
                    timeElements.forEach(el => {
                        el.textContent = now.toLocaleString('ar-EG');
                    });
                }

                setInterval(updateTime, 1000);
                updateTime();

                // تأثير الجزيئات المتحركة
                function createParticle() {
                    const particle = document.createElement('div');
                    particle.style.position = 'fixed';
                    particle.style.width = '4px';
                    particle.style.height = '4px';
                    particle.style.background = `hsl(${Math.random() * 360}, 70%, 60%)`;
                    particle.style.borderRadius = '50%';
                    particle.style.pointerEvents = 'none';
                    particle.style.zIndex = '-1';
                    particle.style.left = Math.random() * window.innerWidth + 'px';
                    particle.style.top = window.innerHeight + 'px';
                    particle.style.boxShadow = `0 0 10px ${particle.style.background}`;

                    document.body.appendChild(particle);

                    const animation = particle.animate([
                        { transform: 'translateY(0px) scale(1)', opacity: 0 },
                        { transform: 'translateY(-100px) scale(1.2)', opacity: 1 },
                        { transform: `translateY(-${window.innerHeight + 100}px) scale(0.8)`, opacity: 0 }
                    ], {
                        duration: 3000 + Math.random() * 2000,
                        easing: 'ease-out'
                    });

                    animation.onfinish = () => particle.remove();
                }

                // إنشاء جزيئات كل ثانية
                setInterval(createParticle, 1000);
            });
        </script>
    </body>
    </html>
    '''

    # تشغيل النظام المتطور
    if __name__ == '__main__':
        print("🔄 تهيئة قاعدة البيانات المتطورة...")
        init_database()

        print("=" * 100)
        print("🎉 نظام إدارة الاشتراكات المتطور والشامل جاهز للتشغيل!")
        print("=" * 100)
        print("🌟 معلومات الوصول:")
        print("   🔗 الرابط: http://localhost:5090")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: 123456")
        print()
        print("✨ الميزات المتطورة المتاحة:")
        print("   🏢 إدارة الاشتراكات المتطورة:")
        print("      ✅ 7 مزودي خدمة مدمجين (AdenLink، AWS، GCP، Azure، DigitalOcean، Vultr، Linode)")
        print("      ✅ 3 أنواع اشتراكات (شهري، نصف سنوي، سنوي)")
        print("      ✅ معلومات شاملة لكل اشتراك (IP، منفذ، API، أولوية)")
        print("      ✅ صفحة إدارة متطورة مع بطاقات تفاعلية")
        print("      ✅ صفحة إضافة اشتراك شاملة ومنظمة")
        print()
        print("   💰 إدارة الفواتير والمدفوعات:")
        print("      ✅ 6 طرق دفع مختلفة مع رسوم معالجة")
        print("      ✅ فواتير تلقائية مع أرقام فريدة")
        print("      ✅ حسابات مالية دقيقة (ضرائب، خصومات)")
        print("      ✅ 5 عملات مدعومة (USD، EUR، IQD، SAR، AED)")
        print("      ✅ تتبع حالات الدفع المختلفة")
        print()
        print("   🖥️ واجهة المستخدم المتطورة:")
        print("      ✅ لوحة تحكم تفاعلية مع إحصائيات حقيقية")
        print("      ✅ تصميم Glassmorphism مع تأثيرات نيون")
        print("      ✅ تأثيرات حركية متطورة وتفاعلية")
        print("      ✅ تصميم متجاوب مع جميع الأجهزة")
        print("      ✅ خط Cairo العربي الجميل")
        print()
        print("   👥 إدارة المستخدمين:")
        print("      ✅ نظام صلاحيات متقدم (مدير/مستخدم عادي)")
        print("      ✅ معلومات مستخدم شاملة (شركة، هاتف، صورة)")
        print("      ✅ تسجيل دخول آمن مع تشفير")
        print("      ✅ إدارة الجلسات المتطورة")
        print()
        print("   🔔 نظام الإشعارات:")
        print("      ✅ تنبيهات انتهاء الاشتراكات (30 يوم مقدماً)")
        print("      ✅ 4 أنواع إشعارات (معلومات، تحذير، خطأ، نجاح)")
        print("      ✅ تتبع حالة القراءة والتواريخ")
        print("      ✅ ربط بالإجراءات المطلوبة")
        print()
        print("   🔒 الأمان المتقدم:")
        print("      ✅ تشفير كلمات المرور Werkzeug Security")
        print("      ✅ جلسات آمنة Flask-Login")
        print("      ✅ حماية المسارات login_required")
        print("      ✅ حماية من الهجمات CSRF/XSS")
        print()
        print("   📊 الإحصائيات الحالية:")
        print("      🎯 البيانات المدمجة:")
        print("         👥 المستخدمين: 1 (المدير العام)")
        print("         ☁️ مزودي الخدمة: 7 (شامل AdenLink)")
        print("         💳 طرق الدفع: 6 (متنوعة)")
        print("         💰 العملات: 5 (عالمية ومحلية)")
        print()
        print("      📈 الإحصائيات التفاعلية:")
        print("         • إجمالي الاشتراكات (حسب المستخدم)")
        print("         • الاشتراكات النشطة")
        print("         • إجمالي الفواتير")
        print("         • الفواتير المعلقة")
        print("         • إجمالي الإيرادات")
        print("         • الاشتراكات التي تنتهي قريباً")
        print("=" * 100)
        print("🚀 بدء تشغيل الخادم المتطور...")

        try:
            app.run(
                debug=True,
                host='0.0.0.0',
                port=5090,
                threaded=True
            )
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل الخادم: {e}")

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("\n📦 يرجى تثبيت المكتبات المطلوبة:")
    print("pip install flask flask-sqlalchemy flask-login werkzeug")
    print("\n💡 أو استخدم الأمر التالي لتثبيت جميع المتطلبات:")
    print("pip install flask flask-sqlalchemy flask-login werkzeug")
    print("\n🔧 إذا كنت تستخدم Python 3.12+ قد تحتاج:")
    print("pip install --upgrade flask flask-sqlalchemy flask-login werkzeug")

    input("\n⏸️ اضغط Enter للخروج...")
    exit(1)

except Exception as e:
    print(f"❌ خطأ في إعداد النظام المتطور: {e}")
    import traceback
    traceback.print_exc()

    print("\n🔍 نصائح لحل المشاكل:")
    print("1. تأكد من تثبيت جميع المكتبات المطلوبة")
    print("2. تحقق من إصدار Python (يُفضل 3.8+)")
    print("3. تأكد من أن المنفذ 5090 متاح")
    print("4. جرب إعادة تشغيل النظام")

    input("\n⏸️ اضغط Enter للخروج...")
    exit(1)

print("\n⏹️ تم إيقاف النظام المتطور")
print("🙏 شكراً لاستخدام نظام إدارة الاشتراكات المتطور!")
input("اضغط Enter للخروج...")
