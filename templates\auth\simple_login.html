<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Arial', sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 100%;
        }
        
        .login-title {
            text-align: center;
            color: #00f5ff;
            font-size: 2rem;
            margin-bottom: 30px;
            text-shadow: 0 0 20px #00f5ff;
        }
        
        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
            margin-bottom: 15px;
        }
        
        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00f5ff;
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
            color: white;
        }
        
        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 12px;
            width: 100%;
            font-weight: 600;
            margin-top: 20px;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .alert {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff6b6b;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 15px;
        }
        
        .demo-info {
            background: rgba(0, 245, 255, 0.1);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            text-align: center;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1 class="login-title">
            <i class="fas fa-rocket me-2"></i>
            تسجيل الدخول
        </h1>
        
        <!-- عرض الرسائل -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            {{ form.hidden_tag() }}
            
            <div class="mb-3">
                <label class="form-label">اسم المستخدم</label>
                {{ form.username(class="form-control", placeholder="أدخل اسم المستخدم") }}
                {% if form.username.errors %}
                    <div class="alert">
                        {% for error in form.username.errors %}{{ error }}{% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label class="form-label">كلمة المرور</label>
                {{ form.password(class="form-control", placeholder="أدخل كلمة المرور") }}
                {% if form.password.errors %}
                    <div class="alert">
                        {% for error in form.password.errors %}{{ error }}{% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="form-check mb-3">
                {{ form.remember_me(class="form-check-input") }}
                <label class="form-check-label" for="{{ form.remember_me.id }}">
                    تذكرني
                </label>
            </div>
            
            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>
                تسجيل الدخول
            </button>
        </form>
        
        <div class="demo-info">
            <strong>بيانات تجريبية:</strong><br>
            المستخدم: admin<br>
            كلمة المرور: 123456
        </div>
        
        <div class="text-center mt-3">
            <a href="{{ url_for('welcome') }}" style="color: #00f5ff; text-decoration: none;">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
