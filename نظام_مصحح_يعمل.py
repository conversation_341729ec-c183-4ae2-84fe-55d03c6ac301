#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 النظام المصحح الطولي لإدارة الاشتراكات
مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
شركة AdenLink - العراق 🇮🇶
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta

print("🚀 بدء تشغيل النظام المصحح الطولي...")

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'fixed-vertical-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///fixed_vertical_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# نماذج قاعدة البيانات

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user', nullable=False)
    company = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

class CloudProvider(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    website = db.Column(db.String(255))
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    service_type = db.Column(db.String(50))
    price = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    billing_cycle = db.Column(db.String(20), default='monthly')
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    server_ip = db.Column(db.String(45))
    port = db.Column(db.Integer)
    region = db.Column(db.String(50))
    priority = db.Column(db.String(20), default='medium')
    status = db.Column(db.String(20), default='active')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    user = db.relationship('User', backref='subscriptions')
    provider = db.relationship('CloudProvider', backref='subscriptions')

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    subtotal = db.Column(db.Float, nullable=False, default=0.0)
    tax_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='pending')
    payment_method = db.Column(db.String(50))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    user = db.relationship('User', backref='invoices')
    subscription = db.relationship('Subscription', backref='invoices')

# دوال مساعدة
def get_dashboard_stats():
    try:
        if current_user.is_admin():
            total_subscriptions = Subscription.query.count()
            active_subscriptions = Subscription.query.filter_by(status='active').count()
            total_users = User.query.count()
            total_invoices = Invoice.query.count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(status='paid').scalar() or 0
            pending_invoices = Invoice.query.filter_by(status='pending').count()
        else:
            total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
            active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
            total_users = 1
            total_invoices = Invoice.query.filter_by(user_id=current_user.id).count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(user_id=current_user.id, status='paid').scalar() or 0
            pending_invoices = Invoice.query.filter_by(user_id=current_user.id, status='pending').count()

        return {
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'expired_subscriptions': total_subscriptions - active_subscriptions,
            'total_users': total_users,
            'total_invoices': total_invoices,
            'pending_invoices': pending_invoices,
            'total_revenue': total_revenue
        }
    except Exception as e:
        print(f"خطأ في الإحصائيات: {e}")
        return {
            'total_subscriptions': 0,
            'active_subscriptions': 0,
            'expired_subscriptions': 0,
            'total_users': 0,
            'total_invoices': 0,
            'pending_invoices': 0,
            'total_revenue': 0
        }

def generate_invoice_number():
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return f"INV-{timestamp}"

def calculate_subscription_end_date(start_date, billing_cycle):
    if billing_cycle == 'monthly':
        return start_date + timedelta(days=30)
    elif billing_cycle == 'quarterly':
        return start_date + timedelta(days=90)
    elif billing_cycle == 'semi_annual':
        return start_date + timedelta(days=180)
    elif billing_cycle == 'annual':
        return start_date + timedelta(days=365)
    else:
        return start_date + timedelta(days=30)

# قوالب HTML

LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - النظام المصحح الطولي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }
        .logo-section {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo-icon {
            font-size: 4rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .system-title {
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }
        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            padding: 1rem;
        }
        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            color: white;
            box-shadow: none;
        }
        .btn-login {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            width: 100%;
        }
        .demo-credentials {
            background: rgba(0, 212, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: center;
        }
        .success-message {
            background: rgba(0, 255, 136, 0.2);
            border: 2px solid rgba(0, 255, 136, 0.5);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
            font-weight: 600;
            color: #00ff88;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <div class="logo-icon">
                <i class="fas fa-cloud"></i>
            </div>
            <h1 class="system-title">النظام المصحح الطولي</h1>
            <p>مطور بواسطة: المهندس محمد ياسر الجبوري ❤️</p>
            <p>شركة AdenLink - العراق 🇮🇶</p>
        </div>

        <div class="success-message">
            ✅ النظام مصحح ويعمل بنجاح!
        </div>

        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST">
            <div class="mb-3">
                <input type="text" class="form-control" name="username" placeholder="اسم المستخدم" required>
            </div>
            <div class="mb-3">
                <input type="password" class="form-control" name="password" placeholder="كلمة المرور" required>
            </div>
            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
            </button>
        </form>

        <div class="demo-credentials">
            <div><strong>بيانات التجربة:</strong></div>
            <div>اسم المستخدم: <strong>admin</strong></div>
            <div>كلمة المرور: <strong>123456</strong></div>
            <div class="mt-2">
                <small>🔗 http://localhost:5000</small>
            </div>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - النظام المصحح الطولي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-left: 2px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            overflow-y: auto;
        }
        .sidebar-header {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }
        .logo {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .nav-link:hover {
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
            border-left-color: #00d4ff;
        }
        .nav-link i {
            width: 20px;
            margin-left: 1rem;
        }
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        .top-bar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .page-title {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .stats-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }
        .stat-card:hover {
            transform: translateX(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
        }
        .stat-info {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }
        .stat-icon {
            font-size: 3rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            width: 80px;
            text-align: center;
        }
        .stat-details {
            flex: 1;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }
        .stat-title {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }
        .stat-description {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 0.3rem;
        }
        .btn-logout {
            background: linear-gradient(135deg, #ff006e, #b537f2);
            border: none;
            border-radius: 12px;
            padding: 0.8rem 1.5rem;
            color: white;
            text-decoration: none;
        }
        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
        }
        .action-card {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .action-card:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: #00d4ff;
            color: white;
            text-decoration: none;
            transform: translateX(-5px);
        }
        .action-icon {
            font-size: 2rem;
            color: #00d4ff;
            width: 50px;
            text-align: center;
        }
        .action-content {
            flex: 1;
        }
        .action-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.3rem;
        }
        .action-desc {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
        @media (max-width: 768px) {
            .sidebar { transform: translateX(100%); }
            .main-content { margin-right: 0; }
            .top-bar { flex-direction: column; gap: 1rem; text-align: center; }
            .stat-card { flex-direction: column; text-align: center; }
            .stat-info { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo"><i class="fas fa-cloud"></i></div>
            <div>النظام المصحح الطولي</div>
            <div>محمد ياسر الجبوري</div>
        </div>

        <nav>
            <a href="{{ url_for('dashboard') }}" class="nav-link">
                <i class="fas fa-tachometer-alt"></i>لوحة التحكم
            </a>
            <a href="{{ url_for('subscriptions') }}" class="nav-link">
                <i class="fas fa-server"></i>إدارة الاشتراكات
            </a>
            <a href="{{ url_for('invoices') }}" class="nav-link">
                <i class="fas fa-file-invoice"></i>إدارة الفواتير
            </a>
            {% if current_user.is_admin() %}
            <a href="{{ url_for('users') }}" class="nav-link">
                <i class="fas fa-users"></i>إدارة المستخدمين
            </a>
            <a href="{{ url_for('providers') }}" class="nav-link">
                <i class="fas fa-cloud"></i>مزودي الخدمة
            </a>
            {% endif %}
        </nav>
    </div>

    <div class="main-content">
        <div class="top-bar">
            <div>
                <h1 class="page-title">لوحة التحكم الطولية</h1>
                <p class="mb-0">مرحباً بك في النظام المصحح الطولي</p>
            </div>
            <div>
                <span>{{ current_user.full_name }}</span>
                <a href="{{ url_for('logout') }}" class="btn-logout ms-2">
                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                </a>
            </div>
        </div>

        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.total_subscriptions }}</div>
                        <div class="stat-title">إجمالي الاشتراكات</div>
                        <div class="stat-description">جميع الاشتراكات المسجلة في النظام</div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.active_subscriptions }}</div>
                        <div class="stat-title">الاشتراكات النشطة</div>
                        <div class="stat-description">الاشتراكات التي تعمل حالياً</div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.total_invoices }}</div>
                        <div class="stat-title">إجمالي الفواتير</div>
                        <div class="stat-description">جميع الفواتير المُصدرة</div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-number">${{ "%.0f"|format(stats.total_revenue) }}</div>
                        <div class="stat-title">إجمالي الإيرادات</div>
                        <div class="stat-description">المبالغ المحصلة من الفواتير المدفوعة</div>
                    </div>
                </div>
            </div>

            {% if current_user.is_admin() %}
            <div class="stat-card">
                <div class="stat-info">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-number">{{ stats.total_users }}</div>
                        <div class="stat-title">إجمالي المستخدمين</div>
                        <div class="stat-description">جميع المستخدمين المسجلين</div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <div class="quick-actions">
            <h3 style="color: #00d4ff; margin-bottom: 1rem;">
                <i class="fas fa-bolt me-2"></i>الإجراءات السريعة
            </h3>

            <a href="{{ url_for('subscriptions') }}" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-server"></i>
                </div>
                <div class="action-content">
                    <div class="action-title">إدارة الاشتراكات</div>
                    <div class="action-desc">عرض وإدارة جميع الاشتراكات السحابية</div>
                </div>
                <div class="action-icon">
                    <i class="fas fa-arrow-left"></i>
                </div>
            </a>

            <a href="{{ url_for('invoices') }}" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="action-content">
                    <div class="action-title">إدارة الفواتير</div>
                    <div class="action-desc">إنشاء وإدارة الفواتير والمدفوعات</div>
                </div>
                <div class="action-icon">
                    <i class="fas fa-arrow-left"></i>
                </div>
            </a>

            {% if current_user.is_admin() %}
            <a href="{{ url_for('users') }}" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="action-content">
                    <div class="action-title">إدارة المستخدمين</div>
                    <div class="action-desc">إضافة وإدارة مستخدمي النظام</div>
                </div>
                <div class="action-icon">
                    <i class="fas fa-arrow-left"></i>
                </div>
            </a>
            {% endif %}
        </div>
    </div>
</body>
</html>
'''

# المسارات الأساسية

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            login_user(user)
            flash('🎉 مرحباً بك في النظام المصحح الطولي!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('✅ تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    stats = get_dashboard_stats()
    return render_template_string(DASHBOARD_TEMPLATE, stats=stats)

# مسارات بسيطة للصفحات الأخرى

@app.route('/subscriptions')
@login_required
def subscriptions():
    try:
        if current_user.is_admin():
            subscriptions = Subscription.query.order_by(Subscription.created_at.desc()).all()
        else:
            subscriptions = Subscription.query.filter_by(user_id=current_user.id).order_by(Subscription.created_at.desc()).all()

        return f'''
        <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
            <div style="text-align: center; margin-bottom: 2rem;">
                <h1 style="color: #00d4ff; font-size: 2.5rem; margin-bottom: 1rem;">📋 إدارة الاشتراكات الطولية</h1>
                <div style="background: rgba(0, 255, 136, 0.2); border: 2px solid rgba(0, 255, 136, 0.5); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #00ff88; font-weight: 700;">
                    ✅ النظام مصحح ويعمل بنجاح
                </div>
                <br>
                <a href="{url_for('dashboard')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
            </div>
            <div style="display: flex; flex-direction: column; gap: 1.5rem;">
                {"".join([f'''
                <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 2rem; display: flex; align-items: center; gap: 2rem; transition: all 0.3s ease;" onmouseover="this.style.transform='translateX(-5px)'" onmouseout="this.style.transform='translateX(0)'">
                    <div style="font-size: 3rem; color: #00d4ff; width: 80px; text-align: center;">
                        <i class="fas fa-server"></i>
                    </div>
                    <div style="flex: 1;">
                        <h5 style="color: #00d4ff; font-size: 1.5rem; margin-bottom: 0.5rem;">{sub.name}</h5>
                        <p style="color: rgba(255, 255, 255, 0.7); margin-bottom: 1rem;"><strong>المزود:</strong> {sub.provider.name if sub.provider else "غير محدد"}</p>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">السعر:</span><br><strong>{sub.price} {sub.currency}</strong></div>
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">النوع:</span><br><strong>{sub.service_type or "غير محدد"}</strong></div>
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">تاريخ الانتهاء:</span><br><strong>{sub.end_date}</strong></div>
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">الأولوية:</span><br><strong>{sub.priority}</strong></div>
                        </div>
                    </div>
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 1rem;">
                        <div style="background: {"#28a745" if sub.status == "active" else "#dc3545"}; padding: 0.5rem 1rem; border-radius: 15px; font-size: 0.9rem; font-weight: 600; text-align: center;">
                            {"نشط" if sub.status == "active" else "منتهي" if sub.status == "expired" else "موقوف"}
                        </div>
                    </div>
                </div>
                ''' for sub in subscriptions])}
            </div>
            {f'<div style="text-align: center; margin-top: 3rem;"><h3>لا توجد اشتراكات حالياً</h3><p>ابدأ بإضافة أول اشتراك لك</p></div>' if not subscriptions else ''}
        </div>
        '''
    except Exception as e:
        return f'<div style="padding: 2rem; color: white; background: #dc3545;">خطأ: {str(e)}</div>'

@app.route('/invoices')
@login_required
def invoices():
    try:
        if current_user.is_admin():
            invoices = Invoice.query.order_by(Invoice.created_at.desc()).all()
        else:
            invoices = Invoice.query.filter_by(user_id=current_user.id).order_by(Invoice.created_at.desc()).all()

        return f'''
        <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
            <div style="text-align: center; margin-bottom: 2rem;">
                <h1 style="color: #00d4ff; font-size: 2.5rem; margin-bottom: 1rem;">🧾 إدارة الفواتير الطولية</h1>
                <div style="background: rgba(0, 255, 136, 0.2); border: 2px solid rgba(0, 255, 136, 0.5); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #00ff88; font-weight: 700;">
                    ✅ النظام مصحح ويعمل بنجاح
                </div>
                <br>
                <a href="{url_for('dashboard')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
            </div>
            <div style="display: flex; flex-direction: column; gap: 1.5rem;">
                {"".join([f'''
                <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 2rem; display: flex; align-items: center; gap: 2rem; transition: all 0.3s ease;" onmouseover="this.style.transform='translateX(-5px)'" onmouseout="this.style.transform='translateX(0)'">
                    <div style="font-size: 3rem; color: #00d4ff; width: 80px; text-align: center;">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div style="flex: 1;">
                        <h5 style="color: #00d4ff; font-size: 1.5rem; margin-bottom: 0.5rem;">فاتورة رقم: {inv.invoice_number}</h5>
                        <p style="color: rgba(255, 255, 255, 0.7); margin-bottom: 1rem;"><strong>الاشتراك:</strong> {inv.subscription.name}</p>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">المبلغ:</span><br><strong>{inv.total_amount} {inv.currency}</strong></div>
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">تاريخ الإصدار:</span><br><strong>{inv.issue_date}</strong></div>
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">تاريخ الاستحقاق:</span><br><strong>{inv.due_date}</strong></div>
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">طريقة الدفع:</span><br><strong>{inv.payment_method or "غير محدد"}</strong></div>
                        </div>
                    </div>
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 1rem;">
                        <div style="background: {"#28a745" if inv.status == "paid" else "#ffc107" if inv.status == "pending" else "#dc3545"}; padding: 0.5rem 1rem; border-radius: 15px; font-size: 0.9rem; font-weight: 600; text-align: center; color: {"white" if inv.status != "pending" else "black"};">
                            {"مدفوع" if inv.status == "paid" else "معلق" if inv.status == "pending" else "ملغي"}
                        </div>
                    </div>
                </div>
                ''' for inv in invoices])}
            </div>
            {f'<div style="text-align: center; margin-top: 3rem;"><h3>لا توجد فواتير حالياً</h3><p>ستظهر الفواتير هنا عند إنشائها</p></div>' if not invoices else ''}
        </div>
        '''
    except Exception as e:
        return f'<div style="padding: 2rem; color: white; background: #dc3545;">خطأ: {str(e)}</div>'

@app.route('/users')
@login_required
def users():
    if not current_user.is_admin():
        return redirect(url_for('dashboard'))

    try:
        users = User.query.order_by(User.created_at.desc()).all()

        return f'''
        <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
            <div style="text-align: center; margin-bottom: 2rem;">
                <h1 style="color: #00d4ff; font-size: 2.5rem; margin-bottom: 1rem;">👥 إدارة المستخدمين الطولية</h1>
                <div style="background: rgba(0, 255, 136, 0.2); border: 2px solid rgba(0, 255, 136, 0.5); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #00ff88; font-weight: 700;">
                    ✅ النظام مصحح ويعمل بنجاح
                </div>
                <br>
                <a href="{url_for('dashboard')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
            </div>
            <div style="display: flex; flex-direction: column; gap: 1.5rem;">
                {"".join([f'''
                <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 2rem; display: flex; align-items: center; gap: 2rem; transition: all 0.3s ease;" onmouseover="this.style.transform='translateX(-5px)'" onmouseout="this.style.transform='translateX(0)'">
                    <div style="font-size: 3rem; color: #00d4ff; width: 80px; text-align: center;">
                        <i class="fas fa-user"></i>
                    </div>
                    <div style="flex: 1;">
                        <h5 style="color: #00d4ff; font-size: 1.5rem; margin-bottom: 0.5rem;">{user.full_name}</h5>
                        <p style="color: rgba(255, 255, 255, 0.7); margin-bottom: 1rem;"><strong>اسم المستخدم:</strong> {user.username}</p>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">البريد:</span><br><strong>{user.email}</strong></div>
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">الشركة:</span><br><strong>{user.company or "غير محدد"}</strong></div>
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">الهاتف:</span><br><strong>{user.phone or "غير محدد"}</strong></div>
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">تاريخ التسجيل:</span><br><strong>{user.created_at.strftime('%Y-%m-%d') if user.created_at else 'غير محدد'}</strong></div>
                        </div>
                    </div>
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 1rem;">
                        <div style="background: {"#ff006e" if user.role == "admin" else "#28a745"}; padding: 0.5rem 1rem; border-radius: 15px; font-size: 0.9rem; font-weight: 600; text-align: center;">
                            {"مدير" if user.role == "admin" else "مستخدم"}
                        </div>
                        <div style="background: {"#28a745" if user.is_active else "#dc3545"}; padding: 0.5rem 1rem; border-radius: 15px; font-size: 0.9rem; font-weight: 600; text-align: center;">
                            {"نشط" if user.is_active else "غير نشط"}
                        </div>
                    </div>
                </div>
                ''' for user in users])}
            </div>
        </div>
        '''
    except Exception as e:
        return f'<div style="padding: 2rem; color: white; background: #dc3545;">خطأ: {str(e)}</div>'

@app.route('/providers')
@login_required
def providers():
    if not current_user.is_admin():
        return redirect(url_for('dashboard'))

    try:
        providers = CloudProvider.query.all()

        return f'''
        <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
            <div style="text-align: center; margin-bottom: 2rem;">
                <h1 style="color: #00d4ff; font-size: 2.5rem; margin-bottom: 1rem;">☁️ مزودي الخدمة الطولية</h1>
                <div style="background: rgba(0, 255, 136, 0.2); border: 2px solid rgba(0, 255, 136, 0.5); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #00ff88; font-weight: 700;">
                    ✅ النظام مصحح ويعمل بنجاح
                </div>
                <br>
                <a href="{url_for('dashboard')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
            </div>
            <div style="display: flex; flex-direction: column; gap: 1.5rem;">
                {"".join([f'''
                <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 2rem; display: flex; align-items: center; gap: 2rem; transition: all 0.3s ease;" onmouseover="this.style.transform='translateX(-5px)'" onmouseout="this.style.transform='translateX(0)'">
                    <div style="font-size: 3rem; color: #00d4ff; width: 80px; text-align: center;">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <div style="flex: 1;">
                        <h5 style="color: #00d4ff; font-size: 1.5rem; margin-bottom: 0.5rem;">{provider.name}</h5>
                        <p style="color: rgba(255, 255, 255, 0.7); margin-bottom: 1rem;"><strong>الرمز:</strong> {provider.slug}</p>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">الموقع:</span><br><strong><a href="{provider.website}" target="_blank" style="color: #00d4ff;">{provider.website}</a></strong></div>
                            <div><span style="color: rgba(255, 255, 255, 0.6); font-size: 0.8rem;">الوصف:</span><br><strong>{provider.description or "لا يوجد وصف"}</strong></div>
                        </div>
                    </div>
                    <div style="display: flex; flex-direction: column; align-items: center; gap: 1rem;">
                        <div style="background: {"#28a745" if provider.is_active else "#dc3545"}; padding: 0.5rem 1rem; border-radius: 15px; font-size: 0.9rem; font-weight: 600; text-align: center;">
                            {"نشط" if provider.is_active else "غير نشط"}
                        </div>
                    </div>
                </div>
                ''' for provider in providers])}
            </div>
        </div>
        '''
    except Exception as e:
        return f'<div style="padding: 2rem; color: white; background: #dc3545;">خطأ: {str(e)}</div>'

# تهيئة قاعدة البيانات
def init_database():
    with app.app_context():
        try:
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات")

            if User.query.count() > 0:
                print("✅ البيانات موجودة مسبقاً")
                return

            # إنشاء المستخدمين
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام - محمد ياسر الجبوري',
                role='admin',
                company='AdenLink - العراق',
                phone='+***********-789',
                is_active=True
            )
            admin.set_password('123456')
            db.session.add(admin)

            user1 = User(
                username='user1',
                email='<EMAIL>',
                full_name='أحمد محمد علي',
                role='user',
                company='شركة التقنية المتقدمة',
                phone='+***********-333',
                is_active=True
            )
            user1.set_password('123456')
            db.session.add(user1)

            user2 = User(
                username='user2',
                email='<EMAIL>',
                full_name='فاطمة أحمد حسن',
                role='user',
                company='مؤسسة الحلول الذكية',
                phone='+***********-666',
                is_active=True
            )
            user2.set_password('123456')
            db.session.add(user2)

            db.session.flush()
            print("✅ تم إنشاء المستخدمين")

            # إنشاء مزودي الخدمة
            providers_data = [
                ('Amazon Web Services', 'aws', 'https://aws.amazon.com', 'خدمات الحوسبة السحابية الرائدة من أمازون'),
                ('Microsoft Azure', 'azure', 'https://azure.microsoft.com', 'منصة الحوسبة السحابية الشاملة من مايكروسوفت'),
                ('Google Cloud Platform', 'gcp', 'https://cloud.google.com', 'خدمات الحوسبة السحابية المتطورة من جوجل'),
                ('DigitalOcean', 'digitalocean', 'https://digitalocean.com', 'خدمات الخوادم الافتراضية البسيطة والقوية'),
                ('Vultr', 'vultr', 'https://vultr.com', 'خدمات الخوادم السحابية عالية الأداء')
            ]

            for name, slug, website, description in providers_data:
                provider = CloudProvider(
                    name=name,
                    slug=slug,
                    website=website,
                    description=description,
                    is_active=True
                )
                db.session.add(provider)

            db.session.flush()
            print("✅ تم إنشاء مزودي الخدمة")

            # إنشاء الاشتراكات التجريبية
            subscriptions_data = [
                (2, 1, 'خادم ويب أساسي AWS', 'خادم ويب للموقع الشخصي مع قاعدة بيانات', 'VPS', 25.99, 'USD', 'monthly', '************', 22, 'us-east-1', 'medium', 'active'),
                (2, 2, 'قاعدة بيانات Azure متقدمة', 'قاعدة بيانات SQL للتطبيق الرئيسي', 'Database', 89.99, 'USD', 'monthly', '********', 1433, 'east-us', 'high', 'active'),
                (3, 3, 'تخزين سحابي Google', 'مساحة تخزين للملفات والنسخ الاحتياطية', 'Cloud Storage', 15.50, 'USD', 'monthly', None, None, 'us-central1', 'low', 'active'),
                (3, 4, 'خادم تطوير DigitalOcean', 'خادم للتطوير والاختبار', 'VPS', 12.00, 'USD', 'monthly', '*************', 22, 'nyc3', 'medium', 'expired'),
                (1, 5, 'خادم الإنتاج Vultr', 'خادم الإنتاج الرئيسي عالي الأداء', 'Dedicated', 160.00, 'USD', 'monthly', '************', 22, 'ewr', 'critical', 'active')
            ]

            for user_id, provider_id, name, description, service_type, price, currency, billing_cycle, server_ip, port, region, priority, status in subscriptions_data:
                start_date = date.today() - timedelta(days=30)
                end_date = start_date + timedelta(days=30) if status == 'active' else start_date - timedelta(days=5)

                subscription = Subscription(
                    user_id=user_id,
                    provider_id=provider_id,
                    name=name,
                    description=description,
                    service_type=service_type,
                    price=price,
                    currency=currency,
                    billing_cycle=billing_cycle,
                    start_date=start_date,
                    end_date=end_date,
                    server_ip=server_ip,
                    port=port,
                    region=region,
                    priority=priority,
                    status=status
                )
                db.session.add(subscription)

            db.session.flush()
            print("✅ تم إنشاء الاشتراكات التجريبية")

            # إنشاء الفواتير التجريبية
            invoices_data = [
                (2, 1, 25.99, 2.60, 28.59, 'USD', 'paid', 'credit_card'),
                (2, 2, 89.99, 9.00, 98.99, 'USD', 'pending', 'paypal'),
                (3, 3, 15.50, 1.55, 17.05, 'USD', 'pending', 'bank_transfer'),
                (1, 5, 160.00, 16.00, 176.00, 'USD', 'paid', 'credit_card')
            ]

            for user_id, subscription_id, subtotal, tax_amount, total_amount, currency, status, payment_method in invoices_data:
                invoice = Invoice(
                    invoice_number=generate_invoice_number(),
                    user_id=user_id,
                    subscription_id=subscription_id,
                    subtotal=subtotal,
                    tax_amount=tax_amount,
                    total_amount=total_amount,
                    currency=currency,
                    issue_date=date.today() - timedelta(days=15),
                    due_date=date.today() + timedelta(days=15),
                    paid_date=date.today() - timedelta(days=5) if status == 'paid' else None,
                    status=status,
                    payment_method=payment_method
                )
                db.session.add(invoice)

            db.session.flush()
            print("✅ تم إنشاء الفواتير التجريبية")

            db.session.commit()
            print("✅ تم حفظ جميع البيانات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            db.session.rollback()

# تشغيل النظام المصحح
if __name__ == '__main__':
    print("🚀 بدء تشغيل النظام المصحح الطولي...")
    print("=" * 70)
    print("🎯 النظام المصحح الطولي لإدارة الاشتراكات السحابية")
    print("💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️")
    print("🏢 شركة AdenLink - العراق 🇮🇶")
    print("🎨 التصميم: طولي مع قائمة جانبية")
    print("=" * 70)

    try:
        print("📊 تهيئة قاعدة البيانات...")
        init_database()

        print("\n✅ تم تهيئة النظام المصحح بنجاح!")
        print("=" * 70)
        print("🌐 معلومات الوصول:")
        print("🔗 الرابط: http://localhost:5000")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: 123456")
        print("=" * 70)
        print("🎮 الميزات المتاحة:")
        print("📊 • لوحة تحكم طولية مع إحصائيات مفصلة")
        print("📋 • إدارة الاشتراكات بتخطيط طولي")
        print("🧾 • إدارة الفواتير بتخطيط طولي")
        print("👥 • إدارة المستخدمين (للمديرين)")
        print("☁️ • إدارة مزودي الخدمة (للمديرين)")
        print("🎨 • تصميم طولي متجاوب ومتطور")
        print("🔐 • نظام تسجيل دخول آمن")
        print("⚡ • أداء سريع ومحسن")
        print("=" * 70)
        print("🎯 مميزات التصميم الطولي:")
        print("✅ • ترتيب عمودي للعناصر الرئيسية")
        print("✅ • بطاقات أفقية طويلة للمحتوى")
        print("✅ • قائمة جانبية ثابتة للتنقل")
        print("✅ • استغلال أفضل للمساحة العمودية")
        print("✅ • سهولة القراءة والتصفح")
        print("✅ • تنظيم أفضل للمعلومات")
        print("=" * 70)
        print("👥 المستخدمين التجريبيين:")
        print("🔹 admin / 123456 (مدير)")
        print("🔹 user1 / 123456 (مستخدم)")
        print("🔹 user2 / 123456 (مستخدم)")
        print("=" * 70)
        print("🚀 النظام المصحح الطولي جاهز للاستخدام!")
        print("=" * 70)

        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True
        )

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("🔧 يرجى التحقق من:")
        print("   • تثبيت جميع المكتبات المطلوبة")
        print("   • عدم استخدام البورت 5000 من تطبيق آخر")
        print("   • صلاحيات الكتابة في مجلد التطبيق")

print("🎉 تم إعداد النظام المصحح الطولي بالكامل!")
