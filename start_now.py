#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🚀 تشغيل سريع لنظام إدارة الاشتراكات")

try:
    import os
    import sys
    
    # تحديد المجلد الحالي
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print(f"📂 المجلد: {os.getcwd()}")
    
    # استيراد المكتبات المطلوبة
    from flask import Flask
    
    print("✅ Flask متاح")
    
    # تشغيل النظام
    exec(open('advanced_subscription_system.py', 'r', encoding='utf-8').read())
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
    input("اضغط Enter...")
