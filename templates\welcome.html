{% extends "base.html" %}

{% block title %}مرحباً - نظام إدارة الاشتراكات{% endblock %}

{% block body_class %}welcome-page{% endblock %}

{% block extra_css %}
<style>
.welcome-page {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.welcome-container {
    text-align: center;
    max-width: 800px;
    padding: 40px;
}

.welcome-title {
    font-size: 4rem;
    font-weight: 700;
    background: linear-gradient(135deg, #00f5ff, #bf00ff, #ff0080);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    animation: titleGlow 3s ease-in-out infinite;
}

@keyframes titleGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
}

.welcome-subtitle {
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 40px;
}

.welcome-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 50px;
    line-height: 1.8;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin: 50px 0;
}

.feature-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 30px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #00f5ff, #bf00ff);
}

.feature-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
}

.feature-icon {
    font-size: 3rem;
    color: #00f5ff;
    margin-bottom: 20px;
    text-shadow: 0 0 20px #00f5ff;
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: white;
    margin-bottom: 15px;
}

.feature-description {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
}

.cta-buttons {
    margin-top: 50px;
}

.cta-btn {
    display: inline-block;
    padding: 15px 40px;
    margin: 10px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid transparent;
}

.cta-btn-secondary {
    background: transparent;
    color: #00f5ff;
    border: 2px solid #00f5ff;
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.cta-btn-primary:hover {
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
    color: white;
}

.cta-btn-secondary:hover {
    background: #00f5ff;
    color: #0a0a0a;
    box-shadow: 0 15px 30px rgba(0, 245, 255, 0.4);
}

.developer-info {
    margin-top: 60px;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.developer-name {
    color: #bf00ff;
    font-weight: 600;
    text-shadow: 0 0 10px #bf00ff;
}

@media (max-width: 768px) {
    .welcome-title {
        font-size: 2.5rem;
    }
    
    .welcome-subtitle {
        font-size: 1.2rem;
    }
    
    .welcome-description {
        font-size: 1rem;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .cta-btn {
        display: block;
        margin: 10px 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="welcome-container">
    <h1 class="welcome-title">
        <i class="fas fa-rocket me-3"></i>
        نظام إدارة الاشتراكات
    </h1>
    
    <p class="welcome-subtitle">
        الحل المتطور لإدارة اشتراكاتك وفواتيرك بكفاءة عالية
    </p>
    
    <p class="welcome-description">
        نظام شامل ومتطور لإدارة جميع اشتراكاتك الرقمية والخدمات المختلفة، 
        مع واجهة عصرية وميزات متقدمة لتتبع الفواتير والمدفوعات وإرسال التذكيرات التلقائية.
    </p>
    
    <div class="feature-grid">
        <div class="feature-card hologram-card">
            <div class="feature-icon">
                <i class="fas fa-subscription"></i>
            </div>
            <h3 class="feature-title">إدارة الاشتراكات</h3>
            <p class="feature-description">
                تتبع جميع اشتراكاتك مع تفاصيل السيرفرات والتواريخ والأسعار
            </p>
        </div>
        
        <div class="feature-card hologram-card">
            <div class="feature-icon">
                <i class="fas fa-file-invoice-dollar"></i>
            </div>
            <h3 class="feature-title">نظام الفواتير</h3>
            <p class="feature-description">
                إنشاء وإدارة الفواتير مع تتبع المدفوعات وتصدير PDF
            </p>
        </div>
        
        <div class="feature-card hologram-card">
            <div class="feature-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <h3 class="feature-title">التحليلات المتقدمة</h3>
            <p class="feature-description">
                رسوم بيانية تفاعلية وإحصائيات مفصلة لاتخاذ قرارات ذكية
            </p>
        </div>
        
        <div class="feature-card hologram-card">
            <div class="feature-icon">
                <i class="fas fa-envelope"></i>
            </div>
            <h3 class="feature-title">مركز التواصل</h3>
            <p class="feature-description">
                إرسال رسائل تلقائية للعملاء مع قوالب جاهزة ومتغيرات ديناميكية
            </p>
        </div>
        
        <div class="feature-card hologram-card">
            <div class="feature-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h3 class="feature-title">الأمان المتقدم</h3>
            <p class="feature-description">
                حماية متعددة الطبقات مع تشفير البيانات وسجل الأنشطة
            </p>
        </div>
        
        <div class="feature-card hologram-card">
            <div class="feature-icon">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3 class="feature-title">تصميم متجاوب</h3>
            <p class="feature-description">
                واجهة عصرية تعمل بسلاسة على جميع الأجهزة والشاشات
            </p>
        </div>
    </div>
    
    <div class="cta-buttons">
        <a href="{{ url_for('login') }}" class="cta-btn cta-btn-primary crystal-btn ripple">
            <i class="fas fa-sign-in-alt me-2"></i>
            تسجيل الدخول
        </a>
        <a href="#features" class="cta-btn cta-btn-secondary crystal-btn ripple">
            <i class="fas fa-info-circle me-2"></i>
            المزيد من المعلومات
        </a>
    </div>
    
    <div class="developer-info">
        <p class="mb-2">
            <i class="fas fa-code text-primary me-2"></i>
            مطور بواسطة: <span class="developer-name">المهندس محمد ياسر الجبوري</span>
        </p>
        <p class="mb-0">
            <i class="fas fa-heart text-danger me-2"></i>
            صُنع بحب وإتقان لخدمة المجتمع التقني العربي
        </p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الكتابة التدريجية للعنوان
    const title = document.querySelector('.welcome-title');
    const text = title.textContent;
    title.textContent = '';
    
    let i = 0;
    const typeWriter = () => {
        if (i < text.length) {
            title.textContent += text.charAt(i);
            i++;
            setTimeout(typeWriter, 100);
        }
    };
    
    setTimeout(typeWriter, 500);
    
    // تأثير الظهور التدريجي للبطاقات
    const cards = document.querySelectorAll('.feature-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 1000 + (index * 200));
    });
});
</script>
{% endblock %}
