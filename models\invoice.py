#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج الفاتورة
"""

from datetime import datetime, timedelta
from flask_sqlalchemy import SQLAlchemy

# استيراد db من app.py سيتم لاحقاً
db = None

class Invoice(db.Model):
    """نموذج الفاتورة"""
    
    __tablename__ = 'invoices'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # معلومات أساسية
    invoice_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    
    # المبالغ
    amount = db.Column(db.Float, nullable=False)
    tax_amount = db.Column(db.Float, default=0)
    discount_amount = db.Column(db.Float, default=0)
    total_amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')
    
    # التواريخ
    issue_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date)
    due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.Date)
    
    # الحالة
    status = db.Column(db.String(20), default='pending', nullable=False)  # pending, paid, overdue, cancelled
    payment_method = db.Column(db.String(50))  # cash, bank_transfer, credit_card, paypal, etc.
    
    # معلومات إضافية
    notes = db.Column(db.Text)
    reference_number = db.Column(db.String(100))
    
    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'))
    
    def __init__(self, **kwargs):
        super(Invoice, self).__init__(**kwargs)
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
        if not self.total_amount:
            self.calculate_total()
    
    def generate_invoice_number(self):
        """توليد رقم فاتورة فريد"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        return f'INV-{timestamp}'
    
    def calculate_total(self):
        """حساب المجموع الكلي"""
        self.total_amount = self.amount + self.tax_amount - self.discount_amount
    
    def is_paid(self):
        """التحقق من حالة الدفع"""
        return self.status == 'paid'
    
    def is_overdue(self):
        """التحقق من التأخير في الدفع"""
        return self.due_date < datetime.now().date() and self.status == 'pending'
    
    def days_overdue(self):
        """عدد أيام التأخير"""
        if self.is_overdue():
            return (datetime.now().date() - self.due_date).days
        return 0
    
    def days_until_due(self):
        """عدد الأيام المتبقية للاستحقاق"""
        return (self.due_date - datetime.now().date()).days
    
    def mark_as_paid(self, payment_method=None, paid_date=None):
        """تحديد الفاتورة كمدفوعة"""
        self.status = 'paid'
        self.paid_date = paid_date or datetime.now().date()
        if payment_method:
            self.payment_method = payment_method
        self.updated_at = datetime.utcnow()
    
    def mark_as_overdue(self):
        """تحديد الفاتورة كمتأخرة"""
        if self.due_date < datetime.now().date() and self.status == 'pending':
            self.status = 'overdue'
            self.updated_at = datetime.utcnow()
    
    def cancel_invoice(self):
        """إلغاء الفاتورة"""
        self.status = 'cancelled'
        self.updated_at = datetime.utcnow()
    
    def get_tax_percentage(self):
        """حساب نسبة الضريبة"""
        if self.amount > 0:
            return (self.tax_amount / self.amount) * 100
        return 0
    
    def get_discount_percentage(self):
        """حساب نسبة الخصم"""
        if self.amount > 0:
            return (self.discount_amount / self.amount) * 100
        return 0
    
    def apply_tax(self, tax_percentage):
        """تطبيق الضريبة"""
        self.tax_amount = (self.amount * tax_percentage) / 100
        self.calculate_total()
    
    def apply_discount(self, discount_percentage):
        """تطبيق الخصم"""
        self.discount_amount = (self.amount * discount_percentage) / 100
        self.calculate_total()
    
    def get_status_color(self):
        """الحصول على لون الحالة"""
        colors = {
            'pending': 'warning',
            'paid': 'success',
            'overdue': 'danger',
            'cancelled': 'secondary'
        }
        return colors.get(self.status, 'primary')
    
    def get_status_text(self):
        """الحصول على نص الحالة بالعربية"""
        status_text = {
            'pending': 'معلق',
            'paid': 'مدفوع',
            'overdue': 'متأخر',
            'cancelled': 'ملغي'
        }
        return status_text.get(self.status, 'غير محدد')
    
    def to_dict(self):
        """تحويل البيانات إلى قاموس"""
        return {
            'id': self.id,
            'invoice_number': self.invoice_number,
            'title': self.title,
            'description': self.description,
            'amount': self.amount,
            'tax_amount': self.tax_amount,
            'discount_amount': self.discount_amount,
            'total_amount': self.total_amount,
            'currency': self.currency,
            'issue_date': self.issue_date.isoformat() if self.issue_date else None,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'paid_date': self.paid_date.isoformat() if self.paid_date else None,
            'status': self.status,
            'status_text': self.get_status_text(),
            'status_color': self.get_status_color(),
            'payment_method': self.payment_method,
            'notes': self.notes,
            'reference_number': self.reference_number,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_paid': self.is_paid(),
            'is_overdue': self.is_overdue(),
            'days_overdue': self.days_overdue(),
            'days_until_due': self.days_until_due(),
            'tax_percentage': self.get_tax_percentage(),
            'discount_percentage': self.get_discount_percentage()
        }
    
    def __repr__(self):
        return f'<Invoice {self.invoice_number} - {self.total_amount} {self.currency}>'
