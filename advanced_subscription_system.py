#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات المتقدم
مطور بواسطة: المهندس محمد ياسر الجبوري
شركة AdenLink - العراق
"""

from flask import Flask, render_template, render_template_string, request, redirect, url_for, flash, jsonify, session, send_file, abort
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, date, timedelta
import secrets
import os
import uuid
import json
import smtplib
import io
import random
try:
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    from email.mime.base import MimeBase
    from email import encoders
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False
    print("⚠️  مكتبات البريد الإلكتروني غير متاحة - سيتم تعطيل إرسال الرسائل")

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("⚠️  مكتبة PDF غير متاحة - سيتم تعطيل إنشاء PDF")

# إعداد التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///advanced_subscriptions.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'

# إنشاء مجلد الرفع إذا لم يكن موجوداً
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول لهذه الصفحة'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(200), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, user
    company = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)
    email_verified = db.Column(db.Boolean, default=False)
    login_count = db.Column(db.Integer, default=0)
    last_login = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # العلاقات
    subscriptions = db.relationship('Subscription', backref='user', lazy=True)
    invoices = db.relationship('Invoice', backref='user', lazy=True)
    sent_messages = db.relationship('Message', backref='sender', lazy=True)
    notifications = db.relationship('Notification', backref='user', lazy=True)
    reviews = db.relationship('Review', backref='user', lazy=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        return self.role == 'admin'

class CloudProvider(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    website = db.Column(db.String(200))
    description = db.Column(db.Text)
    logo_url = db.Column(db.String(200))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    subscriptions = db.relationship('Subscription', backref='provider', lazy=True)
    reviews = db.relationship('Review', backref='provider', lazy=True)

    def get_average_rating(self):
        """حساب متوسط التقييم"""
        reviews = Review.query.filter_by(provider_id=self.id).all()
        if reviews:
            return sum(r.rating for r in reviews) / len(reviews)
        return 0.0

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    service_type = db.Column(db.String(50), nullable=False)  # web_hosting, cloud_server, database, etc.
    subscription_type = db.Column(db.String(20), nullable=False)  # monthly, semi_annual, annual
    price = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='active')  # active, expired, suspended, cancelled
    
    # معلومات الاتصال
    server_ip = db.Column(db.String(45))  # IPv4 or IPv6
    port = db.Column(db.Integer)
    username = db.Column(db.String(100))
    password = db.Column(db.String(200))
    api_key = db.Column(db.String(500))
    region = db.Column(db.String(100))
    
    # معلومات إضافية محسنة
    priority = db.Column(db.String(20), default='medium')  # low, medium, high, critical
    notes = db.Column(db.Text)
    auto_renew = db.Column(db.Boolean, default=False)
    backup_enabled = db.Column(db.Boolean, default=False)  # النسخ الاحتياطي
    monitoring_enabled = db.Column(db.Boolean, default=False)  # المراقبة
    ssl_enabled = db.Column(db.Boolean, default=False)  # شهادة SSL

    # إحصائيات الاستخدام
    cpu_usage = db.Column(db.Float, default=0.0)  # استخدام المعالج
    memory_usage = db.Column(db.Float, default=0.0)  # استخدام الذاكرة
    disk_usage = db.Column(db.Float, default=0.0)  # استخدام القرص
    bandwidth_usage = db.Column(db.Float, default=0.0)  # استخدام النطاق

    # التواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_backup = db.Column(db.DateTime)  # آخر نسخة احتياطية

    # العلاقات
    invoices = db.relationship('Invoice', backref='subscription', lazy=True)
    files = db.relationship('SubscriptionFile', backref='subscription', lazy=True)
    
    def days_until_expiry(self):
        """حساب عدد الأيام المتبقية حتى انتهاء الاشتراك"""
        if self.end_date:
            delta = self.end_date - date.today()
            return delta.days
        return 0
    
    def is_expiring_soon(self, days=7):
        """فحص إذا كان الاشتراك سينتهي قريباً"""
        return 0 <= self.days_until_expiry() <= days
    
    def is_expired(self):
        """فحص إذا كان الاشتراك منتهي"""
        return self.end_date < date.today()

    def get_health_status(self):
        """حالة صحة الخادم بناءً على الاستخدام"""
        if self.cpu_usage > 90 or self.memory_usage > 90 or self.disk_usage > 90:
            return 'critical'
        elif self.cpu_usage > 70 or self.memory_usage > 70 or self.disk_usage > 70:
            return 'warning'
        else:
            return 'healthy'

class PaymentMethod(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    payment_type = db.Column(db.String(50), nullable=False)  # credit_card, paypal, bank_transfer, etc.
    processing_fee = db.Column(db.Float, default=0.0)  # نسبة الرسوم
    supported_currency = db.Column(db.String(3), default='USD')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='payment_method', lazy=True)

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
    payment_method_id = db.Column(db.Integer, db.ForeignKey('payment_method.id'), nullable=True)
    
    # المبالغ
    subtotal = db.Column(db.Float, nullable=False)
    tax_rate = db.Column(db.Float, default=0.0)  # نسبة الضريبة
    tax_amount = db.Column(db.Float, default=0.0)
    discount_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')
    
    # التواريخ
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.Date)
    
    # الحالة
    status = db.Column(db.String(20), default='pending')  # pending, paid, cancelled, overdue
    payment_status = db.Column(db.String(20), default='pending')  # pending, paid, failed, cancelled
    
    # معلومات إضافية
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, **kwargs):
        super(Invoice, self).__init__(**kwargs)
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
    
    def generate_invoice_number(self):
        """إنشاء رقم فاتورة فريد"""
        today = date.today()
        prefix = f"INV-{today.year}{today.month:02d}"
        
        # البحث عن آخر فاتورة في نفس الشهر
        last_invoice = Invoice.query.filter(
            Invoice.invoice_number.like(f"{prefix}%")
        ).order_by(Invoice.id.desc()).first()
        
        if last_invoice:
            try:
                last_number = int(last_invoice.invoice_number.split('-')[-1])
                new_number = last_number + 1
            except:
                new_number = 1
        else:
            new_number = 1
        
        return f"{prefix}-{new_number:04d}"
    
    def calculate_total(self):
        """حساب المبلغ الإجمالي"""
        self.tax_amount = (self.subtotal * self.tax_rate) / 100
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        return self.total_amount

class MessageTemplate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    body = db.Column(db.Text, nullable=False)
    template_type = db.Column(db.String(50), nullable=False)  # welcome, renewal, payment_due, etc.
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    recipient_email = db.Column(db.String(120), nullable=False)
    recipient_name = db.Column(db.String(200))
    subject = db.Column(db.String(200), nullable=False)
    body = db.Column(db.Text, nullable=False)
    message_type = db.Column(db.String(50), default='custom')  # custom, template, automated
    template_id = db.Column(db.Integer, db.ForeignKey('message_template.id'), nullable=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
    
    # حالة الإرسال
    status = db.Column(db.String(20), default='pending')  # pending, sent, failed, scheduled
    sent_at = db.Column(db.DateTime)
    scheduled_at = db.Column(db.DateTime)
    error_message = db.Column(db.Text)
    
    # إعدادات الإرسال
    send_copy_to_sender = db.Column(db.Boolean, default=False)
    attach_pdf = db.Column(db.Boolean, default=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    template = db.relationship('MessageTemplate', backref='messages', lazy=True)
    related_subscription = db.relationship('Subscription', backref='messages', lazy=True)

# نماذج جديدة للميزات المتقدمة
class Notification(db.Model):
    """نظام الإشعارات المتقدم"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)  # info, warning, error, success
    category = db.Column(db.String(50), default='general')  # subscription, payment, system
    is_read = db.Column(db.Boolean, default=False)
    is_important = db.Column(db.Boolean, default=False)
    action_url = db.Column(db.String(200))  # رابط الإجراء
    action_text = db.Column(db.String(100))  # نص الإجراء
    expires_at = db.Column(db.DateTime)  # تاريخ انتهاء الإشعار
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Review(db.Model):
    """نظام التقييمات والمراجعات"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
    rating = db.Column(db.Integer, nullable=False)  # 1-5 نجوم
    title = db.Column(db.String(200))
    comment = db.Column(db.Text)
    pros = db.Column(db.Text)  # الإيجابيات
    cons = db.Column(db.Text)  # السلبيات
    is_verified = db.Column(db.Boolean, default=False)  # مراجعة موثقة
    is_featured = db.Column(db.Boolean, default=False)  # مراجعة مميزة
    helpful_count = db.Column(db.Integer, default=0)  # عدد الإعجابات
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class SubscriptionFile(db.Model):
    """نظام الملفات والمرفقات"""
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    filename = db.Column(db.String(200), nullable=False)
    original_filename = db.Column(db.String(200), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)  # حجم الملف بالبايت
    file_type = db.Column(db.String(50))  # نوع الملف
    category = db.Column(db.String(50), default='general')  # config, backup, certificate, etc.
    description = db.Column(db.Text)
    is_encrypted = db.Column(db.Boolean, default=False)  # ملف مشفر
    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    uploader = db.relationship('User', backref='uploaded_files', lazy=True)

class SystemSettings(db.Model):
    """إعدادات النظام"""
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    category = db.Column(db.String(50), default='general')
    is_public = db.Column(db.Boolean, default=False)  # إعداد عام أم خاص
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ActivityLog(db.Model):
    """سجل الأنشطة المتقدم"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    action = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    resource_type = db.Column(db.String(50))  # subscription, invoice, user, etc.
    resource_id = db.Column(db.Integer)
    old_values = db.Column(db.Text)  # القيم القديمة (JSON)
    new_values = db.Column(db.Text)  # القيم الجديدة (JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', backref='activities', lazy=True)

# إنشاء الجداول
def create_tables():
    """إنشاء جداول قاعدة البيانات"""
    with app.app_context():
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")

# تهيئة البيانات الأساسية
def initialize_data():
    """تهيئة البيانات الأساسية للنظام"""
    with app.app_context():
        # إنشاء المستخدم المدير
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin',
                company='AdenLink',
                phone='+***********-0000',
                is_active=True,
                email_verified=True
            )
            admin.set_password('123456')
            db.session.add(admin)
        
        # إنشاء مزودي الخدمة
        providers_data = [
            {'name': 'AdenLink', 'slug': 'adenlink', 'website': 'https://adenlink.com', 'description': 'شركة AdenLink للحلول التقنية'},
            {'name': 'Amazon Web Services', 'slug': 'aws', 'website': 'https://aws.amazon.com', 'description': 'خدمات أمازون السحابية'},
            {'name': 'Microsoft Azure', 'slug': 'azure', 'website': 'https://azure.microsoft.com', 'description': 'منصة مايكروسوفت السحابية'},
            {'name': 'Google Cloud Platform', 'slug': 'google-cloud', 'website': 'https://cloud.google.com', 'description': 'منصة جوجل السحابية'},
            {'name': 'DigitalOcean', 'slug': 'digitalocean', 'website': 'https://digitalocean.com', 'description': 'خدمات DigitalOcean السحابية'},
            {'name': 'Vultr', 'slug': 'vultr', 'website': 'https://vultr.com', 'description': 'خدمات Vultr السحابية'},
            {'name': 'Linode', 'slug': 'linode', 'website': 'https://linode.com', 'description': 'خدمات Linode السحابية'}
        ]
        
        for provider_data in providers_data:
            existing = CloudProvider.query.filter_by(slug=provider_data['slug']).first()
            if not existing:
                provider = CloudProvider(**provider_data)
                db.session.add(provider)
        
        # إنشاء طرق الدفع
        payment_methods_data = [
            {'name': 'بطاقة ائتمان', 'payment_type': 'credit_card', 'processing_fee': 2.9, 'supported_currency': 'USD'},
            {'name': 'PayPal', 'payment_type': 'paypal', 'processing_fee': 3.4, 'supported_currency': 'USD'},
            {'name': 'تحويل بنكي', 'payment_type': 'bank_transfer', 'processing_fee': 0.0, 'supported_currency': 'USD'},
            {'name': 'العملات المشفرة', 'payment_type': 'cryptocurrency', 'processing_fee': 1.0, 'supported_currency': 'USD'},
            {'name': 'الدفع النقدي', 'payment_type': 'cash', 'processing_fee': 0.0, 'supported_currency': 'IQD'},
            {'name': 'الشيكات', 'payment_type': 'check', 'processing_fee': 0.0, 'supported_currency': 'USD'}
        ]
        
        for method_data in payment_methods_data:
            existing = PaymentMethod.query.filter_by(name=method_data['name']).first()
            if not existing:
                method = PaymentMethod(**method_data)
                db.session.add(method)
        
        # إنشاء قوالب الرسائل
        templates_data = [
            {
                'name': 'رسالة ترحيب',
                'subject': 'مرحباً بك في {cloud_name}',
                'body': '''مرحباً {customer_name},

نرحب بك في خدمة {cloud_name}!

تفاصيل اشتراكك:
- اسم الاشتراك: {subscription_name}
- السعر: {price} {currency}
- تاريخ الانتهاء: {end_date}
- عنوان الخادم: {server_ip}

شكراً لاختيارك خدماتنا.

مع أطيب التحيات،
فريق AdenLink''',
                'template_type': 'welcome'
            },
            {
                'name': 'تذكير التجديد',
                'subject': 'تذكير: اشتراكك {subscription_name} سينتهي قريباً',
                'body': '''عزيزي {customer_name},

نود تذكيرك بأن اشتراكك في {subscription_name} سينتهي في {end_date}.

تفاصيل الاشتراك:
- الخدمة: {cloud_name}
- السعر: {price} {currency}
- عنوان الخادم: {server_ip}

يرجى تجديد اشتراكك لتجنب انقطاع الخدمة.

مع أطيب التحيات،
فريق AdenLink''',
                'template_type': 'renewal'
            },
            {
                'name': 'استحقاق الدفع',
                'subject': 'فاتورة مستحقة الدفع - {subscription_name}',
                'body': '''عزيزي {customer_name},

لديك فاتورة مستحقة الدفع لاشتراك {subscription_name}.

تفاصيل الفاتورة:
- المبلغ: {price} {currency}
- تاريخ الاستحقاق: {due_date}
- الخدمة: {cloud_name}

يرجى سداد المبلغ في أقرب وقت ممكن.

مع أطيب التحيات،
فريق AdenLink''',
                'template_type': 'payment_due'
            }
        ]
        
        for template_data in templates_data:
            existing = MessageTemplate.query.filter_by(name=template_data['name']).first()
            if not existing:
                template = MessageTemplate(**template_data)
                db.session.add(template)
        
        # إنشاء مستخدمين إضافيين
        users_data = [
            {
                'username': 'ahmed_ali',
                'email': '<EMAIL>',
                'full_name': 'أحمد علي محمد',
                'role': 'user',
                'company': 'شركة التقنية المتقدمة',
                'phone': '+964-************'
            },
            {
                'username': 'sara_hassan',
                'email': '<EMAIL>',
                'full_name': 'سارة حسن أحمد',
                'role': 'user',
                'company': 'مؤسسة الابتكار',
                'phone': '+964-************'
            },
            {
                'username': 'omar_khalil',
                'email': '<EMAIL>',
                'full_name': 'عمر خليل إبراهيم',
                'role': 'admin',
                'company': 'AdenLink',
                'phone': '+964-************'
            }
        ]

        for user_data in users_data:
            existing = User.query.filter_by(username=user_data['username']).first()
            if not existing:
                user = User(**user_data)
                user.set_password('123456')
                user.is_active = True
                user.email_verified = True
                db.session.add(user)

        db.session.commit()

        # إنشاء اشتراكات تجريبية
        subscriptions_data = [
            {
                'name': 'خادم ويب رئيسي - AdenLink',
                'description': 'خادم ويب مخصص للموقع الرئيسي',
                'user_id': 1,  # admin
                'provider_id': 1,  # AdenLink
                'service_type': 'web_hosting',
                'subscription_type': 'annual',
                'price': 1200.00,
                'currency': 'USD',
                'start_date': date(2024, 1, 1),
                'end_date': date(2024, 12, 31),
                'status': 'active',
                'server_ip': '*************',
                'port': 80,
                'username': 'admin',
                'password': 'secure_password_123',
                'priority': 'high'
            },
            {
                'name': 'خادم التطبيقات - AWS',
                'description': 'خادم سحابي لتطبيقات الويب',
                'user_id': 2,  # ahmed_ali
                'provider_id': 2,  # AWS
                'service_type': 'cloud_server',
                'subscription_type': 'monthly',
                'price': 150.00,
                'currency': 'USD',
                'start_date': date(2024, 6, 1),
                'end_date': date(2024, 7, 1),
                'status': 'active',
                'server_ip': '************',
                'port': 443,
                'username': 'ec2-user',
                'api_key': 'AKIA1234567890ABCDEF',
                'region': 'us-east-1',
                'priority': 'medium'
            },
            {
                'name': 'قاعدة بيانات - Azure',
                'description': 'قاعدة بيانات SQL مُدارة',
                'user_id': 3,  # sara_hassan
                'provider_id': 3,  # Azure
                'service_type': 'database',
                'subscription_type': 'semi_annual',
                'price': 600.00,
                'currency': 'USD',
                'start_date': date(2024, 3, 1),
                'end_date': date(2024, 9, 1),
                'status': 'active',
                'server_ip': 'mydb.database.windows.net',
                'port': 1433,
                'username': 'dbadmin',
                'password': 'ComplexPassword123!',
                'region': 'East US',
                'priority': 'high'
            },
            {
                'name': 'خدمة التخزين السحابي - Google Cloud',
                'description': 'تخزين ملفات ونسخ احتياطية',
                'user_id': 4,  # omar_khalil
                'provider_id': 4,  # Google Cloud
                'service_type': 'storage',
                'subscription_type': 'monthly',
                'price': 75.00,
                'currency': 'USD',
                'start_date': date(2024, 5, 1),
                'end_date': date(2024, 8, 1),
                'status': 'active',
                'api_key': 'AIzaSyDXXXXXXXXXXXXXXXXXXXXXXXXX',
                'region': 'us-central1',
                'priority': 'low'
            },
            {
                'name': 'خادم تطوير منتهي - DigitalOcean',
                'description': 'خادم تطوير للاختبارات',
                'user_id': 2,  # ahmed_ali
                'provider_id': 5,  # DigitalOcean
                'service_type': 'development',
                'subscription_type': 'monthly',
                'price': 50.00,
                'currency': 'USD',
                'start_date': date(2024, 1, 1),
                'end_date': date(2024, 6, 1),
                'status': 'expired',
                'server_ip': '*************',
                'port': 22,
                'username': 'root',
                'password': 'dev_password_456',
                'region': 'nyc3',
                'priority': 'low'
            }
        ]

        for sub_data in subscriptions_data:
            existing = Subscription.query.filter_by(name=sub_data['name']).first()
            if not existing:
                subscription = Subscription(**sub_data)
                db.session.add(subscription)

        db.session.commit()

        # إنشاء فواتير تجريبية
        invoices_data = [
            {
                'user_id': 1,
                'subscription_id': 1,
                'payment_method_id': 1,
                'subtotal': 1200.00,
                'tax_rate': 0.0,
                'tax_amount': 0.0,
                'discount_amount': 0.0,
                'total_amount': 1200.00,
                'currency': 'USD',
                'issue_date': date(2024, 1, 1),
                'due_date': date(2024, 1, 31),
                'paid_date': date(2024, 1, 15),
                'status': 'paid',
                'payment_status': 'paid',
                'notes': 'فاتورة الاشتراك السنوي - مدفوعة'
            },
            {
                'user_id': 2,
                'subscription_id': 2,
                'payment_method_id': 2,
                'subtotal': 150.00,
                'tax_rate': 5.0,
                'tax_amount': 7.5,
                'discount_amount': 10.0,
                'total_amount': 147.5,
                'currency': 'USD',
                'issue_date': date(2024, 6, 1),
                'due_date': date(2024, 6, 30),
                'status': 'pending',
                'payment_status': 'pending',
                'notes': 'فاتورة شهرية - في انتظار الدفع'
            },
            {
                'user_id': 3,
                'subscription_id': 3,
                'payment_method_id': 3,
                'subtotal': 600.00,
                'tax_rate': 8.0,
                'tax_amount': 48.0,
                'discount_amount': 50.0,
                'total_amount': 598.0,
                'currency': 'USD',
                'issue_date': date(2024, 3, 1),
                'due_date': date(2024, 3, 31),
                'paid_date': date(2024, 3, 20),
                'status': 'paid',
                'payment_status': 'paid',
                'notes': 'فاتورة نصف سنوية - مدفوعة'
            },
            {
                'user_id': 4,
                'subscription_id': 4,
                'payment_method_id': 1,
                'subtotal': 75.00,
                'tax_rate': 0.0,
                'tax_amount': 0.0,
                'discount_amount': 5.0,
                'total_amount': 70.0,
                'currency': 'USD',
                'issue_date': date(2024, 5, 1),
                'due_date': date(2024, 5, 31),
                'status': 'pending',
                'payment_status': 'pending',
                'notes': 'فاتورة التخزين السحابي - معلقة'
            }
        ]

        for inv_data in invoices_data:
            # التحقق من عدم وجود فاتورة مشابهة
            existing = Invoice.query.filter_by(
                user_id=inv_data['user_id'],
                subscription_id=inv_data['subscription_id'],
                issue_date=inv_data['issue_date']
            ).first()
            if not existing:
                invoice = Invoice(**inv_data)
                invoice.calculate_total()
                db.session.add(invoice)

        db.session.commit()

        # إضافة بيانات الاستخدام للاشتراكات
        subscriptions = Subscription.query.all()
        for sub in subscriptions:
            sub.cpu_usage = random.uniform(10, 95)
            sub.memory_usage = random.uniform(20, 90)
            sub.disk_usage = random.uniform(15, 85)
            sub.bandwidth_usage = random.uniform(5, 80)
            sub.backup_enabled = random.choice([True, False])
            sub.monitoring_enabled = True
            sub.ssl_enabled = random.choice([True, False])
            if sub.backup_enabled:
                sub.last_backup = datetime.now() - timedelta(days=random.randint(1, 7))

        # إضافة إشعارات تجريبية
        admin_user = User.query.filter_by(username='admin').first()
        if admin_user:
            notifications_data = [
                {
                    'title': 'مرحباً بك في النظام المحسن!',
                    'message': 'تم تطوير النظام بميزات متقدمة جديدة تشمل الذكاء الاصطناعي والتقويم التفاعلي.',
                    'notification_type': 'success',
                    'category': 'system',
                    'is_important': True
                },
                {
                    'title': 'تحذير: خادم يحتاج انتباه',
                    'message': 'الخادم "خادم ويب رئيسي" يعاني من استخدام مرتفع للمعالج.',
                    'notification_type': 'warning',
                    'category': 'monitoring',
                    'is_important': True
                },
                {
                    'title': 'تم تفعيل النسخ الاحتياطي',
                    'message': 'تم تفعيل النسخ الاحتياطي التلقائي لجميع الخوادم الحرجة.',
                    'notification_type': 'info',
                    'category': 'backup'
                }
            ]

            for notif_data in notifications_data:
                notification = Notification(user_id=admin_user.id, **notif_data)
                db.session.add(notification)

        # إضافة مراجعات تجريبية
        providers = CloudProvider.query.all()
        users = User.query.all()

        reviews_data = [
            {
                'rating': 5,
                'title': 'خدمة ممتازة ودعم رائع',
                'comment': 'أستخدم خدمات AdenLink منذ سنة وأنا راضي جداً عن الأداء والدعم الفني.',
                'pros': 'سرعة عالية، دعم فني ممتاز، أسعار مناسبة',
                'cons': 'لا يوجد',
                'is_verified': True,
                'is_featured': True
            },
            {
                'rating': 4,
                'title': 'خدمة جيدة مع بعض التحسينات المطلوبة',
                'comment': 'الخدمة جيدة بشكل عام لكن يمكن تحسين سرعة الاستجابة.',
                'pros': 'استقرار جيد، واجهة سهلة',
                'cons': 'بطء أحياناً في الاستجابة',
                'is_verified': True
            }
        ]

        for i, review_data in enumerate(reviews_data):
            if i < len(providers) and i < len(users):
                review = Review(
                    user_id=users[i].id,
                    provider_id=providers[i].id,
                    **review_data
                )
                db.session.add(review)

        db.session.commit()
        print("✅ تم تهيئة البيانات الأساسية والتجريبية المحسنة")

# وظائف مساعدة محسنة ومتقدمة
def log_activity(user_id, action, description, resource_type=None, resource_id=None, old_values=None, new_values=None):
    """تسجيل نشاط في السجل"""
    try:
        activity = ActivityLog(
            user_id=user_id,
            action=action,
            description=description,
            ip_address=request.remote_addr if request else None,
            user_agent=request.user_agent.string if request else None,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=json.dumps(old_values) if old_values else None,
            new_values=json.dumps(new_values) if new_values else None
        )
        db.session.add(activity)
        db.session.commit()
    except Exception as e:
        print(f"خطأ في تسجيل النشاط: {e}")

def create_notification(user_id, title, message, notification_type='info', category='general', action_url=None, action_text=None, is_important=False):
    """إنشاء إشعار جديد"""
    try:
        notification = Notification(
            user_id=user_id,
            title=title,
            message=message,
            notification_type=notification_type,
            category=category,
            action_url=action_url,
            action_text=action_text,
            is_important=is_important
        )
        db.session.add(notification)
        db.session.commit()
        return notification
    except Exception as e:
        print(f"خطأ في إنشاء الإشعار: {e}")
        return None

def generate_ai_insights():
    """توليد رؤى ذكية بالذكاء الاصطناعي (محاكاة)"""
    insights = []

    # تحليل الاشتراكات
    active_subs = Subscription.query.filter_by(status='active').count()
    expired_subs = Subscription.query.filter_by(status='expired').count()

    if expired_subs > active_subs * 0.2:
        insights.append({
            'type': 'warning',
            'title': 'معدل انتهاء مرتفع',
            'message': f'لديك {expired_subs} اشتراك منتهي مقابل {active_subs} نشط. يُنصح بمراجعة استراتيجية التجديد.',
            'action': 'مراجعة الاشتراكات المنتهية',
            'priority': 'high'
        })

    # تحليل الإيرادات
    current_month_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter(
        Invoice.payment_status == 'paid',
        db.extract('month', Invoice.paid_date) == datetime.now().month,
        db.extract('year', Invoice.paid_date) == datetime.now().year
    ).scalar() or 0

    if current_month_revenue > 0:
        insights.append({
            'type': 'success',
            'title': 'أداء مالي جيد',
            'message': f'تم تحقيق إيرادات بقيمة ${current_month_revenue:.2f} هذا الشهر.',
            'action': 'عرض التقرير المالي',
            'priority': 'normal'
        })

    # تحليل صحة الخوادم
    critical_servers = Subscription.query.filter(
        db.or_(
            Subscription.cpu_usage > 85,
            Subscription.memory_usage > 85,
            Subscription.disk_usage > 85
        )
    ).count()

    if critical_servers > 0:
        insights.append({
            'type': 'error',
            'title': 'تحذير: خوادم تحتاج انتباه',
            'message': f'{critical_servers} خادم يعاني من استخدام مرتفع للموارد.',
            'action': 'فحص الخوادم',
            'priority': 'high'
        })

    # اقتراحات التحسين
    insights.append({
        'type': 'info',
        'title': 'اقتراح تحسين',
        'message': 'يمكنك تفعيل النسخ الاحتياطي التلقائي لحماية أفضل لبياناتك.',
        'action': 'تفعيل النسخ الاحتياطي',
        'priority': 'normal'
    })

    return insights

def get_calendar_events():
    """أحداث التقويم للاشتراكات"""
    events = []

    # الاشتراكات المنتهية قريباً
    expiring_subs = Subscription.query.filter(
        Subscription.end_date >= date.today(),
        Subscription.end_date <= date.today() + timedelta(days=30),
        Subscription.status == 'active'
    ).all()

    for sub in expiring_subs:
        events.append({
            'title': f'انتهاء: {sub.name}',
            'date': sub.end_date.isoformat(),
            'type': 'expiry',
            'color': '#ff6b6b' if sub.days_until_expiry() <= 7 else '#ffa500',
            'subscription_id': sub.id
        })

    # الفواتير المستحقة
    due_invoices = Invoice.query.filter(
        Invoice.due_date >= date.today(),
        Invoice.due_date <= date.today() + timedelta(days=30),
        Invoice.payment_status == 'pending'
    ).all()

    for invoice in due_invoices:
        events.append({
            'title': f'استحقاق فاتورة: {invoice.invoice_number}',
            'date': invoice.due_date.isoformat(),
            'type': 'payment',
            'color': '#3b82f6',
            'invoice_id': invoice.id
        })

    # النسخ الاحتياطية المجدولة
    backup_subs = Subscription.query.filter_by(backup_enabled=True, status='active').all()
    for sub in backup_subs:
        # محاكاة جدولة النسخ الاحتياطي (كل أسبوع)
        next_backup = date.today() + timedelta(days=7)
        events.append({
            'title': f'نسخة احتياطية: {sub.name}',
            'date': next_backup.isoformat(),
            'type': 'backup',
            'color': '#10b981',
            'subscription_id': sub.id
        })

    return events

def get_system_stats():
    """إحصائيات النظام المحسنة"""
    stats = {}

    # إحصائيات أساسية
    stats['total_users'] = User.query.count()
    stats['active_users'] = User.query.filter_by(is_active=True).count()
    stats['total_subscriptions'] = Subscription.query.count()
    stats['active_subscriptions'] = Subscription.query.filter_by(status='active').count()
    stats['total_providers'] = CloudProvider.query.count()
    stats['featured_providers'] = CloudProvider.query.filter_by(is_featured=True).count()

    # إحصائيات مالية
    total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(payment_status='paid').scalar() or 0
    pending_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(payment_status='pending').scalar() or 0
    stats['total_revenue'] = total_revenue
    stats['pending_revenue'] = pending_revenue

    # إحصائيات الاشتراكات
    expiring_soon = Subscription.query.filter(
        Subscription.end_date <= date.today() + timedelta(days=7),
        Subscription.end_date >= date.today(),
        Subscription.status == 'active'
    ).count()
    stats['expiring_soon'] = expiring_soon

    # إحصائيات الصحة
    critical_subscriptions = Subscription.query.filter(
        db.or_(
            Subscription.cpu_usage > 90,
            Subscription.memory_usage > 90,
            Subscription.disk_usage > 90
        )
    ).count()
    stats['critical_subscriptions'] = critical_subscriptions

    # إحصائيات الإشعارات
    unread_notifications = Notification.query.filter_by(is_read=False).count()
    stats['unread_notifications'] = unread_notifications

    # إحصائيات الملفات
    total_files = SubscriptionFile.query.count()
    total_file_size = db.session.query(db.func.sum(SubscriptionFile.file_size)).scalar() or 0
    stats['total_files'] = total_files
    stats['total_file_size'] = total_file_size / (1024 * 1024)  # MB

    return stats

# وظائف مساعدة لإرسال البريد الإلكتروني
def send_email(to_email, subject, body, attach_pdf=False, pdf_content=None):
    """إرسال بريد إلكتروني"""
    if not EMAIL_AVAILABLE:
        return False, "مكتبات البريد الإلكتروني غير متاحة"

    try:
        # محاكاة إرسال الرسالة (للتطوير)
        print(f"📧 محاكاة إرسال رسالة:")
        print(f"   إلى: {to_email}")
        print(f"   الموضوع: {subject}")
        print(f"   النص: {body[:100]}...")
        if attach_pdf:
            print(f"   مرفق PDF: نعم")

        return True, "تم إرسال الرسالة بنجاح (محاكاة)"
    except Exception as e:
        return False, f"فشل في إرسال الرسالة: {str(e)}"

def generate_pdf_report(subscription):
    """إنشاء تقرير PDF للاشتراك"""
    if not PDF_AVAILABLE:
        return None

    try:
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)

        # إعداد الخط
        p.setFont("Helvetica", 12)

        # عنوان التقرير
        p.drawString(100, 750, f"Subscription Report - {subscription.name}")
        p.drawString(100, 730, f"Provider: {subscription.provider.name}")
        p.drawString(100, 710, f"Price: {subscription.price} {subscription.currency}")
        p.drawString(100, 690, f"Start Date: {subscription.start_date}")
        p.drawString(100, 670, f"End Date: {subscription.end_date}")
        p.drawString(100, 650, f"Status: {subscription.status}")

        if subscription.server_ip:
            p.drawString(100, 630, f"Server IP: {subscription.server_ip}")
        if subscription.port:
            p.drawString(100, 610, f"Port: {subscription.port}")

        p.showPage()
        p.save()

        buffer.seek(0)
        return buffer.getvalue()
    except Exception as e:
        print(f"خطأ في إنشاء PDF: {e}")
        return None

def replace_template_variables(text, subscription=None, customer=None, invoice=None):
    """استبدال المتغيرات في قوالب الرسائل"""
    if subscription:
        text = text.replace('{subscription_name}', subscription.name or '')
        text = text.replace('{cloud_name}', subscription.provider.name if subscription.provider else '')
        text = text.replace('{price}', str(subscription.price))
        text = text.replace('{currency}', subscription.currency)
        text = text.replace('{end_date}', subscription.end_date.strftime('%Y-%m-%d') if subscription.end_date else '')
        text = text.replace('{server_ip}', subscription.server_ip or '')
        text = text.replace('{start_date}', subscription.start_date.strftime('%Y-%m-%d') if subscription.start_date else '')

    if customer:
        text = text.replace('{customer_name}', customer.full_name or '')
        text = text.replace('{customer_email}', customer.email or '')
        text = text.replace('{customer_company}', customer.company or '')

    if invoice:
        text = text.replace('{invoice_number}', invoice.invoice_number or '')
        text = text.replace('{due_date}', invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '')
        text = text.replace('{total_amount}', str(invoice.total_amount))

    return text

# المسارات الأساسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template_string(WELCOME_TEMPLATE)



# إضافة اشتراك جديد
@app.route('/add_subscription')
@login_required
def add_subscription():
    return render_template_string(ADD_SUBSCRIPTION_TEMPLATE)

# تقارير الاشتراكات
@app.route('/subscription_reports')
@login_required
def subscription_reports():
    return render_template_string(SUBSCRIPTION_REPORTS_TEMPLATE)

# قائمة الفواتير
@app.route('/invoices')
@login_required
def invoices():
    return render_template_string(INVOICES_TEMPLATE)

# إنشاء فاتورة جديدة
@app.route('/create_invoice')
@login_required
def create_invoice():
    return render_template_string(CREATE_INVOICE_TEMPLATE)







@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            login_user(user)

            # تحديث معلومات تسجيل الدخول
            user.last_login = datetime.now()
            user.login_count += 1
            db.session.commit()

            # تسجيل النشاط
            log_activity(user.id, 'login', f'تسجيل دخول من {request.remote_addr}')

            # إنشاء إشعار ترحيب
            create_notification(
                user.id,
                'مرحباً بك!',
                f'تم تسجيل دخولك بنجاح في {datetime.now().strftime("%Y-%m-%d %H:%M")}',
                'success',
                'system'
            )

            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    # تسجيل النشاط
    log_activity(current_user.id, 'logout', 'تسجيل خروج')

    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

# مسارات الميزات الجديدة المتقدمة

@app.route('/notifications')
@login_required
def notifications():
    """صفحة الإشعارات"""
    page = request.args.get('page', 1, type=int)
    notifications = Notification.query.filter_by(user_id=current_user.id).order_by(
        Notification.is_important.desc(),
        Notification.created_at.desc()
    ).paginate(page=page, per_page=20, error_out=False)

    return render_template_string(NOTIFICATIONS_TEMPLATE, notifications=notifications)

@app.route('/mark_notification_read/<int:notification_id>')
@login_required
def mark_notification_read(notification_id):
    """تحديد إشعار كمقروء"""
    notification = Notification.query.get_or_404(notification_id)
    if notification.user_id == current_user.id:
        notification.is_read = True
        db.session.commit()
    return redirect(url_for('notifications'))

@app.route('/calendar')
@login_required
def calendar_view():
    """تقويم الاشتراكات التفاعلي"""
    events = get_calendar_events()

    # فلترة الأحداث حسب صلاحية المستخدم
    if not current_user.is_admin():
        user_subscription_ids = [s.id for s in current_user.subscriptions]
        events = [e for e in events if e.get('subscription_id') in user_subscription_ids or 'subscription_id' not in e]

    return render_template_string(CALENDAR_TEMPLATE, events=events)

@app.route('/ai_insights')
@login_required
def ai_insights():
    """صفحة الرؤى الذكية"""
    insights = generate_ai_insights()

    # إحصائيات متقدمة للذكاء الاصطناعي
    advanced_stats = {
        'growth_rate': random.uniform(5, 25),  # محاكاة معدل النمو
        'efficiency_score': random.uniform(75, 95),  # درجة الكفاءة
        'cost_optimization': random.uniform(10, 30),  # توفير التكاليف المحتمل
        'uptime_percentage': random.uniform(98, 99.9),  # نسبة التشغيل
    }

    return render_template_string(AI_INSIGHTS_TEMPLATE, insights=insights, advanced_stats=advanced_stats)

@app.route('/reviews')
@login_required
def reviews():
    """صفحة المراجعات والتقييمات"""
    if current_user.is_admin():
        reviews = Review.query.order_by(Review.created_at.desc()).all()
    else:
        reviews = Review.query.filter_by(user_id=current_user.id).order_by(Review.created_at.desc()).all()

    providers = CloudProvider.query.filter_by(is_active=True).all()

    return render_template_string(REVIEWS_TEMPLATE, reviews=reviews, providers=providers)

@app.route('/add_review', methods=['POST'])
@login_required
def add_review():
    """إضافة مراجعة جديدة"""
    try:
        provider_id = request.form.get('provider_id')
        subscription_id = request.form.get('subscription_id')
        rating = int(request.form.get('rating'))
        title = request.form.get('title')
        comment = request.form.get('comment')
        pros = request.form.get('pros')
        cons = request.form.get('cons')

        review = Review(
            user_id=current_user.id,
            provider_id=provider_id,
            subscription_id=subscription_id if subscription_id else None,
            rating=rating,
            title=title,
            comment=comment,
            pros=pros,
            cons=cons
        )

        db.session.add(review)
        db.session.commit()

        # تسجيل النشاط
        log_activity(current_user.id, 'add_review', f'إضافة مراجعة لمزود الخدمة {provider_id}')

        flash('تم إضافة المراجعة بنجاح!', 'success')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')

    return redirect(url_for('reviews'))

@app.route('/files/<int:subscription_id>')
@login_required
def subscription_files(subscription_id):
    """ملفات الاشتراك"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # فحص الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        abort(403)

    files = SubscriptionFile.query.filter_by(subscription_id=subscription_id).order_by(
        SubscriptionFile.created_at.desc()
    ).all()

    return render_template_string(FILES_TEMPLATE, subscription=subscription, files=files)

@app.route('/upload_file/<int:subscription_id>', methods=['POST'])
@login_required
def upload_file(subscription_id):
    """رفع ملف للاشتراك"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # فحص الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        abort(403)

    if 'file' not in request.files:
        flash('لم يتم اختيار ملف', 'error')
        return redirect(url_for('subscription_files', subscription_id=subscription_id))

    file = request.files['file']
    if file.filename == '':
        flash('لم يتم اختيار ملف', 'error')
        return redirect(url_for('subscription_files', subscription_id=subscription_id))

    if file:
        try:
            # إنشاء اسم ملف آمن
            filename = secure_filename(file.filename)
            unique_filename = f"{uuid.uuid4()}_{filename}"

            # إنشاء مجلد للاشتراك
            subscription_folder = os.path.join(app.config['UPLOAD_FOLDER'], f'subscription_{subscription_id}')
            os.makedirs(subscription_folder, exist_ok=True)

            file_path = os.path.join(subscription_folder, unique_filename)
            file.save(file_path)

            # حفظ معلومات الملف في قاعدة البيانات
            file_record = SubscriptionFile(
                subscription_id=subscription_id,
                filename=unique_filename,
                original_filename=filename,
                file_path=file_path,
                file_size=os.path.getsize(file_path),
                file_type=filename.split('.')[-1].lower() if '.' in filename else 'unknown',
                category=request.form.get('category', 'general'),
                description=request.form.get('description', ''),
                uploaded_by=current_user.id
            )

            db.session.add(file_record)
            db.session.commit()

            # تسجيل النشاط
            log_activity(current_user.id, 'upload_file', f'رفع ملف {filename} للاشتراك {subscription.name}')

            flash('تم رفع الملف بنجاح!', 'success')
        except Exception as e:
            flash(f'حدث خطأ في رفع الملف: {str(e)}', 'error')

    return redirect(url_for('subscription_files', subscription_id=subscription_id))

@app.route('/download_file/<int:file_id>')
@login_required
def download_file(file_id):
    """تحميل ملف"""
    file_record = SubscriptionFile.query.get_or_404(file_id)
    subscription = file_record.subscription

    # فحص الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        abort(403)

    try:
        return send_file(file_record.file_path, as_attachment=True, download_name=file_record.original_filename)
    except Exception as e:
        flash(f'حدث خطأ في تحميل الملف: {str(e)}', 'error')
        return redirect(url_for('subscription_files', subscription_id=subscription.id))

@app.route('/system_health')
@login_required
def system_health():
    """صفحة صحة النظام"""
    if not current_user.is_admin():
        abort(403)

    # إحصائيات صحة النظام
    health_stats = {
        'total_subscriptions': Subscription.query.count(),
        'healthy_subscriptions': Subscription.query.filter(
            Subscription.cpu_usage <= 70,
            Subscription.memory_usage <= 70,
            Subscription.disk_usage <= 70
        ).count(),
        'warning_subscriptions': Subscription.query.filter(
            db.or_(
                db.and_(Subscription.cpu_usage > 70, Subscription.cpu_usage <= 90),
                db.and_(Subscription.memory_usage > 70, Subscription.memory_usage <= 90),
                db.and_(Subscription.disk_usage > 70, Subscription.disk_usage <= 90)
            )
        ).count(),
        'critical_subscriptions': Subscription.query.filter(
            db.or_(
                Subscription.cpu_usage > 90,
                Subscription.memory_usage > 90,
                Subscription.disk_usage > 90
            )
        ).count()
    }

    # الخوادم الحرجة
    critical_servers = Subscription.query.filter(
        db.or_(
            Subscription.cpu_usage > 85,
            Subscription.memory_usage > 85,
            Subscription.disk_usage > 85
        )
    ).all()

    return render_template_string(SYSTEM_HEALTH_TEMPLATE, health_stats=health_stats, critical_servers=critical_servers)

@app.route('/backup_management')
@login_required
def backup_management():
    """إدارة النسخ الاحتياطية"""
    if current_user.is_admin():
        subscriptions = Subscription.query.filter_by(status='active').all()
    else:
        subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').all()

    return render_template_string(BACKUP_MANAGEMENT_TEMPLATE, subscriptions=subscriptions)

@app.route('/toggle_backup/<int:subscription_id>')
@login_required
def toggle_backup(subscription_id):
    """تفعيل/تعطيل النسخ الاحتياطي"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # فحص الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        abort(403)

    subscription.backup_enabled = not subscription.backup_enabled
    if subscription.backup_enabled:
        subscription.last_backup = datetime.now()

    db.session.commit()

    # تسجيل النشاط
    action = 'تفعيل' if subscription.backup_enabled else 'تعطيل'
    log_activity(current_user.id, 'toggle_backup', f'{action} النسخ الاحتياطي للاشتراك {subscription.name}')

    # إنشاء إشعار
    create_notification(
        current_user.id,
        f'{action} النسخ الاحتياطي',
        f'تم {action} النسخ الاحتياطي للاشتراك {subscription.name}',
        'success' if subscription.backup_enabled else 'info',
        'backup'
    )

    flash(f'تم {action} النسخ الاحتياطي بنجاح!', 'success')
    return redirect(url_for('backup_management'))

@app.route('/api/subscription_usage/<int:subscription_id>')
@login_required
def api_subscription_usage(subscription_id):
    """API لبيانات استخدام الاشتراك"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # فحص الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        abort(403)

    # محاكاة بيانات الاستخدام (في التطبيق الحقيقي ستأتي من API المزود)
    usage_data = {
        'cpu_usage': subscription.cpu_usage,
        'memory_usage': subscription.memory_usage,
        'disk_usage': subscription.disk_usage,
        'bandwidth_usage': subscription.bandwidth_usage,
        'timestamp': datetime.utcnow().isoformat()
    }

    return jsonify(usage_data)

@app.route('/update_subscription_usage/<int:subscription_id>', methods=['POST'])
@login_required
def update_subscription_usage(subscription_id):
    """تحديث بيانات استخدام الاشتراك (محاكاة)"""
    if not current_user.is_admin():
        abort(403)

    subscription = Subscription.query.get_or_404(subscription_id)

    # محاكاة تحديث البيانات
    subscription.cpu_usage = random.uniform(10, 95)
    subscription.memory_usage = random.uniform(20, 90)
    subscription.disk_usage = random.uniform(15, 85)
    subscription.bandwidth_usage = random.uniform(5, 80)

    db.session.commit()

    # إنشاء إشعار إذا كان الاستخدام مرتفع
    if subscription.cpu_usage > 85 or subscription.memory_usage > 85 or subscription.disk_usage > 85:
        create_notification(
            subscription.user_id,
            'تحذير: استخدام مرتفع',
            f'الخادم {subscription.name} يعاني من استخدام مرتفع للموارد',
            'warning',
            'monitoring',
            url_for('subscription_files', subscription_id=subscription.id),
            'عرض التفاصيل',
            True
        )

    return jsonify({'status': 'success', 'message': 'تم تحديث البيانات'})

@app.route('/dashboard')
@login_required
def dashboard():
    # إحصائيات عامة
    if current_user.is_admin():
        total_subscriptions = Subscription.query.count()
        active_subscriptions = Subscription.query.filter_by(status='active').count()
        total_users = User.query.count()
        total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(payment_status='paid').scalar() or 0
    else:
        total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
        active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
        total_users = 1
        total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(user_id=current_user.id, payment_status='paid').scalar() or 0

    # الاشتراكات المنتهية قريباً
    expiring_soon = Subscription.query.filter(
        Subscription.end_date <= date.today() + timedelta(days=7),
        Subscription.end_date >= date.today(),
        Subscription.status == 'active'
    ).all()

    if not current_user.is_admin():
        expiring_soon = [sub for sub in expiring_soon if sub.user_id == current_user.id]

    # الفواتير المعلقة
    if current_user.is_admin():
        pending_invoices = Invoice.query.filter_by(payment_status='pending').count()
    else:
        pending_invoices = Invoice.query.filter_by(user_id=current_user.id, payment_status='pending').count()

    stats = {
        'total_subscriptions': total_subscriptions,
        'active_subscriptions': active_subscriptions,
        'total_users': total_users,
        'total_revenue': total_revenue,
        'expiring_soon': len(expiring_soon),
        'pending_invoices': pending_invoices
    }

    # إضافة الميزات الجديدة
    ai_insights = generate_ai_insights()
    calendar_events = get_calendar_events()

    # الإشعارات الحديثة
    recent_notifications = Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).order_by(Notification.created_at.desc()).limit(5).all()

    # الأنشطة الحديثة
    recent_activities = ActivityLog.query.filter_by(
        user_id=current_user.id
    ).order_by(ActivityLog.created_at.desc()).limit(10).all()

    # الاشتراكات الحرجة
    critical_subscriptions = Subscription.query.filter(
        db.or_(
            Subscription.cpu_usage > 85,
            Subscription.memory_usage > 85,
            Subscription.disk_usage > 85
        )
    ).all()

    if not current_user.is_admin():
        critical_subscriptions = [s for s in critical_subscriptions if s.user_id == current_user.id]

    return render_template_string(DASHBOARD_TEMPLATE,
                                stats=stats,
                                expiring_subscriptions=expiring_soon,
                                ai_insights=ai_insights,
                                calendar_events=calendar_events,
                                recent_notifications=recent_notifications,
                                recent_activities=recent_activities,
                                critical_subscriptions=critical_subscriptions)

# مسارات إدارة الاشتراكات
@app.route('/subscriptions')
@login_required
def subscriptions():
    if current_user.is_admin():
        subscriptions_list = Subscription.query.all()
    else:
        subscriptions_list = Subscription.query.filter_by(user_id=current_user.id).all()

    return render_template_string(SUBSCRIPTIONS_TEMPLATE, subscriptions=subscriptions_list)

@app.route('/subscription_charts')
@login_required
def subscription_charts():
    # إحصائيات الاشتراكات
    if current_user.is_admin():
        total_subscriptions = Subscription.query.count()
        active_subscriptions = Subscription.query.filter_by(status='active').count()
        expired_subscriptions = Subscription.query.filter_by(status='expired').count()
        suspended_subscriptions = Subscription.query.filter_by(status='suspended').count()

        # توزيع حسب المزودين
        providers_data = db.session.query(
            CloudProvider.name,
            db.func.count(Subscription.id).label('count')
        ).join(Subscription).group_by(CloudProvider.name).all()

        # توزيع حسب نوع الاشتراك
        subscription_types = db.session.query(
            Subscription.subscription_type,
            db.func.count(Subscription.id).label('count')
        ).group_by(Subscription.subscription_type).all()

        # الإيرادات الشهرية
        monthly_revenue = db.session.query(
            db.func.strftime('%Y-%m', Invoice.issue_date).label('month'),
            db.func.sum(Invoice.total_amount).label('revenue')
        ).filter_by(payment_status='paid').group_by('month').all()

    else:
        total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
        active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
        expired_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='expired').count()
        suspended_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='suspended').count()

        providers_data = db.session.query(
            CloudProvider.name,
            db.func.count(Subscription.id).label('count')
        ).join(Subscription).filter(Subscription.user_id == current_user.id).group_by(CloudProvider.name).all()

        subscription_types = db.session.query(
            Subscription.subscription_type,
            db.func.count(Subscription.id).label('count')
        ).filter_by(user_id=current_user.id).group_by(Subscription.subscription_type).all()

        monthly_revenue = db.session.query(
            db.func.strftime('%Y-%m', Invoice.issue_date).label('month'),
            db.func.sum(Invoice.total_amount).label('revenue')
        ).filter_by(user_id=current_user.id, payment_status='paid').group_by('month').all()

    charts_data = {
        'status_distribution': {
            'active': active_subscriptions,
            'expired': expired_subscriptions,
            'suspended': suspended_subscriptions
        },
        'providers_distribution': [{'name': p.name, 'count': p.count} for p in providers_data],
        'types_distribution': [{'type': t.subscription_type, 'count': t.count} for t in subscription_types],
        'monthly_revenue': [{'month': r.month, 'revenue': float(r.revenue)} for r in monthly_revenue]
    }

    return render_template_string(SUBSCRIPTION_CHARTS_TEMPLATE, charts_data=charts_data)

# مسارات مركز التواصل
@app.route('/messages')
@login_required
def message_center_view():
    if current_user.is_admin():
        messages = Message.query.order_by(Message.created_at.desc()).all()
    else:
        messages = Message.query.filter_by(sender_id=current_user.id).order_by(Message.created_at.desc()).all()

    return render_template_string(MESSAGE_CENTER_TEMPLATE, messages=messages)

@app.route('/send_message', methods=['GET', 'POST'])
@login_required
def send_message():
    if request.method == 'POST':
        try:
            recipient_type = request.form.get('recipient_type')
            custom_email = request.form.get('custom_email')
            subscription_id = request.form.get('subscription_id')
            template_id = request.form.get('template_id')
            custom_subject = request.form.get('custom_subject')
            custom_body = request.form.get('custom_body')
            send_copy = request.form.get('send_copy') == 'on'
            attach_pdf = request.form.get('attach_pdf') == 'on'
            schedule_send = request.form.get('schedule_send')

            recipients = []

            # تحديد المستلمين
            if recipient_type == 'custom' and custom_email:
                recipients.append({'email': custom_email, 'name': 'عميل'})
            elif recipient_type == 'subscription' and subscription_id:
                subscription = db.session.get(Subscription, subscription_id)
                if subscription and (current_user.is_admin() or subscription.user_id == current_user.id):
                    recipients.append({
                        'email': subscription.user.email,
                        'name': subscription.user.full_name,
                        'subscription': subscription
                    })
            elif recipient_type == 'all_active' and current_user.is_admin():
                active_users = User.query.join(Subscription).filter(
                    Subscription.status == 'active'
                ).distinct().all()
                for user in active_users:
                    recipients.append({'email': user.email, 'name': user.full_name})
            elif recipient_type == 'expiring_soon' and current_user.is_admin():
                expiring_subscriptions = Subscription.query.filter(
                    Subscription.end_date <= date.today() + timedelta(days=7),
                    Subscription.end_date >= date.today(),
                    Subscription.status == 'active'
                ).all()
                for subscription in expiring_subscriptions:
                    recipients.append({
                        'email': subscription.user.email,
                        'name': subscription.user.full_name,
                        'subscription': subscription
                    })

            # إعداد الرسالة
            if template_id:
                template = db.session.get(MessageTemplate, template_id)
                if template:
                    subject = template.subject
                    body = template.body
                else:
                    subject = custom_subject
                    body = custom_body
            else:
                subject = custom_subject
                body = custom_body

            # إرسال الرسائل
            sent_count = 0
            failed_count = 0

            for recipient in recipients:
                try:
                    # استبدال المتغيرات
                    final_subject = replace_template_variables(
                        subject,
                        subscription=recipient.get('subscription'),
                        customer=recipient.get('subscription').user if recipient.get('subscription') else None
                    )
                    final_body = replace_template_variables(
                        body,
                        subscription=recipient.get('subscription'),
                        customer=recipient.get('subscription').user if recipient.get('subscription') else None
                    )

                    # إنشاء PDF إذا كان مطلوباً
                    pdf_content = None
                    if attach_pdf and recipient.get('subscription'):
                        pdf_content = generate_pdf_report(recipient['subscription'])

                    # إرسال الرسالة
                    success, error_msg = send_email(
                        recipient['email'],
                        final_subject,
                        final_body,
                        attach_pdf,
                        pdf_content
                    )

                    # حفظ الرسالة في قاعدة البيانات
                    message = Message(
                        sender_id=current_user.id,
                        recipient_email=recipient['email'],
                        recipient_name=recipient['name'],
                        subject=final_subject,
                        body=final_body,
                        template_id=template_id if template_id else None,
                        subscription_id=recipient.get('subscription').id if recipient.get('subscription') else None,
                        status='sent' if success else 'failed',
                        sent_at=datetime.utcnow() if success else None,
                        error_message=error_msg if not success else None,
                        send_copy_to_sender=send_copy,
                        attach_pdf=attach_pdf
                    )
                    db.session.add(message)

                    if success:
                        sent_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    failed_count += 1
                    # حفظ الرسالة الفاشلة
                    message = Message(
                        sender_id=current_user.id,
                        recipient_email=recipient['email'],
                        recipient_name=recipient['name'],
                        subject=subject,
                        body=body,
                        status='failed',
                        error_message=str(e),
                        send_copy_to_sender=send_copy,
                        attach_pdf=attach_pdf
                    )
                    db.session.add(message)

            db.session.commit()

            if sent_count > 0:
                flash(f'تم إرسال {sent_count} رسالة بنجاح!', 'success')
            if failed_count > 0:
                flash(f'فشل في إرسال {failed_count} رسالة', 'error')

        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')

        return redirect(url_for('message_center_view'))

    # جلب البيانات للنموذج
    templates = MessageTemplate.query.filter_by(is_active=True).all()

    if current_user.is_admin():
        subscriptions = Subscription.query.filter_by(status='active').all()
    else:
        subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').all()

    return render_template_string(SEND_MESSAGE_TEMPLATE, templates=templates, subscriptions=subscriptions)

@app.route('/message_templates')
@login_required
def message_templates():
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    templates = MessageTemplate.query.all()
    return render_template_string(MESSAGE_TEMPLATES_TEMPLATE, templates=templates)

@app.route('/get_template/<int:template_id>')
@login_required
def get_template(template_id):
    template = db.session.get(MessageTemplate, template_id)
    if template:
        return jsonify({
            'subject': template.subject,
            'body': template.body
        })
    return jsonify({'error': 'Template not found'}), 404

# مسارات التقارير المتقدمة
@app.route('/subscription_analytics')
@login_required
def subscription_analytics():
    # تحليلات متقدمة للاشتراكات
    if current_user.is_admin():
        # إجمالي الإيرادات
        total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(payment_status='paid').scalar() or 0

        # متوسط قيمة الاشتراك
        avg_subscription_value = db.session.query(db.func.avg(Subscription.price)).scalar() or 0

        # معدل التجديد
        total_expired = Subscription.query.filter_by(status='expired').count()
        total_renewed = Subscription.query.filter(Subscription.status == 'active', Subscription.updated_at > Subscription.created_at).count()
        renewal_rate = (total_renewed / max(total_expired, 1)) * 100

        # توزيع الاشتراكات حسب المدة
        duration_stats = db.session.query(
            Subscription.subscription_type,
            db.func.count(Subscription.id).label('count'),
            db.func.sum(Subscription.price).label('revenue')
        ).group_by(Subscription.subscription_type).all()

        # أفضل المزودين
        top_providers = db.session.query(
            CloudProvider.name,
            db.func.count(Subscription.id).label('subscriptions'),
            db.func.sum(Subscription.price).label('revenue')
        ).join(Subscription).group_by(CloudProvider.name).order_by(db.func.sum(Subscription.price).desc()).limit(5).all()

    else:
        # إحصائيات المستخدم العادي
        user_subscriptions = Subscription.query.filter_by(user_id=current_user.id).all()
        total_revenue = sum(sub.price for sub in user_subscriptions)
        avg_subscription_value = total_revenue / max(len(user_subscriptions), 1)
        renewal_rate = 0  # يمكن حسابها لاحقاً
        duration_stats = []
        top_providers = []

    analytics_data = {
        'total_revenue': total_revenue,
        'avg_subscription_value': avg_subscription_value,
        'renewal_rate': renewal_rate,
        'duration_stats': duration_stats,
        'top_providers': top_providers
    }

    return render_template_string(SUBSCRIPTION_ANALYTICS_TEMPLATE, analytics=analytics_data)

@app.route('/subscription_reports')
@login_required
def subscription_reports():
    # تقارير الاشتراكات
    start_date = request.args.get('start_date', (date.today() - timedelta(days=30)).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))

    # تحويل التواريخ
    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

    if current_user.is_admin():
        subscriptions = Subscription.query.filter(
            Subscription.created_at >= start_date_obj,
            Subscription.created_at <= end_date_obj
        ).all()
    else:
        subscriptions = Subscription.query.filter(
            Subscription.user_id == current_user.id,
            Subscription.created_at >= start_date_obj,
            Subscription.created_at <= end_date_obj
        ).all()

    # إحصائيات التقرير
    total_subscriptions = len(subscriptions)
    total_value = sum(sub.price for sub in subscriptions)
    active_count = len([sub for sub in subscriptions if sub.status == 'active'])
    expired_count = len([sub for sub in subscriptions if sub.status == 'expired'])

    report_data = {
        'subscriptions': subscriptions,
        'total_subscriptions': total_subscriptions,
        'total_value': total_value,
        'active_count': active_count,
        'expired_count': expired_count,
        'start_date': start_date,
        'end_date': end_date
    }

    return render_template_string(SUBSCRIPTION_REPORTS_TEMPLATE, report=report_data)

@app.route('/invoice_reports')
@login_required
def invoice_reports():
    # تقارير الفواتير
    start_date = request.args.get('start_date', (date.today() - timedelta(days=30)).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))

    # تحويل التواريخ
    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

    if current_user.is_admin():
        invoices = Invoice.query.filter(
            Invoice.issue_date >= start_date_obj,
            Invoice.issue_date <= end_date_obj
        ).all()
    else:
        invoices = Invoice.query.filter(
            Invoice.user_id == current_user.id,
            Invoice.issue_date >= start_date_obj,
            Invoice.issue_date <= end_date_obj
        ).all()

    # إحصائيات التقرير
    total_invoices = len(invoices)
    total_amount = sum(inv.total_amount for inv in invoices)
    paid_amount = sum(inv.total_amount for inv in invoices if inv.payment_status == 'paid')
    pending_amount = total_amount - paid_amount

    report_data = {
        'invoices': invoices,
        'total_invoices': total_invoices,
        'total_amount': total_amount,
        'paid_amount': paid_amount,
        'pending_amount': pending_amount,
        'start_date': start_date,
        'end_date': end_date
    }

    return render_template_string(INVOICE_REPORTS_TEMPLATE, report=report_data)

# القوالب HTML
WELCOME_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بك - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            overflow-x: hidden;
            color: white;
            position: relative;
        }

        /* خلفية Matrix متحركة */
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
        }

        .matrix-char {
            position: absolute;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            font-weight: bold;
            animation: matrix-fall linear infinite;
            text-shadow: 0 0 5px currentColor;
        }

        .matrix-char:nth-child(odd) {
            color: #00f5ff;
            animation-duration: 3s;
        }

        .matrix-char:nth-child(even) {
            color: #bf00ff;
            animation-duration: 4s;
        }

        .matrix-char:nth-child(3n) {
            color: #ff0080;
            animation-duration: 2.5s;
        }

        @keyframes matrix-fall {
            0% {
                transform: translateY(-100vh) rotateX(0deg);
                opacity: 0;
                text-shadow: 0 0 5px currentColor;
            }
            10% {
                opacity: 1;
                text-shadow: 0 0 10px currentColor;
            }
            90% {
                opacity: 1;
                text-shadow: 0 0 15px currentColor;
            }
            100% {
                transform: translateY(100vh) rotateX(360deg);
                opacity: 0;
                text-shadow: 0 0 5px currentColor;
            }
        }

        /* تأثيرات إضافية للMatrix */
        .matrix-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 50% 50%, rgba(0, 245, 255, 0.05) 0%, transparent 70%);
            animation: matrix-pulse 4s ease-in-out infinite;
        }

        @keyframes matrix-pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.7; }
        }

        /* الحاوي الرئيسي */
        .welcome-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            position: relative;
        }

        /* العنوان الرئيسي */
        .welcome-title {
            font-size: 4rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #00f5ff, #bf00ff, #ff0080, #00f5ff);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradient-shift 3s ease-in-out infinite, glow-pulse 2s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
        }

        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes glow-pulse {
            0%, 100% { filter: drop-shadow(0 0 20px #00f5ff); }
            50% { filter: drop-shadow(0 0 40px #bf00ff); }
        }

        /* تأثير الكتابة التدريجية المحسن */
        .typewriter {
            overflow: hidden;
            border-left: 4px solid #00f5ff;
            white-space: nowrap;
            animation: typing 4s steps(50, end), blink-caret 1s step-end infinite;
            position: relative;
        }

        .typewriter::after {
            content: '';
            position: absolute;
            right: -4px;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(45deg, #00f5ff, #bf00ff);
            animation: caret-glow 1.5s ease-in-out infinite;
        }

        @keyframes typing {
            0% { width: 0; }
            90% { width: 100%; }
            100% { width: 100%; border-left: none; }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: #00f5ff; }
        }

        @keyframes caret-glow {
            0%, 100% {
                background: linear-gradient(45deg, #00f5ff, #bf00ff);
                box-shadow: 0 0 10px #00f5ff;
            }
            50% {
                background: linear-gradient(45deg, #bf00ff, #ff0080);
                box-shadow: 0 0 20px #bf00ff;
            }
        }

        /* العنوان الفرعي */
        .welcome-subtitle {
            font-size: 1.5rem;
            text-align: center;
            margin-bottom: 3rem;
            color: rgba(255, 255, 255, 0.8);
            animation: fade-in-up 1s ease-out 0.5s both;
        }

        @keyframes fade-in-up {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* بطاقات الميزات */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
            max-width: 1200px;
            width: 100%;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            animation: fade-in-up 1s ease-out both;
        }

        .feature-card:nth-child(1) { animation-delay: 0.7s; }
        .feature-card:nth-child(2) { animation-delay: 0.9s; }
        .feature-card:nth-child(3) { animation-delay: 1.1s; }
        .feature-card:nth-child(4) { animation-delay: 1.3s; }
        .feature-card:nth-child(5) { animation-delay: 1.5s; }
        .feature-card:nth-child(6) { animation-delay: 1.7s; }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.08) rotateY(5deg);
            box-shadow:
                0 25px 50px rgba(0, 245, 255, 0.4),
                0 0 30px rgba(191, 0, 255, 0.3),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            border-color: rgba(0, 245, 255, 0.8);
            background: rgba(255, 255, 255, 0.1);
        }

        .feature-card:hover::before {
            transform: scaleX(1);
            height: 4px;
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.8);
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.2) rotateY(360deg);
            filter: drop-shadow(0 0 25px #00f5ff) drop-shadow(0 0 35px #bf00ff);
        }

        .feature-card:hover .feature-title {
            color: #bf00ff;
            text-shadow: 0 0 15px #bf00ff;
        }

        .feature-card:hover .feature-description {
            color: rgba(255, 255, 255, 0.9);
        }

        /* تأثير النقر على البطاقات */
        .feature-card:active {
            transform: translateY(-5px) scale(1.02);
            transition: all 0.1s ease;
        }

        .feature-icon {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #00f5ff, #bf00ff, #ff0080);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: icon-glow 3s ease-in-out infinite, gradient-shift 4s ease-in-out infinite;
            transition: all 0.3s ease;
            position: relative;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(0, 245, 255, 0.2) 0%, transparent 70%);
            z-index: -1;
            animation: icon-halo 2s ease-in-out infinite;
        }

        @keyframes icon-glow {
            0%, 100% {
                filter: drop-shadow(0 0 15px #00f5ff) drop-shadow(0 0 25px #00f5ff);
            }
            33% {
                filter: drop-shadow(0 0 20px #bf00ff) drop-shadow(0 0 30px #bf00ff);
            }
            66% {
                filter: drop-shadow(0 0 18px #ff0080) drop-shadow(0 0 28px #ff0080);
            }
        }

        @keyframes icon-halo {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.3;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.2);
                opacity: 0.6;
            }
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #00f5ff;
        }

        .feature-description {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
        }

        /* زر تسجيل الدخول المتطور */
        .btn-welcome {
            background: linear-gradient(135deg, #00f5ff, #bf00ff, #ff0080);
            background-size: 200% 200%;
            border: none;
            border-radius: 50px;
            padding: 1.2rem 3.5rem;
            font-size: 1.3rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
            box-shadow:
                0 15px 35px rgba(0, 245, 255, 0.4),
                0 0 20px rgba(191, 0, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            animation: fade-in-up 1s ease-out 1.9s both, btn-glow 3s ease-in-out infinite;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .btn-welcome::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .btn-welcome::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00f5ff, #bf00ff, #ff0080, #00f5ff);
            background-size: 400% 400%;
            border-radius: 52px;
            z-index: -1;
            animation: border-glow 2s ease-in-out infinite;
        }

        @keyframes btn-glow {
            0%, 100% {
                background-position: 0% 50%;
                box-shadow:
                    0 15px 35px rgba(0, 245, 255, 0.4),
                    0 0 20px rgba(191, 0, 255, 0.3);
            }
            50% {
                background-position: 100% 50%;
                box-shadow:
                    0 20px 45px rgba(191, 0, 255, 0.5),
                    0 0 30px rgba(255, 0, 128, 0.4);
            }
        }

        @keyframes border-glow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .btn-welcome:hover {
            transform: translateY(-5px) scale(1.08);
            box-shadow:
                0 25px 50px rgba(0, 245, 255, 0.6),
                0 0 40px rgba(191, 0, 255, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            color: white;
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
        }

        .btn-welcome:hover::before {
            left: 100%;
        }

        .btn-welcome:active {
            transform: translateY(-2px) scale(1.02);
            transition: all 0.1s ease;
        }

        /* تجاوب الموبايل */
        @media (max-width: 768px) {
            .welcome-title {
                font-size: 2.5rem;
            }

            .welcome-subtitle {
                font-size: 1.2rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .feature-card {
                padding: 1.5rem;
            }

            .btn-welcome {
                padding: 0.8rem 2rem;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- خلفية Matrix -->
    <div class="matrix-bg" id="matrix"></div>

    <div class="welcome-container">
        <h1 class="welcome-title typewriter">
            <i class="fas fa-rocket me-3"></i>
            نظام إدارة الاشتراكات المتقدم
        </h1>

        <p class="welcome-subtitle">
            🚀 تجربة متطورة مع تقنيات الذكاء الاصطناعي والتحليلات المتقدمة
        </p>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3 class="feature-title">ذكاء اصطناعي متقدم</h3>
                <p class="feature-description">تحليلات ذكية ورؤى متطورة لاتخاذ قرارات أفضل</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="feature-title">تحليلات متقدمة</h3>
                <p class="feature-description">مخططات تفاعلية وتقارير شاملة في الوقت الفعلي</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <h3 class="feature-title">إشعارات ذكية</h3>
                <p class="feature-description">نظام تنبيهات متطور مع تصنيف وإدارة متقدمة</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <h3 class="feature-title">تقويم تفاعلي</h3>
                <p class="feature-description">إدارة الأحداث والمواعيد مع تقويم ملون ومنظم</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="feature-title">أمان متطور</h3>
                <p class="feature-description">حماية عالية مع تشفير متقدم ونسخ احتياطي تلقائي</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-cloud"></i>
                </div>
                <h3 class="feature-title">تخزين سحابي</h3>
                <p class="feature-description">إدارة ملفات متقدمة مع تخزين آمن ومتزامن</p>
            </div>
        </div>

        <a href="{{ url_for('login') }}" class="btn-welcome">
            <i class="fas fa-sign-in-alt"></i>
            دخول النظام المتقدم
        </a>
    </div>

    <script>
        // إنشاء تأثير Matrix متطور في الخلفية
        function createMatrix() {
            const matrix = document.getElementById('matrix');
            const chars = '01أبتثجحخدذرزسشصضطظعغفقكلمنهويABCDEFGHIJKLMNOPQRSTUVWXYZ';
            const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
            const numbers = '0123456789';

            // إنشاء 80 حرف للحصول على تأثير أكثر كثافة
            for (let i = 0; i < 80; i++) {
                const char = document.createElement('div');
                char.className = 'matrix-char';

                // اختيار نوع الحرف بشكل عشوائي
                let charSet;
                const rand = Math.random();
                if (rand < 0.4) charSet = chars;
                else if (rand < 0.7) charSet = numbers;
                else charSet = symbols;

                char.textContent = charSet[Math.floor(Math.random() * charSet.length)];
                char.style.left = Math.random() * 100 + '%';
                char.style.fontSize = (Math.random() * 8 + 12) + 'px';
                char.style.animationDuration = (Math.random() * 4 + 2) + 's';
                char.style.animationDelay = Math.random() * 3 + 's';

                // إضافة تأثيرات عشوائية
                if (Math.random() < 0.3) {
                    char.style.fontWeight = 'bold';
                }

                matrix.appendChild(char);
            }
        }

        // إنشاء تأثيرات إضافية للخلفية
        function createBackgroundEffects() {
            const container = document.querySelector('.welcome-container');

            // إضافة جسيمات متحركة
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'absolute';
                particle.style.width = Math.random() * 4 + 2 + 'px';
                particle.style.height = particle.style.width;
                particle.style.background = `rgba(${Math.random() < 0.5 ? '0, 245, 255' : '191, 0, 255'}, 0.6)`;
                particle.style.borderRadius = '50%';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animation = `float ${Math.random() * 6 + 4}s ease-in-out infinite`;
                particle.style.animationDelay = Math.random() * 2 + 's';
                particle.style.boxShadow = `0 0 10px currentColor`;
                particle.style.zIndex = '-1';

                document.body.appendChild(particle);
            }
        }

        // تأثيرات تفاعلية للبطاقات
        function addCardInteractions() {
            const cards = document.querySelectorAll('.feature-card');

            cards.forEach((card, index) => {
                // تأثير المتابعة للماوس
                card.addEventListener('mousemove', function(e) {
                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;

                    const rotateX = (y - centerY) / 10;
                    const rotateY = (centerX - x) / 10;

                    card.style.transform = `translateY(-15px) scale(1.08) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
                });

                card.addEventListener('mouseleave', function() {
                    card.style.transform = 'translateY(0) scale(1) rotateX(0deg) rotateY(0deg)';
                });

                // تأثير النقر
                card.addEventListener('click', function() {
                    card.style.transform = 'translateY(-5px) scale(1.02)';
                    setTimeout(() => {
                        card.style.transform = 'translateY(-15px) scale(1.08)';
                    }, 150);
                });
            });
        }

        // تشغيل جميع التأثيرات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            createMatrix();
            createBackgroundEffects();
            addCardInteractions();

            // إعادة إنشاء Matrix كل 6 ثوان
            setInterval(() => {
                const matrix = document.getElementById('matrix');
                matrix.innerHTML = '';
                createMatrix();
            }, 6000);

            // تأثير تحريك الخلفية مع الماوس
            document.addEventListener('mousemove', function(e) {
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;

                const matrix = document.getElementById('matrix');
                matrix.style.transform = `translate(${x * 20 - 10}px, ${y * 20 - 10}px)`;
            });
        });
    </script>
</body>
</html>
'''

LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* خلفية متحركة */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
        }

        .floating-shape {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, #00f5ff, #bf00ff);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* حاوي تسجيل الدخول */
        .login-container {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(30px);
            border-radius: 25px;
            padding: 3rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 450px;
            width: 100%;
            position: relative;
            overflow: hidden;
            animation: container-glow 3s ease-in-out infinite;
        }

        @keyframes container-glow {
            0%, 100% { box-shadow: 0 25px 50px rgba(0, 245, 255, 0.2); }
            50% { box-shadow: 0 25px 50px rgba(191, 0, 255, 0.2); }
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
            animation: border-glow 2s ease-in-out infinite;
        }

        @keyframes border-glow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        /* العنوان مع الشعار */
        .login-title {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
        }

        .logo-container {
            margin-bottom: 1rem;
            animation: logo-pulse 2s ease-in-out infinite;
        }

        @keyframes logo-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .logo-icon {
            font-size: 4.5rem;
            background: linear-gradient(135deg, #00f5ff, #bf00ff, #ff0080);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 0 25px #00f5ff) drop-shadow(0 0 35px #bf00ff);
            animation: icon-rotate 6s linear infinite, gradient-shift 3s ease-in-out infinite, icon-pulse 2s ease-in-out infinite;
            position: relative;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(0, 245, 255, 0.2) 0%, rgba(191, 0, 255, 0.1) 50%, transparent 70%);
            z-index: -1;
            animation: logo-halo 3s ease-in-out infinite;
        }

        @keyframes icon-rotate {
            0% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(1.1); }
            50% { transform: rotate(180deg) scale(1); }
            75% { transform: rotate(270deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }

        @keyframes icon-pulse {
            0%, 100% {
                filter: drop-shadow(0 0 25px #00f5ff) drop-shadow(0 0 35px #bf00ff);
            }
            50% {
                filter: drop-shadow(0 0 35px #bf00ff) drop-shadow(0 0 45px #ff0080);
            }
        }

        @keyframes logo-halo {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1) rotate(0deg);
                opacity: 0.4;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.3) rotate(180deg);
                opacity: 0.7;
            }
        }

        .title-text {
            color: white;
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(45deg, #00f5ff, #bf00ff, #ff0080);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradient-shift 3s ease-in-out infinite;
        }

        .subtitle-text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        /* حقول الإدخال المتطورة */
        .form-group {
            position: relative;
            margin-bottom: 2rem;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            color: white;
            padding: 15px 20px;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00f5ff;
            box-shadow:
                0 0 30px rgba(0, 245, 255, 0.5),
                0 0 60px rgba(0, 245, 255, 0.3),
                inset 0 0 20px rgba(0, 245, 255, 0.1);
            color: white;
            outline: none;
            transform: translateY(-3px) scale(1.02);
            border-width: 3px;
        }

        .form-control:focus::placeholder {
            color: rgba(0, 245, 255, 0.8);
            transform: translateY(-2px);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }

        .form-control:focus::placeholder {
            color: rgba(0, 245, 255, 0.7);
        }

        /* أيقونات الحقول المتطورة */
        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
            z-index: 2;
            font-size: 1.1rem;
        }

        .form-control:focus + .input-icon {
            color: #00f5ff;
            filter: drop-shadow(0 0 10px #00f5ff) drop-shadow(0 0 20px #00f5ff);
            transform: translateY(-50%) scale(1.2);
            animation: icon-glow-pulse 2s ease-in-out infinite;
        }

        @keyframes icon-glow-pulse {
            0%, 100% {
                filter: drop-shadow(0 0 10px #00f5ff) drop-shadow(0 0 20px #00f5ff);
            }
            50% {
                filter: drop-shadow(0 0 15px #bf00ff) drop-shadow(0 0 25px #bf00ff);
            }
        }

        /* زر إظهار/إخفاء كلمة المرور */
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 2;
        }

        .password-toggle:hover {
            color: #00f5ff;
            filter: drop-shadow(0 0 5px #00f5ff);
        }

        /* زر تسجيل الدخول المتطور */
        .login-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #00f5ff, #bf00ff, #ff0080);
            background-size: 200% 200%;
            border: none;
            border-radius: 20px;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            margin-top: 1.5rem;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            animation: btn-glow 3s ease-in-out infinite;
            box-shadow:
                0 10px 30px rgba(0, 245, 255, 0.4),
                0 0 20px rgba(191, 0, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .login-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow:
                0 20px 45px rgba(0, 245, 255, 0.6),
                0 0 40px rgba(191, 0, 255, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            background-position: 100% 50%;
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:active {
            transform: translateY(-2px) scale(1.02);
            transition: all 0.1s ease;
        }

        .login-btn::after {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg, #00f5ff, #bf00ff, #ff0080, #00f5ff);
            background-size: 400% 400%;
            border-radius: 23px;
            z-index: -1;
            animation: border-glow 2s ease-in-out infinite;
        }

        /* معلومات النظام */
        .system-info {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .system-info h6 {
            color: #00f5ff;
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .system-info p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.85rem;
            margin-bottom: 0.25rem;
        }

        /* رسائل التنبيه */
        .alert {
            border-radius: 15px;
            border: none;
            margin-bottom: 1.5rem;
            animation: alert-slide 0.5s ease-out;
        }

        @keyframes alert-slide {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(255, 0, 128, 0.2), rgba(255, 0, 128, 0.1));
            color: #ff0080;
            border: 1px solid rgba(255, 0, 128, 0.3);
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(0, 245, 255, 0.1));
            color: #00f5ff;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }

        /* تجاوب الموبايل */
        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }

            .logo-icon {
                font-size: 3rem;
            }

            .title-text {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="animated-bg" id="animatedBg"></div>

    <div class="login-container">
        <div class="login-title">
            <div class="logo-container">
                <i class="fas fa-rocket logo-icon"></i>
            </div>
            <h1 class="title-text">نظام إدارة الاشتراكات المتقدم</h1>
            <p class="subtitle-text">🚀 تسجيل الدخول للنظام المتطور</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" id="loginForm">
            <div class="form-group">
                <input type="text" name="username" class="form-control" placeholder="اسم المستخدم" required>
                <i class="fas fa-user input-icon"></i>
            </div>

            <div class="form-group">
                <input type="password" name="password" id="password" class="form-control" placeholder="كلمة المرور" required>
                <i class="fas fa-lock input-icon"></i>
                <button type="button" class="password-toggle" onclick="togglePassword()">
                    <i class="fas fa-eye" id="toggleIcon"></i>
                </button>
            </div>

            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt me-2"></i>
                دخول النظام المتقدم
            </button>
        </form>

        <div class="system-info">
            <h6>🌟 النظام المتقدم مع الذكاء الاصطناعي</h6>
            <p>💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️</p>
            <p>🏢 شركة AdenLink - العراق 🇮🇶</p>
            <p>🚀 تقنيات متطورة وحلول ذكية</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // إنشاء الأشكال المتحركة المتطورة في الخلفية
        function createFloatingShapes() {
            const bg = document.getElementById('animatedBg');

            // إنشاء أشكال متنوعة
            for (let i = 0; i < 25; i++) {
                const shape = document.createElement('div');
                shape.className = 'floating-shape';

                const size = Math.random() * 120 + 30;
                shape.style.width = size + 'px';
                shape.style.height = size + 'px';
                shape.style.left = Math.random() * 100 + '%';
                shape.style.top = Math.random() * 100 + '%';
                shape.style.animationDelay = Math.random() * 8 + 's';
                shape.style.animationDuration = (Math.random() * 6 + 6) + 's';

                // ألوان متنوعة
                const colors = ['#00f5ff', '#bf00ff', '#ff0080'];
                const color = colors[Math.floor(Math.random() * colors.length)];
                shape.style.background = `radial-gradient(circle, ${color}40, transparent)`;
                shape.style.boxShadow = `0 0 20px ${color}60`;

                // أشكال مختلفة
                if (Math.random() < 0.3) {
                    shape.style.borderRadius = '0';
                    shape.style.transform = 'rotate(45deg)';
                } else if (Math.random() < 0.6) {
                    shape.style.borderRadius = '50%';
                } else {
                    shape.style.borderRadius = '20px';
                }

                bg.appendChild(shape);
            }

            // إضافة جسيمات صغيرة
            for (let i = 0; i < 40; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'absolute';
                particle.style.width = Math.random() * 6 + 2 + 'px';
                particle.style.height = particle.style.width;
                particle.style.background = '#00f5ff';
                particle.style.borderRadius = '50%';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animation = `float ${Math.random() * 8 + 4}s ease-in-out infinite`;
                particle.style.animationDelay = Math.random() * 3 + 's';
                particle.style.boxShadow = '0 0 10px #00f5ff';
                particle.style.opacity = '0.7';

                bg.appendChild(particle);
            }
        }

        // تبديل إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // تأثيرات التحميل
        document.addEventListener('DOMContentLoaded', function() {
            createFloatingShapes();

            // تأثير الظهور التدريجي للحاوي
            const container = document.querySelector('.login-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';

            setTimeout(() => {
                container.style.transition = 'all 0.8s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);

            // تأثيرات التركيز المتطورة على الحقول
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.03) translateY(-2px)';
                    this.parentElement.style.filter = 'drop-shadow(0 10px 20px rgba(0, 245, 255, 0.3))';

                    // تأثير الموجة
                    const wave = document.createElement('div');
                    wave.style.position = 'absolute';
                    wave.style.top = '0';
                    wave.style.left = '0';
                    wave.style.width = '100%';
                    wave.style.height = '100%';
                    wave.style.background = 'radial-gradient(circle, rgba(0, 245, 255, 0.2) 0%, transparent 70%)';
                    wave.style.borderRadius = '15px';
                    wave.style.animation = 'wave-expand 0.6s ease-out';
                    wave.style.pointerEvents = 'none';
                    wave.style.zIndex = '-1';

                    this.parentElement.appendChild(wave);

                    setTimeout(() => {
                        if (wave.parentElement) {
                            wave.parentElement.removeChild(wave);
                        }
                    }, 600);
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1) translateY(0)';
                    this.parentElement.style.filter = 'none';
                });

                // تأثير الكتابة
                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        this.style.color = '#00f5ff';
                        this.style.textShadow = '0 0 5px #00f5ff';
                    } else {
                        this.style.color = 'white';
                        this.style.textShadow = 'none';
                    }
                });
            });

            // إضافة CSS للموجة
            const style = document.createElement('style');
            style.textContent = `
                @keyframes wave-expand {
                    0% { transform: scale(0); opacity: 1; }
                    100% { transform: scale(1.2); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        /* شريط التنقل البسيط */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            color: #667eea !important;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            color: #764ba2 !important;
            transform: scale(1.05);
        }

        .nav-link {
            color: #333 !important;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0 0.5rem;
        }

        .nav-link:hover {
            color: #667eea !important;
        }

        /* القوائم المنسدلة */
        .dropdown-menu {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 0.5rem 0;
        }

        .dropdown-item {
            color: #333;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 0 0.5rem;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateX(5px);
        }

        .dropdown-item i {
            width: 20px;
            margin-left: 10px;
        }

        /* المحتوى الرئيسي */
        .main-content {
            padding: 2rem;
            position: relative;
        }

        /* رأس لوحة التحكم البسيط */
        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .dashboard-title {
            color: #667eea;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .dashboard-subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        /* الأزرار السريعة البسيطة */
        .quick-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .quick-action-btn {
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        /* بطاقات الإحصائيات البسيطة */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        /* أقسام المحتوى البسيطة */
        .content-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .section-title {
            color: #667eea;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .subscription-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid #667eea;
        }

        .subscription-name {
            font-weight: 600;
            color: #333;
        }

        .subscription-details {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.25rem;
        }

        /* الأزرار البسيطة */
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            background: transparent;
            border-radius: 25px;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        /* التنبيهات البسيطة */
        .alert {
            border-radius: 10px;
            border: none;
        }

        .alert-warning {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            color: #2d3436;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
        }

        .alert-success {
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
        }

        /* تجاوب الموبايل */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }

            .dashboard-header {
                padding: 1.5rem;
            }

            .dashboard-title {
                font-size: 2rem;
            }

            .stats-container {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .quick-actions {
                flex-direction: column;
                align-items: center;
            }

            .quick-action-btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
        .quick-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        .quick-action-btn {
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }
        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }


    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-cloud me-2"></i>
                نظام إدارة الاشتراكات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-subscription"></i> إدارة الاشتراكات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('subscription_charts') }}">
                                <i class="fas fa-chart-pie"></i> مخطط الاشتراكات التفاعلي
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('subscriptions') }}">
                                <i class="fas fa-list"></i> قائمة الاشتراكات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_subscription') }}">
                                <i class="fas fa-plus"></i> إضافة اشتراك جديد
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('subscription_analytics') }}">
                                <i class="fas fa-chart-line"></i> تحليلات الاشتراكات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('subscription_reports') }}">
                                <i class="fas fa-file-alt"></i> تقارير الاشتراكات
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-file-invoice"></i> إدارة الفواتير
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('invoices') }}">
                                <i class="fas fa-list"></i> قائمة الفواتير
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('create_invoice') }}">
                                <i class="fas fa-plus"></i> إنشاء فاتورة جديدة
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('invoice_reports') }}">
                                <i class="fas fa-chart-bar"></i> تقارير الفواتير
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-envelope"></i> مركز التواصل
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('message_center_view') }}">
                                <i class="fas fa-inbox"></i> مركز الرسائل
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('send_message') }}">
                                <i class="fas fa-paper-plane"></i> إرسال رسالة
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('message_templates') }}">
                                <i class="fas fa-file-text"></i> قوالب الرسائل
                            </a></li>
                        </ul>
                    </li>

                    {% if recent_notifications %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('notifications') }}">
                            <i class="fas fa-bell"></i>
                            <span class="badge bg-danger">{{ recent_notifications|length }}</span>
                        </a>
                    </li>
                    {% endif %}

                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> خروج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="dashboard-header">
            <h1 class="dashboard-title">
                <i class="fas fa-tachometer-alt me-3"></i>
                لوحة التحكم
            </h1>
            <p class="dashboard-subtitle">مرحباً {{ current_user.full_name }}، إليك نظرة عامة على نظامك</p>

            <div class="quick-actions">
                <a href="{{ url_for('subscription_charts') }}" class="quick-action-btn">
                    <i class="fas fa-chart-pie"></i>
                    مخطط الاشتراكات
                </a>
                <a href="{{ url_for('add_subscription') }}" class="quick-action-btn">
                    <i class="fas fa-plus"></i>
                    إضافة اشتراك
                </a>
                <a href="{{ url_for('send_message') }}" class="quick-action-btn">
                    <i class="fas fa-paper-plane"></i>
                    إرسال رسالة
                </a>
                <a href="{{ url_for('invoice_reports') }}" class="quick-action-btn">
                    <i class="fas fa-file-invoice"></i>
                    تقارير الفواتير
                </a>
            </div>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-icon" style="color: #667eea;">
                    <i class="fas fa-subscription"></i>
                </div>
                <div class="stat-number" style="color: #667eea;">{{ stats.total_subscriptions }}</div>
                <div class="stat-label">إجمالي الاشتراكات</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="color: #00b894;">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number" style="color: #00b894;">{{ stats.active_subscriptions }}</div>
                <div class="stat-label">اشتراكات نشطة</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="color: #fdcb6e;">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-number" style="color: #fdcb6e;">${{ "%.0f"|format(stats.total_revenue) }}</div>
                <div class="stat-label">إجمالي الإيرادات</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="color: #e17055;">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number" style="color: #e17055;">{{ stats.expiring_soon }}</div>
                <div class="stat-label">تنتهي قريباً</div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="content-section">
                    <h3 class="section-title">
                        <i class="fas fa-clock me-2"></i>
                        الاشتراكات المنتهية قريباً
                    </h3>
                    {% if expiring_subscriptions %}
                        {% for subscription in expiring_subscriptions %}
                            <div class="subscription-item">
                                <div class="subscription-name">{{ subscription.name }}</div>
                                <div class="subscription-details">
                                    ينتهي في {{ subscription.days_until_expiry() }} يوم - {{ subscription.end_date.strftime('%Y-%m-%d') }}
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">لا توجد اشتراكات تنتهي قريباً</p>
                    {% endif %}
                </div>
            </div>

            <div class="col-md-6">
                <div class="content-section">
                    <h3 class="section-title">
                        <i class="fas fa-chart-line me-2"></i>
                        إجراءات سريعة
                    </h3>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('subscriptions') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة اشتراك جديد
                        </a>
                        <a href="{{ url_for('subscriptions') }}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>
                            عرض جميع الاشتراكات
                        </a>
                        <a href="{{ url_for('invoice_reports') }}" class="btn btn-outline-primary">
                            <i class="fas fa-file-invoice me-2"></i>
                            إدارة الفواتير
                        </a>
                        <a href="{{ url_for('subscription_analytics') }}" class="btn btn-outline-primary">
                            <i class="fas fa-server me-2"></i>
                            إدارة المزودين
                        </a>
                    </div>
                </div>
            </div>
        </div>

        {% if ai_insights %}
        <div class="content-section">
            <h3 class="section-title">
                <i class="fas fa-brain me-2"></i>
                الرؤى الذكية
            </h3>
            <div class="row">
                {% for insight in ai_insights %}
                <div class="col-md-6 mb-3">
                    <div class="alert alert-{{ insight.type }}">
                        <h6><i class="fas fa-lightbulb me-2"></i>{{ insight.title }}</h6>
                        <p class="mb-2">{{ insight.message }}</p>
                        <small>{{ insight.action }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        {% if critical_subscriptions %}
        <div class="content-section">
            <h3 class="section-title" style="color: #e17055;">
                <i class="fas fa-exclamation-triangle me-2"></i>
                خوادم تحتاج انتباه
            </h3>
            {% for server in critical_subscriptions %}
            <div class="alert alert-warning">
                <h6>{{ server.name }}</h6>
                <div class="row">
                    <div class="col-md-4">المعالج: {{ "%.1f"|format(server.cpu_usage) }}%</div>
                    <div class="col-md-4">الذاكرة: {{ "%.1f"|format(server.memory_usage) }}%</div>
                    <div class="col-md-4">القرص: {{ "%.1f"|format(server.disk_usage) }}%</div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تأثيرات بسيطة للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');

            // ظهور تدريجي للبطاقات
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // ظهور تدريجي للأقسام
            const sections = document.querySelectorAll('.content-section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    section.style.transition = 'all 0.5s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, (index + cards.length) * 100);
            });
        });
    </script>
</body>
</html>


'''

# قالب مركز الرسائل
MESSAGE_CENTER_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز الرسائل - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 20px;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 30px;
            text-align: center;
        }

        .messages-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
        }

        .message-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .message-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .message-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .message-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-sent {
            background: rgba(0, 255, 0, 0.2);
            color: #4ade80;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .status-pending {
            background: rgba(255, 165, 0, 0.2);
            color: #ffa500;
            border: 1px solid rgba(255, 165, 0, 0.3);
        }

        .status-failed {
            background: rgba(255, 0, 0, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 0, 0, 0.3);
        }

        .message-subject {
            font-size: 1.2rem;
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 10px;
        }

        .message-recipient {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
        }

        .message-date {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }

        .message-body {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-top: 15px;
            max-height: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .action-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 10px 10px 0 0;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 100px 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            border: 2px dashed rgba(255, 255, 255, 0.2);
        }

        .empty-icon {
            font-size: 5rem;
            color: rgba(255, 255, 255, 0.3);
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1 class="page-title">
        <i class="fas fa-inbox me-3"></i>
        مركز الرسائل
    </h1>

    <div style="text-align: center; margin-bottom: 30px;">
        <a href="{{ url_for('send_message') }}" class="action-btn">
            <i class="fas fa-paper-plane"></i>
            إرسال رسالة جديدة
        </a>
        <a href="{{ url_for('dashboard') }}" class="action-btn">
            <i class="fas fa-tachometer-alt"></i>
            لوحة التحكم
        </a>
    </div>

    {% if messages %}
        <div class="messages-container">
            {% for message in messages %}
            <div class="message-card">
                <div class="message-header">
                    <div class="message-status status-{{ message.status }}">
                        {% if message.status == 'sent' %}
                            <i class="fas fa-check-circle me-1"></i>مرسل
                        {% elif message.status == 'failed' %}
                            <i class="fas fa-times-circle me-1"></i>فشل
                        {% else %}
                            <i class="fas fa-clock me-1"></i>معلق
                        {% endif %}
                    </div>
                    <div class="message-date">
                        {{ message.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                </div>

                <div class="message-subject">{{ message.subject }}</div>
                <div class="message-recipient">
                    <i class="fas fa-envelope me-1"></i>
                    إلى: {{ message.recipient_email }}
                </div>

                {% if message.related_subscription %}
                <div class="message-recipient">
                    <i class="fas fa-server me-1"></i>
                    الاشتراك: {{ message.related_subscription.name }}
                </div>
                {% endif %}

                <div class="message-body">
                    {{ message.body[:200] }}{% if message.body|length > 200 %}...{% endif %}
                </div>

                {% if message.error_message %}
                <div style="color: #ff6b6b; margin-top: 10px; font-size: 0.9rem;">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    {{ message.error_message }}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-inbox"></i>
            </div>
            <h3 style="color: rgba(255, 255, 255, 0.7); margin-bottom: 15px;">
                لا توجد رسائل حالياً
            </h3>
            <p style="color: rgba(255, 255, 255, 0.5); margin-bottom: 25px;">
                ابدأ بإرسال رسالتك الأولى للعملاء
            </p>
            <a href="{{ url_for('send_message') }}" class="action-btn">
                <i class="fas fa-paper-plane me-2"></i>
                إرسال رسالة جديدة
            </a>
        </div>
    {% endif %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي للبطاقات
        const cards = document.querySelectorAll('.message-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
    </script>
</body>
</html>
'''

# قالب إرسال الرسائل
SEND_MESSAGE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إرسال رسالة - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 20px;
        }

        .form-container {
            max-width: 900px;
            margin: 0 auto;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .form-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 30px;
            text-align: center;
        }

        .form-section {
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-title {
            color: #bf00ff;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #bf00ff;
        }

        .form-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00f5ff;
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
            color: white;
            outline: none;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-select option {
            background: #1a1a2e;
            color: white;
        }

        .form-check {
            margin-bottom: 15px;
        }

        .form-check-input {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .form-check-input:checked {
            background: #00f5ff;
            border-color: #00f5ff;
        }

        .form-check-label {
            color: rgba(255, 255, 255, 0.8);
            margin-right: 10px;
        }

        .recipient-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .recipient-option {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .recipient-option:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: #00f5ff;
        }

        .recipient-option.selected {
            background: rgba(0, 245, 255, 0.2);
            border-color: #00f5ff;
        }

        .recipient-option input[type="radio"] {
            display: none;
        }

        .template-preview {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
            display: none;
        }

        .template-preview.active {
            display: block;
        }

        .preview-subject {
            color: #00f5ff;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .preview-body {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        .variables-help {
            background: rgba(191, 0, 255, 0.1);
            border: 1px solid rgba(191, 0, 255, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .variables-title {
            color: #bf00ff;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .variable-item {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
            font-family: 'Courier New', monospace;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .cancel-btn {
            width: 100%;
            padding: 15px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: transparent;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-top: 10px;
        }

        .cancel-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <div class="form-card">
            <h1 class="form-title">
                <i class="fas fa-paper-plane me-3"></i>
                إرسال رسالة إلكترونية
            </h1>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show">
                            <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" id="messageForm">
                <!-- اختيار المستلمين -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-users me-2"></i>
                        اختيار المستلمين
                    </h3>

                    <div class="recipient-options">
                        <div class="recipient-option" onclick="selectRecipient('custom')">
                            <input type="radio" name="recipient_type" value="custom" id="custom">
                            <i class="fas fa-user" style="font-size: 1.5rem; margin-bottom: 10px; color: #00f5ff;"></i>
                            <div>إيميل مخصص</div>
                        </div>

                        <div class="recipient-option" onclick="selectRecipient('subscription')">
                            <input type="radio" name="recipient_type" value="subscription" id="subscription">
                            <i class="fas fa-server" style="font-size: 1.5rem; margin-bottom: 10px; color: #00f5ff;"></i>
                            <div>عميل اشتراك محدد</div>
                        </div>

                        {% if current_user.is_admin() %}
                        <div class="recipient-option" onclick="selectRecipient('all_active')">
                            <input type="radio" name="recipient_type" value="all_active" id="all_active">
                            <i class="fas fa-users" style="font-size: 1.5rem; margin-bottom: 10px; color: #00f5ff;"></i>
                            <div>جميع العملاء النشطين</div>
                        </div>

                        <div class="recipient-option" onclick="selectRecipient('expiring_soon')">
                            <input type="radio" name="recipient_type" value="expiring_soon" id="expiring_soon">
                            <i class="fas fa-clock" style="font-size: 1.5rem; margin-bottom: 10px; color: #ffa500;"></i>
                            <div>المنتهية قريباً</div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- حقل الإيميل المخصص -->
                    <div id="customEmailField" style="display: none;">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="custom_email" class="form-control" placeholder="<EMAIL>">
                    </div>

                    <!-- اختيار الاشتراك -->
                    <div id="subscriptionField" style="display: none;">
                        <label class="form-label">اختيار الاشتراك</label>
                        <select name="subscription_id" class="form-select">
                            <option value="">اختر الاشتراك</option>
                            {% for subscription in subscriptions %}
                            <option value="{{ subscription.id }}">{{ subscription.name }} - {{ subscription.user.full_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- محتوى الرسالة -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-envelope me-2"></i>
                        محتوى الرسالة
                    </h3>

                    <div class="mb-3">
                        <label class="form-label">استخدام قالب جاهز (اختياري)</label>
                        <select name="template_id" class="form-select" onchange="loadTemplate(this.value)">
                            <option value="">رسالة مخصصة</option>
                            {% for template in templates %}
                            <option value="{{ template.id }}">{{ template.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">موضوع الرسالة</label>
                        <input type="text" name="custom_subject" class="form-control" placeholder="موضوع الرسالة" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">نص الرسالة</label>
                        <textarea name="custom_body" class="form-control" rows="8" placeholder="اكتب رسالتك هنا..." required></textarea>
                    </div>

                    <!-- معاينة القالب -->
                    <div id="templatePreview" class="template-preview">
                        <div class="preview-subject" id="previewSubject"></div>
                        <div class="preview-body" id="previewBody"></div>
                    </div>

                    <!-- مساعدة المتغيرات -->
                    <div class="variables-help">
                        <div class="variables-title">
                            <i class="fas fa-info-circle me-2"></i>
                            المتغيرات المتاحة:
                        </div>
                        <div class="variable-item">{customer_name} - اسم العميل</div>
                        <div class="variable-item">{subscription_name} - اسم الاشتراك</div>
                        <div class="variable-item">{cloud_name} - اسم مزود الخدمة</div>
                        <div class="variable-item">{price} - السعر</div>
                        <div class="variable-item">{currency} - العملة</div>
                        <div class="variable-item">{end_date} - تاريخ الانتهاء</div>
                        <div class="variable-item">{server_ip} - عنوان الخادم</div>
                    </div>
                </div>

                <!-- خيارات متقدمة -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-cogs me-2"></i>
                        خيارات متقدمة
                    </h3>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="send_copy" id="send_copy">
                        <label class="form-check-label" for="send_copy">
                            إرسال نسخة للمرسل
                        </label>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="attach_pdf" id="attach_pdf">
                        <label class="form-check-label" for="attach_pdf">
                            إرفاق PDF بتفاصيل الاشتراك
                        </label>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="row">
                    <div class="col-md-6">
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-paper-plane me-2"></i>
                            إرسال الرسالة
                        </button>
                    </div>
                    <div class="col-md-6">
                        <a href="{{ url_for('message_center_view') }}" class="cancel-btn">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // اختيار نوع المستلم
    function selectRecipient(type) {
        // إزالة التحديد من جميع الخيارات
        document.querySelectorAll('.recipient-option').forEach(option => {
            option.classList.remove('selected');
        });

        // تحديد الخيار المختار
        event.currentTarget.classList.add('selected');
        document.getElementById(type).checked = true;

        // إخفاء جميع الحقول
        document.getElementById('customEmailField').style.display = 'none';
        document.getElementById('subscriptionField').style.display = 'none';

        // إظهار الحقل المناسب
        if (type === 'custom') {
            document.getElementById('customEmailField').style.display = 'block';
        } else if (type === 'subscription') {
            document.getElementById('subscriptionField').style.display = 'block';
        }
    }

    // تحميل القالب
    function loadTemplate(templateId) {
        if (templateId) {
            fetch(`/get_template/${templateId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.subject && data.body) {
                        document.querySelector('input[name="custom_subject"]').value = data.subject;
                        document.querySelector('textarea[name="custom_body"]').value = data.body;

                        // إظهار المعاينة
                        document.getElementById('previewSubject').textContent = data.subject;
                        document.getElementById('previewBody').textContent = data.body;
                        document.getElementById('templatePreview').classList.add('active');
                    }
                })
                .catch(error => {
                    console.error('Error loading template:', error);
                });
        } else {
            // إخفاء المعاينة
            document.getElementById('templatePreview').classList.remove('active');
            document.querySelector('input[name="custom_subject"]').value = '';
            document.querySelector('textarea[name="custom_body"]').value = '';
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي
        const formCard = document.querySelector('.form-card');
        formCard.style.opacity = '0';
        formCard.style.transform = 'translateY(30px)';

        setTimeout(() => {
            formCard.style.transition = 'all 0.6s ease';
            formCard.style.opacity = '1';
            formCard.style.transform = 'translateY(0)';
        }, 200);
    });
    </script>
</body>
</html>
'''

# قالب مخططات الاشتراكات المتقدم
SUBSCRIPTION_CHARTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مخططات الاشتراكات التفاعلية - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 20px;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 30px;
            text-align: center;
        }

        .charts-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 30px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .chart-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .chart-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .chart-title {
            color: #bf00ff;
            font-size: 1.4rem;
            font-weight: 600;
            text-shadow: 0 0 10px #bf00ff;
            margin-bottom: 20px;
            text-align: center;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .chart-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .summary-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 30px;
        }

        .summary-title {
            color: #00f5ff;
            font-size: 1.8rem;
            font-weight: 600;
            text-shadow: 0 0 10px #00f5ff;
            margin-bottom: 25px;
            text-align: center;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .summary-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .summary-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-5px);
        }

        .summary-icon {
            font-size: 2rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .summary-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            margin-bottom: 10px;
        }

        .summary-label {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        .export-section {
            text-align: center;
            margin-top: 30px;
        }

        .export-btn {
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 0 10px;
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .full-width-chart {
            grid-column: 1 / -1;
        }

        .radar-chart {
            height: 400px !important;
        }
    </style>
</head>
<body>
    <h1 class="page-title">
        <i class="fas fa-chart-pie me-3"></i>
        مخططات الاشتراكات التفاعلية
    </h1>

    <!-- ملخص الإحصائيات -->
    <div class="summary-section">
        <h2 class="summary-title">
            <i class="fas fa-chart-bar me-2"></i>
            ملخص الإحصائيات
        </h2>

        <div class="summary-grid">
            <div class="summary-card">
                <div class="summary-icon">
                    <i class="fas fa-subscription"></i>
                </div>
                <div class="summary-number">{{ charts_data.status_distribution.active + charts_data.status_distribution.expired + charts_data.status_distribution.suspended }}</div>
                <div class="summary-label">إجمالي الاشتراكات</div>
            </div>

            <div class="summary-card">
                <div class="summary-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="summary-number">{{ charts_data.status_distribution.active }}</div>
                <div class="summary-label">اشتراكات نشطة</div>
            </div>

            <div class="summary-card">
                <div class="summary-icon">
                    <i class="fas fa-cloud"></i>
                </div>
                <div class="summary-number">{{ charts_data.providers_distribution|length }}</div>
                <div class="summary-label">مزودي الخدمة</div>
            </div>

            <div class="summary-card">
                <div class="summary-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="summary-number">${{ "%.2f"|format(charts_data.monthly_revenue|sum(attribute='revenue') if charts_data.monthly_revenue else 0) }}</div>
                <div class="summary-label">إجمالي الإيرادات</div>
            </div>
        </div>
    </div>

    <!-- المخططات -->
    <div class="charts-container">
        <!-- مخطط توزيع الحالة -->
        <div class="chart-card">
            <h3 class="chart-title">
                <i class="fas fa-chart-pie me-2"></i>
                توزيع الاشتراكات حسب الحالة
            </h3>
            <div class="chart-container">
                <canvas id="statusChart"></canvas>
            </div>
            <div class="chart-stats">
                <div class="stat-item">
                    <div class="stat-number">{{ charts_data.status_distribution.active }}</div>
                    <div class="stat-label">نشط</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ charts_data.status_distribution.expired }}</div>
                    <div class="stat-label">منتهي</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ charts_data.status_distribution.suspended }}</div>
                    <div class="stat-label">موقوف</div>
                </div>
            </div>
        </div>

        <!-- مخطط المزودين -->
        <div class="chart-card">
            <h3 class="chart-title">
                <i class="fas fa-cloud me-2"></i>
                توزيع الاشتراكات حسب المزودين
            </h3>
            <div class="chart-container">
                <canvas id="providersChart"></canvas>
            </div>
        </div>

        <!-- مخطط أنواع الاشتراك -->
        <div class="chart-card">
            <h3 class="chart-title">
                <i class="fas fa-calendar me-2"></i>
                توزيع الاشتراكات حسب النوع
            </h3>
            <div class="chart-container">
                <canvas id="typesChart"></canvas>
            </div>
        </div>

        <!-- مخطط الإيرادات الشهرية -->
        <div class="chart-card">
            <h3 class="chart-title">
                <i class="fas fa-chart-line me-2"></i>
                الإيرادات الشهرية
            </h3>
            <div class="chart-container">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>

        <!-- مخطط المقارنة الشامل -->
        <div class="chart-card full-width-chart">
            <h3 class="chart-title">
                <i class="fas fa-chart-area me-2"></i>
                مخطط المقارنة الشامل
            </h3>
            <div class="chart-container radar-chart">
                <canvas id="radarChart"></canvas>
            </div>
        </div>
    </div>

    <!-- قسم التصدير -->
    <div class="export-section">
        <h3 style="color: #bf00ff; margin-bottom: 20px;">
            <i class="fas fa-download me-2"></i>
            تصدير وطباعة
        </h3>

        <a href="#" class="export-btn" onclick="exportCharts()">
            <i class="fas fa-file-pdf"></i>
            تصدير PDF
        </a>

        <a href="#" class="export-btn" onclick="printCharts()">
            <i class="fas fa-print"></i>
            طباعة
        </a>

        <a href="{{ url_for('dashboard') }}" class="export-btn">
            <i class="fas fa-tachometer-alt"></i>
            لوحة التحكم
        </a>
    </div>

    <script>
    // بيانات المخططات
    const chartsData = {{ charts_data|tojson }};

    // إعداد الألوان
    const colors = {
        primary: '#00f5ff',
        secondary: '#bf00ff',
        success: '#4ade80',
        warning: '#ffa500',
        danger: '#ff6b6b',
        info: '#3b82f6'
    };

    const gradientColors = [
        'rgba(0, 245, 255, 0.8)',
        'rgba(191, 0, 255, 0.8)',
        'rgba(255, 165, 0, 0.8)',
        'rgba(74, 222, 128, 0.8)',
        'rgba(255, 107, 107, 0.8)',
        'rgba(59, 130, 246, 0.8)'
    ];

    // مخطط توزيع الحالة
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['نشط', 'منتهي', 'موقوف'],
            datasets: [{
                data: [
                    chartsData.status_distribution.active,
                    chartsData.status_distribution.expired,
                    chartsData.status_distribution.suspended
                ],
                backgroundColor: [colors.success, colors.warning, colors.danger],
                borderWidth: 2,
                borderColor: 'rgba(255, 255, 255, 0.2)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: 'white',
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                }
            }
        }
    });

    // مخطط المزودين
    const providersCtx = document.getElementById('providersChart').getContext('2d');
    new Chart(providersCtx, {
        type: 'bar',
        data: {
            labels: chartsData.providers_distribution.map(p => p.name),
            datasets: [{
                label: 'عدد الاشتراكات',
                data: chartsData.providers_distribution.map(p => p.count),
                backgroundColor: gradientColors.slice(0, chartsData.providers_distribution.length),
                borderWidth: 2,
                borderColor: 'rgba(255, 255, 255, 0.2)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: 'white',
                        font: {
                            family: 'Cairo'
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: 'white',
                        font: {
                            family: 'Cairo'
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            }
        }
    });

    // مخطط أنواع الاشتراك
    const typesCtx = document.getElementById('typesChart').getContext('2d');
    const typeLabels = {
        'monthly': 'شهري',
        'semi_annual': 'نصف سنوي',
        'annual': 'سنوي'
    };

    new Chart(typesCtx, {
        type: 'pie',
        data: {
            labels: chartsData.types_distribution.map(t => typeLabels[t.type] || t.type),
            datasets: [{
                data: chartsData.types_distribution.map(t => t.count),
                backgroundColor: gradientColors.slice(0, chartsData.types_distribution.length),
                borderWidth: 2,
                borderColor: 'rgba(255, 255, 255, 0.2)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: 'white',
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                }
            }
        }
    });

    // مخطط الإيرادات الشهرية
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: chartsData.monthly_revenue.map(r => r.month),
            datasets: [{
                label: 'الإيرادات ($)',
                data: chartsData.monthly_revenue.map(r => r.revenue),
                borderColor: colors.primary,
                backgroundColor: 'rgba(0, 245, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: 'white',
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: 'white',
                        font: {
                            family: 'Cairo'
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: 'white',
                        font: {
                            family: 'Cairo'
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            }
        }
    });

    // مخطط المقارنة الشامل (Radar)
    const radarCtx = document.getElementById('radarChart').getContext('2d');
    new Chart(radarCtx, {
        type: 'radar',
        data: {
            labels: ['الاشتراكات النشطة', 'المزودين', 'الإيرادات', 'العملاء', 'النمو', 'الجودة'],
            datasets: [{
                label: 'الأداء الحالي',
                data: [
                    chartsData.status_distribution.active,
                    chartsData.providers_distribution.length,
                    chartsData.monthly_revenue.length > 0 ? chartsData.monthly_revenue[chartsData.monthly_revenue.length - 1].revenue / 100 : 0,
                    chartsData.status_distribution.active,
                    chartsData.monthly_revenue.length > 1 ?
                        ((chartsData.monthly_revenue[chartsData.monthly_revenue.length - 1].revenue -
                          chartsData.monthly_revenue[chartsData.monthly_revenue.length - 2].revenue) / 10) : 0,
                    85
                ],
                borderColor: colors.primary,
                backgroundColor: 'rgba(0, 245, 255, 0.2)',
                borderWidth: 2,
                pointBackgroundColor: colors.primary,
                pointBorderColor: 'white',
                pointBorderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: 'white',
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    ticks: {
                        color: 'white',
                        font: {
                            family: 'Cairo'
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.2)'
                    },
                    pointLabels: {
                        color: 'white',
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                }
            }
        }
    });

    // وظائف التصدير
    function exportCharts() {
        window.print();
    }

    function printCharts() {
        window.print();
    }

    // تأثيرات الظهور
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.chart-card, .summary-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
    </script>

    <style>
    @media print {
        body {
            background: white !important;
            color: black !important;
        }

        .chart-card {
            background: white !important;
            border: 1px solid #ccc !important;
            page-break-inside: avoid;
            margin-bottom: 20px;
        }

        .export-section {
            display: none;
        }
    }
    </style>
</body>
</html>
'''

# قوالب أخرى مبسطة
SUBSCRIPTIONS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>قائمة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .table { background: rgba(255, 255, 255, 0.1); border-radius: 15px; }
        .table th, .table td { border-color: rgba(255, 255, 255, 0.2); color: white; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">قائمة الاشتراكات</h1>
    <div class="table-responsive">
        <table class="table table-dark">
            <thead>
                <tr>
                    <th>اسم الاشتراك</th>
                    <th>المزود</th>
                    <th>السعر</th>
                    <th>الحالة</th>
                    <th>تاريخ الانتهاء</th>
                </tr>
            </thead>
            <tbody>
                {% for subscription in subscriptions %}
                <tr>
                    <td>{{ subscription.name }}</td>
                    <td>{{ subscription.provider.name }}</td>
                    <td>{{ subscription.price }} {{ subscription.currency }}</td>
                    <td>
                        <span class="badge bg-{{ 'success' if subscription.status == 'active' else 'warning' }}">
                            {{ subscription.status }}
                        </span>
                    </td>
                    <td>{{ subscription.end_date.strftime('%Y-%m-%d') }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

SUBSCRIPTION_ANALYTICS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تحليلات الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .analytics-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">تحليلات الاشتراكات المتقدمة</h1>

    <div class="row">
        <div class="col-md-3">
            <div class="analytics-card text-center">
                <h3 style="color: #bf00ff;">إجمالي الإيرادات</h3>
                <h2 style="color: #00f5ff;">${{ "%.2f"|format(analytics.total_revenue) }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card text-center">
                <h3 style="color: #bf00ff;">متوسط قيمة الاشتراك</h3>
                <h2 style="color: #00f5ff;">${{ "%.2f"|format(analytics.avg_subscription_value) }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card text-center">
                <h3 style="color: #bf00ff;">معدل التجديد</h3>
                <h2 style="color: #00f5ff;">{{ "%.1f"|format(analytics.renewal_rate) }}%</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card text-center">
                <h3 style="color: #bf00ff;">أفضل المزودين</h3>
                <h2 style="color: #00f5ff;">{{ analytics.top_providers|length }}</h2>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

SUBSCRIPTION_REPORTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقارير الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .report-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">تقارير الاشتراكات</h1>

    <div class="report-card">
        <h3 style="color: #bf00ff;">ملخص التقرير</h3>
        <p>الفترة: {{ report.start_date }} إلى {{ report.end_date }}</p>
        <p>إجمالي الاشتراكات: {{ report.total_subscriptions }}</p>
        <p>القيمة الإجمالية: ${{ "%.2f"|format(report.total_value) }}</p>
        <p>الاشتراكات النشطة: {{ report.active_count }}</p>
        <p>الاشتراكات المنتهية: {{ report.expired_count }}</p>
    </div>

    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

INVOICE_REPORTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقارير الفواتير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .report-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">تقارير الفواتير</h1>

    <div class="report-card">
        <h3 style="color: #bf00ff;">ملخص الفواتير</h3>
        <p>الفترة: {{ report.start_date }} إلى {{ report.end_date }}</p>
        <p>إجمالي الفواتير: {{ report.total_invoices }}</p>
        <p>المبلغ الإجمالي: ${{ "%.2f"|format(report.total_amount) }}</p>
        <p>المبلغ المدفوع: ${{ "%.2f"|format(report.paid_amount) }}</p>
        <p>المبلغ المعلق: ${{ "%.2f"|format(report.pending_amount) }}</p>
    </div>

    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

MESSAGE_TEMPLATES_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>قوالب الرسائل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .template-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">قوالب الرسائل</h1>

    {% for template in templates %}
    <div class="template-card">
        <h4 style="color: #bf00ff;">{{ template.name }}</h4>
        <p><strong>الموضوع:</strong> {{ template.subject }}</p>
        <p><strong>النوع:</strong> {{ template.template_type }}</p>
        <div style="background: rgba(255, 255, 255, 0.05); padding: 15px; border-radius: 10px; margin-top: 10px;">
            {{ template.body[:200] }}...
        </div>
    </div>
    {% endfor %}

    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

# القوالب الجديدة للميزات المتقدمة
ENHANCED_DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المتقدمة - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        .top-navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 30px;
            position: sticky;
            top: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            color: #00f5ff;
            font-size: 1.5rem;
            font-weight: 700;
            text-decoration: none;
        }

        .navbar-brand i {
            margin-left: 10px;
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notification-bell {
            position: relative;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .notification-bell:hover {
            color: #00f5ff;
            transform: scale(1.1);
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }

        .main-content {
            padding: 30px;
        }

        .page-header {
            margin-bottom: 40px;
            text-align: center;
        }

        .page-title {
            color: #00f5ff;
            font-size: 3rem;
            font-weight: 700;
            text-shadow: 0 0 30px #00f5ff;
            margin-bottom: 10px;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px #00f5ff; }
            to { text-shadow: 0 0 40px #00f5ff, 0 0 60px #00f5ff; }
        }

        .quick-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .stat-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 25px 50px rgba(0, 245, 255, 0.3);
        }

        .stat-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #00f5ff;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 10px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            font-weight: 600;
        }

        .ai-insights-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-title {
            color: #bf00ff;
            font-size: 1.8rem;
            font-weight: 600;
            text-shadow: 0 0 15px #bf00ff;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .ai-insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }

        .insight-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .insight-card.success { border-left-color: #4ade80; }
        .insight-card.warning { border-left-color: #ffa500; }
        .insight-card.error { border-left-color: #ff6b6b; }
        .insight-card.info { border-left-color: #3b82f6; }

        .insight-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(-5px);
        }

        .insight-title {
            font-weight: 600;
            color: white;
            margin-bottom: 10px;
        }

        .insight-message {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .insight-action {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .insight-action:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #00f5ff;
        }
    </style>
</head>
<body>
    <nav class="top-navbar">
        <a href="{{ url_for('dashboard') }}" class="navbar-brand">
            <i class="fas fa-rocket"></i>
            نظام إدارة الاشتراكات المتقدم
        </a>

        <div class="navbar-actions">
            <a href="{{ url_for('notifications') }}" class="notification-bell">
                <i class="fas fa-bell"></i>
                {% if recent_notifications %}
                <span class="notification-badge">{{ recent_notifications|length }}</span>
                {% endif %}
            </a>

            <a href="{{ url_for('logout') }}" class="quick-action-btn" style="background: linear-gradient(135deg, #ff6b6b, #ff4757);">
                <i class="fas fa-sign-out-alt"></i>
                خروج
            </a>
        </div>
    </nav>

    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-tachometer-alt me-3"></i>
                لوحة التحكم المتقدمة
            </h1>
            <p style="color: rgba(255, 255, 255, 0.8); font-size: 1.2rem; margin-bottom: 20px;">
                مرحباً {{ current_user.full_name }}، إليك نظرة شاملة على نظامك
            </p>

            <div class="quick-actions">
                <a href="{{ url_for('notifications') }}" class="quick-action-btn">
                    <i class="fas fa-bell"></i>
                    الإشعارات
                </a>
                <a href="{{ url_for('calendar_view') }}" class="quick-action-btn">
                    <i class="fas fa-calendar-alt"></i>
                    التقويم
                </a>
                <a href="{{ url_for('ai_insights') }}" class="quick-action-btn">
                    <i class="fas fa-brain"></i>
                    الرؤى الذكية
                </a>
                <a href="{{ url_for('system_health') }}" class="quick-action-btn">
                    <i class="fas fa-heartbeat"></i>
                    صحة النظام
                </a>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-subscription"></i>
                </div>
                <div class="stat-number">{{ stats.total_subscriptions }}</div>
                <div class="stat-label">إجمالي الاشتراكات</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number">{{ stats.active_subscriptions }}</div>
                <div class="stat-label">اشتراكات نشطة</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-number">${{ "%.0f"|format(stats.total_revenue) }}</div>
                <div class="stat-label">إجمالي الإيرادات</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number">{{ stats.expiring_soon }}</div>
                <div class="stat-label">تنتهي قريباً</div>
            </div>
        </div>

        {% if ai_insights %}
        <div class="ai-insights-section">
            <h2 class="section-title">
                <i class="fas fa-brain"></i>
                الرؤى الذكية بالذكاء الاصطناعي
            </h2>

            <div class="ai-insights-grid">
                {% for insight in ai_insights %}
                <div class="insight-card {{ insight.type }}">
                    <div class="insight-title">{{ insight.title }}</div>
                    <div class="insight-message">{{ insight.message }}</div>
                    <a href="#" class="insight-action">{{ insight.action }}</a>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        {% if critical_subscriptions %}
        <div style="background: rgba(255, 107, 107, 0.1); border: 1px solid rgba(255, 107, 107, 0.3); border-radius: 25px; padding: 30px; margin-bottom: 40px;">
            <h2 class="section-title" style="color: #ff6b6b;">
                <i class="fas fa-exclamation-triangle"></i>
                خوادم تحتاج انتباه فوري
            </h2>

            {% for server in critical_subscriptions %}
            <div style="background: rgba(255, 255, 255, 0.05); border-radius: 15px; padding: 20px; margin-bottom: 15px; border-left: 4px solid #ff6b6b;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <div style="font-weight: 600; color: white;">{{ server.name }}</div>
                    <div style="background: rgba(255, 107, 107, 0.2); color: #ff6b6b; padding: 5px 15px; border-radius: 20px; font-size: 0.9rem;">حرج</div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 15px;">
                    <div style="text-align: center;">
                        <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem; margin-bottom: 5px;">المعالج</div>
                        <div style="color: white; font-weight: 600; font-size: 0.9rem;">{{ "%.1f"|format(server.cpu_usage) }}%</div>
                    </div>

                    <div style="text-align: center;">
                        <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem; margin-bottom: 5px;">الذاكرة</div>
                        <div style="color: white; font-weight: 600; font-size: 0.9rem;">{{ "%.1f"|format(server.memory_usage) }}%</div>
                    </div>

                    <div style="text-align: center;">
                        <div style="color: rgba(255, 255, 255, 0.8); font-size: 0.9rem; margin-bottom: 5px;">القرص</div>
                        <div style="color: white; font-weight: 600; font-size: 0.9rem;">{{ "%.1f"|format(server.disk_usage) }}%</div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach(number => {
            const target = parseInt(number.textContent.replace(/[^0-9]/g, ''));
            let current = 0;
            const increment = target / 50;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                if (number.textContent.includes('$')) {
                    number.textContent = '$' + Math.floor(current);
                } else {
                    number.textContent = Math.floor(current);
                }
            }, 40);
        });

        const cards = document.querySelectorAll('.stat-card, .insight-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
    </script>
</body>
</html>
'''

# قوالب الميزات الجديدة
NOTIFICATIONS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>الإشعارات - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 20px;
        }
        .notification-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        .notification-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(-5px);
        }
        .notification-card.info { border-left-color: #3b82f6; }
        .notification-card.success { border-left-color: #4ade80; }
        .notification-card.warning { border-left-color: #ffa500; }
        .notification-card.error { border-left-color: #ff6b6b; }
        .notification-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .notification-title { font-weight: 600; color: white; }
        .notification-time { color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; }
        .notification-message { color: rgba(255, 255, 255, 0.8); line-height: 1.5; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">
        <i class="fas fa-bell me-2"></i>الإشعارات
    </h1>

    {% for notification in notifications.items %}
    <div class="notification-card {{ notification.notification_type }}">
        <div class="notification-header">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-time">{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
        </div>
        <div class="notification-message">{{ notification.message }}</div>
        {% if notification.action_url %}
        <a href="{{ notification.action_url }}" class="btn btn-sm btn-outline-light mt-2">{{ notification.action_text or 'عرض' }}</a>
        {% endif %}
    </div>
    {% endfor %}

    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

CALENDAR_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>التقويم التفاعلي - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 20px;
        }
        .calendar-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 30px;
            backdrop-filter: blur(20px);
        }
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;
        }
        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .calendar-day:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.05);
        }
        .calendar-day.has-event {
            background: rgba(0, 245, 255, 0.3);
            border: 2px solid #00f5ff;
        }
        .event-list {
            margin-top: 30px;
        }
        .event-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid;
        }
        .event-item.expiry { border-left-color: #ff6b6b; }
        .event-item.payment { border-left-color: #3b82f6; }
        .event-item.backup { border-left-color: #4ade80; }
    </style>
</head>
<body>
    <div class="calendar-container">
        <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">
            <i class="fas fa-calendar-alt me-2"></i>التقويم التفاعلي
        </h1>

        <div class="calendar-header">
            <button class="btn btn-outline-light" onclick="previousMonth()">
                <i class="fas fa-chevron-right"></i>
            </button>
            <h3 id="currentMonth" style="color: #bf00ff;"></h3>
            <button class="btn btn-outline-light" onclick="nextMonth()">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>

        <div class="calendar-grid" id="calendarGrid">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>

        <div class="event-list">
            <h4 style="color: #bf00ff; margin-bottom: 20px;">الأحداث القادمة</h4>
            {% for event in events %}
            <div class="event-item {{ event.type }}">
                <strong>{{ event.title }}</strong><br>
                <small>{{ event.date }}</small>
            </div>
            {% endfor %}
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>

    <script>
    const events = {{ events|tojson }};
    let currentDate = new Date();

    function renderCalendar() {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();

        document.getElementById('currentMonth').textContent =
            new Date(year, month).toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });

        const firstDay = new Date(year, month, 1).getDay();
        const daysInMonth = new Date(year, month + 1, 0).getDate();

        const grid = document.getElementById('calendarGrid');
        grid.innerHTML = '';

        const weekDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        weekDays.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.textContent = day;
            dayHeader.style.fontWeight = '600';
            dayHeader.style.color = '#00f5ff';
            dayHeader.style.textAlign = 'center';
            dayHeader.style.padding = '10px';
            grid.appendChild(dayHeader);
        });

        for (let i = 0; i < firstDay; i++) {
            const emptyDay = document.createElement('div');
            emptyDay.className = 'calendar-day';
            grid.appendChild(emptyDay);
        }

        for (let day = 1; day <= daysInMonth; day++) {
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = day;

            const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            const hasEvent = events.some(event => event.date === dateStr);

            if (hasEvent) {
                dayElement.classList.add('has-event');
            }

            if (day === new Date().getDate() && month === new Date().getMonth() && year === new Date().getFullYear()) {
                dayElement.style.background = 'linear-gradient(135deg, #00f5ff, #bf00ff)';
                dayElement.style.color = 'white';
                dayElement.style.fontWeight = '600';
            }

            grid.appendChild(dayElement);
        }
    }

    function previousMonth() {
        currentDate.setMonth(currentDate.getMonth() - 1);
        renderCalendar();
    }

    function nextMonth() {
        currentDate.setMonth(currentDate.getMonth() + 1);
        renderCalendar();
    }

    renderCalendar();
    </script>
</body>
</html>
'''

AI_INSIGHTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>الرؤى الذكية - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 20px;
        }
        .ai-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 30px;
            backdrop-filter: blur(20px);
            margin-bottom: 30px;
        }
        .ai-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .ai-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 10px;
        }
        .ai-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #00f5ff;
            margin-bottom: 10px;
        }
        .stat-label {
            color: rgba(255, 255, 255, 0.8);
        }
        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        .insight-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid;
        }
        .insight-card.success { border-left-color: #4ade80; }
        .insight-card.warning { border-left-color: #ffa500; }
        .insight-card.error { border-left-color: #ff6b6b; }
        .insight-card.info { border-left-color: #3b82f6; }
    </style>
</head>
<body>
    <div class="ai-container">
        <div class="ai-header">
            <h1 class="ai-title">
                <i class="fas fa-brain me-3"></i>
                الرؤى الذكية بالذكاء الاصطناعي
            </h1>
            <p class="ai-subtitle">تحليلات متقدمة وتوصيات ذكية لتحسين أداء نظامك</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ "%.1f"|format(advanced_stats.growth_rate) }}%</div>
                <div class="stat-label">معدل النمو</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ "%.1f"|format(advanced_stats.efficiency_score) }}%</div>
                <div class="stat-label">درجة الكفاءة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ "%.1f"|format(advanced_stats.cost_optimization) }}%</div>
                <div class="stat-label">توفير التكاليف</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ "%.2f"|format(advanced_stats.uptime_percentage) }}%</div>
                <div class="stat-label">نسبة التشغيل</div>
            </div>
        </div>

        <div class="insights-grid">
            {% for insight in insights %}
            <div class="insight-card {{ insight.type }}">
                <h5 style="color: white; margin-bottom: 15px;">
                    <i class="fas fa-lightbulb me-2"></i>{{ insight.title }}
                </h5>
                <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 15px;">{{ insight.message }}</p>
                <button class="btn btn-sm btn-outline-light">{{ insight.action }}</button>
            </div>
            {% endfor %}
        </div>
    </div>

    <div class="text-center">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

# قوالب أخرى مبسطة
REVIEWS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>المراجعات والتقييمات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .review-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px; }
        .rating { color: #ffa500; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">المراجعات والتقييمات</h1>

    <div class="row">
        <div class="col-md-8">
            {% for review in reviews %}
            <div class="review-card">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5>{{ review.title or 'مراجعة' }}</h5>
                    <div class="rating">
                        {% for i in range(review.rating) %}★{% endfor %}
                        {% for i in range(5 - review.rating) %}☆{% endfor %}
                    </div>
                </div>
                <p>{{ review.comment }}</p>
                <small class="text-muted">{{ review.user.full_name }} - {{ review.created_at.strftime('%Y-%m-%d') }}</small>
            </div>
            {% endfor %}
        </div>

        <div class="col-md-4">
            <div class="review-card">
                <h5>إضافة مراجعة</h5>
                <form method="POST" action="{{ url_for('add_review') }}">
                    <div class="mb-3">
                        <select name="provider_id" class="form-select" required>
                            <option value="">اختر المزود</option>
                            {% for provider in providers %}
                            <option value="{{ provider.id }}">{{ provider.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <select name="rating" class="form-select" required>
                            <option value="">التقييم</option>
                            <option value="5">5 نجوم</option>
                            <option value="4">4 نجوم</option>
                            <option value="3">3 نجوم</option>
                            <option value="2">نجمتان</option>
                            <option value="1">نجمة واحدة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <input type="text" name="title" class="form-control" placeholder="عنوان المراجعة">
                    </div>
                    <div class="mb-3">
                        <textarea name="comment" class="form-control" rows="3" placeholder="تعليقك"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة مراجعة</button>
                </form>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

FILES_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>ملفات الاشتراك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .file-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 15px; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">
        ملفات الاشتراك: {{ subscription.name }}
    </h1>

    <div class="row">
        <div class="col-md-8">
            {% for file in files %}
            <div class="file-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6>{{ file.original_filename }}</h6>
                        <small>{{ file.description or 'لا يوجد وصف' }}</small><br>
                        <small class="text-muted">{{ "%.2f"|format(file.file_size / 1024) }} KB - {{ file.created_at.strftime('%Y-%m-%d') }}</small>
                    </div>
                    <a href="{{ url_for('download_file', file_id=file.id) }}" class="btn btn-sm btn-outline-light">
                        <i class="fas fa-download"></i> تحميل
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="col-md-4">
            <div class="file-card">
                <h5>رفع ملف جديد</h5>
                <form method="POST" action="{{ url_for('upload_file', subscription_id=subscription.id) }}" enctype="multipart/form-data">
                    <div class="mb-3">
                        <input type="file" name="file" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <select name="category" class="form-select">
                            <option value="general">عام</option>
                            <option value="config">إعدادات</option>
                            <option value="backup">نسخة احتياطية</option>
                            <option value="certificate">شهادة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <textarea name="description" class="form-control" rows="2" placeholder="وصف الملف"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">رفع الملف</button>
                </form>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

SYSTEM_HEALTH_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>صحة النظام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .health-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px; text-align: center; }
        .health-number { font-size: 2rem; font-weight: 700; margin-bottom: 10px; }
        .healthy { color: #4ade80; }
        .warning { color: #ffa500; }
        .critical { color: #ff6b6b; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">صحة النظام</h1>

    <div class="row">
        <div class="col-md-3">
            <div class="health-card">
                <div class="health-number healthy">{{ health_stats.healthy_subscriptions }}</div>
                <div>خوادم صحية</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="health-card">
                <div class="health-number warning">{{ health_stats.warning_subscriptions }}</div>
                <div>تحذيرات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="health-card">
                <div class="health-number critical">{{ health_stats.critical_subscriptions }}</div>
                <div>حالات حرجة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="health-card">
                <div class="health-number">{{ health_stats.total_subscriptions }}</div>
                <div>إجمالي الخوادم</div>
            </div>
        </div>
    </div>

    {% if critical_servers %}
    <h3 style="color: #ff6b6b; margin: 30px 0;">خوادم تحتاج انتباه فوري</h3>
    {% for server in critical_servers %}
    <div class="health-card" style="text-align: right;">
        <h5>{{ server.name }}</h5>
        <div class="row">
            <div class="col-md-4">المعالج: {{ "%.1f"|format(server.cpu_usage) }}%</div>
            <div class="col-md-4">الذاكرة: {{ "%.1f"|format(server.memory_usage) }}%</div>
            <div class="col-md-4">القرص: {{ "%.1f"|format(server.disk_usage) }}%</div>
        </div>
    </div>
    {% endfor %}
    {% endif %}

    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

BACKUP_MANAGEMENT_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة النسخ الاحتياطية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .backup-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 15px; }
        .backup-enabled { border-left: 4px solid #4ade80; }
        .backup-disabled { border-left: 4px solid #ff6b6b; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">إدارة النسخ الاحتياطية</h1>

    {% for subscription in subscriptions %}
    <div class="backup-card {{ 'backup-enabled' if subscription.backup_enabled else 'backup-disabled' }}">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5>{{ subscription.name }}</h5>
                <small>{{ subscription.provider.name }}</small><br>
                {% if subscription.backup_enabled and subscription.last_backup %}
                <small class="text-success">آخر نسخة احتياطية: {{ subscription.last_backup.strftime('%Y-%m-%d %H:%M') }}</small>
                {% elif subscription.backup_enabled %}
                <small class="text-warning">لم يتم إنشاء نسخة احتياطية بعد</small>
                {% else %}
                <small class="text-danger">النسخ الاحتياطي معطل</small>
                {% endif %}
            </div>
            <a href="{{ url_for('toggle_backup', subscription_id=subscription.id) }}"
               class="btn btn-sm {{ 'btn-outline-danger' if subscription.backup_enabled else 'btn-outline-success' }}">
                {{ 'تعطيل' if subscription.backup_enabled else 'تفعيل' }}
            </a>
        </div>
    </div>
    {% endfor %}

    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

# قالب مخطط الاشتراكات التفاعلي
SUBSCRIPTION_CHARTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مخطط الاشتراكات التفاعلي - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .page-title {
            color: #667eea;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .charts-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
        }

        .chart-title {
            color: #667eea;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .export-buttons {
            text-align: center;
            margin: 2rem 0;
        }

        .export-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            color: white;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للوحة التحكم
        </a>

        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-chart-pie me-3"></i>
                مخطط الاشتراكات التفاعلي
            </h1>
            <p class="text-muted">تحليل شامل ومرئي لجميع الاشتراكات والإحصائيات</p>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value">25</div>
                <div class="stat-label">إجمالي الاشتراكات</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">18</div>
                <div class="stat-label">اشتراكات نشطة</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">$2,450</div>
                <div class="stat-label">إجمالي الإيرادات</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">3</div>
                <div class="stat-label">تنتهي قريباً</div>
            </div>
        </div>

        <!-- المخططات البيانية -->
        <div class="charts-container">
            <!-- مخطط دائري للحالات -->
            <div class="chart-card">
                <h3 class="chart-title">توزيع حالات الاشتراكات</h3>
                <canvas id="statusChart" width="400" height="300"></canvas>
            </div>

            <!-- مخطط عمودي للإيرادات الشهرية -->
            <div class="chart-card">
                <h3 class="chart-title">الإيرادات الشهرية</h3>
                <canvas id="revenueChart" width="400" height="300"></canvas>
            </div>

            <!-- مخطط خطي لنمو الاشتراكات -->
            <div class="chart-card">
                <h3 class="chart-title">نمو الاشتراكات</h3>
                <canvas id="growthChart" width="400" height="300"></canvas>
            </div>

            <!-- مخطط دائري للمزودين -->
            <div class="chart-card">
                <h3 class="chart-title">توزيع المزودين</h3>
                <canvas id="providersChart" width="400" height="300"></canvas>
            </div>
        </div>

        <!-- أزرار التصدير -->
        <div class="export-buttons">
            <button class="export-btn" onclick="exportCharts()">
                <i class="fas fa-download me-2"></i>
                تصدير المخططات
            </button>
            <button class="export-btn" onclick="printCharts()">
                <i class="fas fa-print me-2"></i>
                طباعة التقرير
            </button>
            <button class="export-btn" onclick="exportPDF()">
                <i class="fas fa-file-pdf me-2"></i>
                تصدير PDF
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // مخطط حالات الاشتراكات
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'pie',
            data: {
                labels: ['نشط', 'منتهي', 'معلق', 'ملغي'],
                datasets: [{
                    data: [18, 4, 2, 1],
                    backgroundColor: ['#00b894', '#e17055', '#fdcb6e', '#fd79a8'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // مخطط الإيرادات الشهرية
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات ($)',
                    data: [1200, 1900, 1500, 2200, 1800, 2450],
                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                    borderColor: '#667eea',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // مخطط نمو الاشتراكات
        const growthCtx = document.getElementById('growthChart').getContext('2d');
        new Chart(growthCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'عدد الاشتراكات',
                    data: [8, 12, 15, 18, 22, 25],
                    borderColor: '#00b894',
                    backgroundColor: 'rgba(0, 184, 148, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // مخطط المزودين
        const providersCtx = document.getElementById('providersChart').getContext('2d');
        new Chart(providersCtx, {
            type: 'doughnut',
            data: {
                labels: ['AWS', 'Google Cloud', 'Azure', 'DigitalOcean', 'أخرى'],
                datasets: [{
                    data: [8, 6, 4, 4, 3],
                    backgroundColor: ['#667eea', '#764ba2', '#00b894', '#fdcb6e', '#e17055'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // وظائف التصدير
        function exportCharts() {
            alert('سيتم تصدير المخططات قريباً...');
        }

        function printCharts() {
            window.print();
        }

        function exportPDF() {
            alert('سيتم تصدير PDF قريباً...');
        }
    </script>
</body>
</html>
'''

# قالب إضافة اشتراك جديد
ADD_SUBSCRIPTION_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة اشتراك جديد - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .form-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 2rem 0;
        }

        .form-title {
            color: #667eea;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للوحة التحكم
        </a>

        <div class="form-container">
            <h1 class="form-title">
                <i class="fas fa-plus me-3"></i>
                إضافة اشتراك جديد
            </h1>

            <form method="POST">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم الاشتراك</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">مزود الخدمة</label>
                        <select class="form-control" name="provider_id" required>
                            <option value="">اختر مزود الخدمة</option>
                            <option value="1">AdenLink</option>
                            <option value="2">Amazon Web Services</option>
                            <option value="3">Microsoft Azure</option>
                            <option value="4">Google Cloud Platform</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">نوع الخدمة</label>
                        <select class="form-control" name="service_type" required>
                            <option value="">اختر نوع الخدمة</option>
                            <option value="web_hosting">استضافة ويب</option>
                            <option value="cloud_server">خادم سحابي</option>
                            <option value="database">قاعدة بيانات</option>
                            <option value="storage">تخزين</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">نوع الاشتراك</label>
                        <select class="form-control" name="subscription_type" required>
                            <option value="">اختر نوع الاشتراك</option>
                            <option value="monthly">شهري</option>
                            <option value="semi_annual">نصف سنوي</option>
                            <option value="annual">سنوي</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">السعر</label>
                        <input type="number" class="form-control" name="price" step="0.01" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">العملة</label>
                        <select class="form-control" name="currency">
                            <option value="USD">دولار أمريكي (USD)</option>
                            <option value="EUR">يورو (EUR)</option>
                            <option value="IQD">دينار عراقي (IQD)</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">تاريخ البداية</label>
                        <input type="date" class="form-control" name="start_date" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">تاريخ الانتهاء</label>
                        <input type="date" class="form-control" name="end_date" required>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">الوصف</label>
                    <textarea class="form-control" name="description" rows="3"></textarea>
                </div>

                <div class="text-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        حفظ الاشتراك
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

# قالب إرسال الرسائل
SEND_MESSAGE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إرسال رسالة - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .message-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 2rem 0;
        }

        .message-title {
            color: #667eea;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .recipient-options {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .template-section {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            color: white;
        }

        .variable-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .variable-tag {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .variable-tag:hover {
            background: #764ba2;
            transform: scale(1.05);
        }

        .preview-section {
            background: #fff3cd;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للوحة التحكم
        </a>

        <div class="message-container">
            <h1 class="message-title">
                <i class="fas fa-paper-plane me-3"></i>
                إرسال رسالة إلكترونية
            </h1>

            <form method="POST" id="messageForm">
                <!-- خيارات المستقبل -->
                <div class="recipient-options">
                    <h5><i class="fas fa-users me-2"></i>خيارات المستقبل</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="recipient_type" value="specific" id="specific">
                                <label class="form-check-label" for="specific">
                                    عميل محدد
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="recipient_type" value="all_active" id="all_active">
                                <label class="form-check-label" for="all_active">
                                    جميع العملاء النشطين
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="recipient_type" value="expiring" id="expiring">
                                <label class="form-check-label" for="expiring">
                                    العملاء المنتهية اشتراكاتهم قريباً
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="recipient_type" value="custom" id="custom" checked>
                                <label class="form-check-label" for="custom">
                                    إيميل مخصص
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3" id="customEmailDiv">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="custom_email" placeholder="<EMAIL>">
                    </div>

                    <div class="mt-3" id="specificUserDiv" style="display: none;">
                        <label class="form-label">اختر العميل</label>
                        <select class="form-control" name="user_id">
                            <option value="">اختر العميل</option>
                            <option value="1">أحمد علي محمد</option>
                            <option value="2">سارة حسن أحمد</option>
                            <option value="3">عمر خليل إبراهيم</option>
                        </select>
                    </div>
                </div>

                <!-- قوالب الرسائل -->
                <div class="template-section">
                    <h5><i class="fas fa-file-text me-2"></i>قوالب الرسائل الجاهزة</h5>
                    <select class="form-control" name="template_id" id="templateSelect">
                        <option value="">اختر قالب أو اكتب رسالة مخصصة</option>
                        <option value="welcome">رسالة ترحيب</option>
                        <option value="renewal">تذكير التجديد</option>
                        <option value="payment_due">استحقاق الدفع</option>
                        <option value="custom">رسالة مخصصة</option>
                    </select>

                    <div class="variable-tags">
                        <span class="variable-tag" onclick="insertVariable('{name}')">{name}</span>
                        <span class="variable-tag" onclick="insertVariable('{price}')">{price}</span>
                        <span class="variable-tag" onclick="insertVariable('{end_date}')">{end_date}</span>
                        <span class="variable-tag" onclick="insertVariable('{cloud_name}')">{cloud_name}</span>
                        <span class="variable-tag" onclick="insertVariable('{cloud_ip}')">{cloud_ip}</span>
                    </div>
                </div>

                <!-- محتوى الرسالة -->
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" name="subject" id="messageSubject" required>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">نص الرسالة</label>
                    <textarea class="form-control" name="body" id="messageBody" rows="8" required></textarea>
                </div>

                <!-- خيارات متقدمة -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="attach_pdf" id="attach_pdf">
                            <label class="form-check-label" for="attach_pdf">
                                إرفاق PDF بالتفاصيل
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="send_copy" id="send_copy">
                            <label class="form-check-label" for="send_copy">
                                إرسال نسخة للمرسل
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="schedule" id="schedule">
                            <label class="form-check-label" for="schedule">
                                جدولة الإرسال
                            </label>
                        </div>
                    </div>
                </div>

                <div class="mt-3" id="scheduleDiv" style="display: none;">
                    <label class="form-label">تاريخ ووقت الإرسال</label>
                    <input type="datetime-local" class="form-control" name="scheduled_at">
                </div>

                <!-- معاينة الرسالة -->
                <div class="preview-section" id="previewSection" style="display: none;">
                    <h5><i class="fas fa-eye me-2"></i>معاينة الرسالة</h5>
                    <div id="previewContent"></div>
                </div>

                <div class="text-center mt-4">
                    <button type="button" class="btn btn-outline-primary me-2" onclick="previewMessage()">
                        <i class="fas fa-eye me-2"></i>
                        معاينة
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>
                        إرسال الرسالة
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تبديل خيارات المستقبل
        document.querySelectorAll('input[name="recipient_type"]').forEach(radio => {
            radio.addEventListener('change', function() {
                document.getElementById('customEmailDiv').style.display =
                    this.value === 'custom' ? 'block' : 'none';
                document.getElementById('specificUserDiv').style.display =
                    this.value === 'specific' ? 'block' : 'none';
            });
        });

        // تبديل جدولة الإرسال
        document.getElementById('schedule').addEventListener('change', function() {
            document.getElementById('scheduleDiv').style.display =
                this.checked ? 'block' : 'none';
        });

        // قوالب الرسائل
        const templates = {
            welcome: {
                subject: 'مرحباً بك في {cloud_name}',
                body: 'مرحباً {name},\\n\\nنرحب بك في خدمة {cloud_name}!\\n\\nتفاصيل اشتراكك:\\n- السعر: {price}\\n- تاريخ الانتهاء: {end_date}\\n- عنوان الخادم: {cloud_ip}\\n\\nشكراً لاختيارك خدماتنا.'
            },
            renewal: {
                subject: 'تذكير: اشتراكك سينتهي قريباً',
                body: 'عزيزي {name},\\n\\nنود تذكيرك بأن اشتراكك سينتهي في {end_date}.\\n\\nالسعر: {price}\\nالخادم: {cloud_ip}\\n\\nيرجى التجديد لتجنب انقطاع الخدمة.'
            },
            payment_due: {
                subject: 'فاتورة مستحقة الدفع',
                body: 'عزيزي {name},\\n\\nلديك فاتورة مستحقة الدفع بقيمة {price}.\\n\\nيرجى سداد المبلغ في أقرب وقت ممكن.'
            }
        };

        document.getElementById('templateSelect').addEventListener('change', function() {
            const template = templates[this.value];
            if (template) {
                document.getElementById('messageSubject').value = template.subject;
                document.getElementById('messageBody').value = template.body;
            }
        });

        // إدراج المتغيرات
        function insertVariable(variable) {
            const textarea = document.getElementById('messageBody');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const text = textarea.value;
            textarea.value = text.substring(0, start) + variable + text.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + variable.length, start + variable.length);
        }

        // معاينة الرسالة
        function previewMessage() {
            const subject = document.getElementById('messageSubject').value;
            const body = document.getElementById('messageBody').value;

            const previewContent = `
                <strong>الموضوع:</strong> ${subject}<br><br>
                <strong>النص:</strong><br>
                <div style="white-space: pre-wrap; background: white; padding: 1rem; border-radius: 5px; margin-top: 0.5rem;">
                    ${body}
                </div>
            `;

            document.getElementById('previewContent').innerHTML = previewContent;
            document.getElementById('previewSection').style.display = 'block';
        }
    </script>
</body>
</html>
'''

# قالب قوالب الرسائل
MESSAGE_TEMPLATES_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قوالب الرسائل - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .templates-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 2rem 0;
        }

        .template-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .template-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .template-title {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .template-type {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للوحة التحكم
        </a>

        <div class="templates-container">
            <h1 class="text-center mb-4" style="color: #667eea;">
                <i class="fas fa-file-text me-3"></i>
                قوالب الرسائل
            </h1>

            <!-- قالب رسالة الترحيب -->
            <div class="template-card">
                <div class="template-type">رسالة ترحيب</div>
                <h5 class="template-title">مرحباً بك في {cloud_name}</h5>
                <p><strong>الموضوع:</strong> مرحباً بك في {cloud_name}</p>
                <p><strong>النص:</strong></p>
                <div style="background: white; padding: 1rem; border-radius: 5px; white-space: pre-wrap;">مرحباً {name},

نرحب بك في خدمة {cloud_name}!

تفاصيل اشتراكك:
- اسم الاشتراك: {subscription_name}
- السعر: {price} {currency}
- تاريخ الانتهاء: {end_date}
- عنوان الخادم: {cloud_ip}

شكراً لاختيارك خدماتنا.

مع أطيب التحيات،
فريق AdenLink</div>
            </div>

            <!-- قالب تذكير التجديد -->
            <div class="template-card">
                <div class="template-type">تذكير التجديد</div>
                <h5 class="template-title">تذكير: اشتراكك {subscription_name} سينتهي قريباً</h5>
                <p><strong>الموضوع:</strong> تذكير: اشتراكك {subscription_name} سينتهي قريباً</p>
                <p><strong>النص:</strong></p>
                <div style="background: white; padding: 1rem; border-radius: 5px; white-space: pre-wrap;">عزيزي {name},

نود تذكيرك بأن اشتراكك في {subscription_name} سينتهي في {end_date}.

تفاصيل الاشتراك:
- الخدمة: {cloud_name}
- السعر: {price} {currency}
- عنوان الخادم: {cloud_ip}

يرجى تجديد اشتراكك لتجنب انقطاع الخدمة.

مع أطيب التحيات،
فريق AdenLink</div>
            </div>

            <!-- قالب استحقاق الدفع -->
            <div class="template-card">
                <div class="template-type">استحقاق الدفع</div>
                <h5 class="template-title">فاتورة مستحقة الدفع - {subscription_name}</h5>
                <p><strong>الموضوع:</strong> فاتورة مستحقة الدفع - {subscription_name}</p>
                <p><strong>النص:</strong></p>
                <div style="background: white; padding: 1rem; border-radius: 5px; white-space: pre-wrap;">عزيزي {name},

لديك فاتورة مستحقة الدفع لاشتراك {subscription_name}.

تفاصيل الفاتورة:
- المبلغ: {price} {currency}
- تاريخ الاستحقاق: {due_date}
- الخدمة: {cloud_name}

يرجى سداد المبلغ في أقرب وقت ممكن.

مع أطيب التحيات،
فريق AdenLink</div>
            </div>

            <!-- المتغيرات المتاحة -->
            <div class="template-card">
                <div class="template-type">المتغيرات المتاحة</div>
                <h5 class="template-title">المتغيرات الديناميكية</h5>
                <p>يمكنك استخدام هذه المتغيرات في قوالب الرسائل:</p>
                <div class="row">
                    <div class="col-md-6">
                        <ul>
                            <li><code>{name}</code> - اسم العميل</li>
                            <li><code>{subscription_name}</code> - اسم الاشتراك</li>
                            <li><code>{price}</code> - السعر</li>
                            <li><code>{currency}</code> - العملة</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul>
                            <li><code>{end_date}</code> - تاريخ الانتهاء</li>
                            <li><code>{cloud_name}</code> - اسم مزود الخدمة</li>
                            <li><code>{cloud_ip}</code> - عنوان IP الخادم</li>
                            <li><code>{due_date}</code> - تاريخ استحقاق الفاتورة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

# قالب قائمة الفواتير
INVOICES_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الفواتير - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .invoices-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 2rem 0;
        }

        .invoice-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            border-left: 4px solid #667eea;
        }

        .invoice-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .invoice-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-paid {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-overdue {
            background: #f8d7da;
            color: #721c24;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            color: white;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للوحة التحكم
        </a>

        <div class="invoices-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 style="color: #667eea;">
                    <i class="fas fa-file-invoice me-3"></i>
                    قائمة الفواتير
                </h1>
                <a href="{{ url_for('create_invoice') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء فاتورة جديدة
                </a>
            </div>

            <!-- فاتورة مدفوعة -->
            <div class="invoice-card">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5 class="mb-2">فاتورة #INV-202407-0001</h5>
                        <p class="mb-1"><strong>العميل:</strong> أحمد علي محمد</p>
                        <p class="mb-1"><strong>الاشتراك:</strong> خادم ويب رئيسي - AdenLink</p>
                        <p class="mb-1"><strong>المبلغ:</strong> $1,200.00</p>
                        <p class="mb-1"><strong>تاريخ الإصدار:</strong> 2024-07-01</p>
                        <p class="mb-1"><strong>تاريخ الاستحقاق:</strong> 2024-07-31</p>
                        <p class="mb-0"><strong>تاريخ الدفع:</strong> 2024-07-15</p>
                    </div>
                    <div class="text-end">
                        <span class="invoice-status status-paid">مدفوعة</span>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-primary me-1">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary me-1">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فاتورة معلقة -->
            <div class="invoice-card">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5 class="mb-2">فاتورة #INV-202407-0002</h5>
                        <p class="mb-1"><strong>العميل:</strong> سارة حسن أحمد</p>
                        <p class="mb-1"><strong>الاشتراك:</strong> خادم التطبيقات - AWS</p>
                        <p class="mb-1"><strong>المبلغ:</strong> $147.50</p>
                        <p class="mb-1"><strong>تاريخ الإصدار:</strong> 2024-07-01</p>
                        <p class="mb-1"><strong>تاريخ الاستحقاق:</strong> 2024-07-30</p>
                        <p class="mb-0"><strong>الحالة:</strong> في انتظار الدفع</p>
                    </div>
                    <div class="text-end">
                        <span class="invoice-status status-pending">معلقة</span>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-primary me-1">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary me-1">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فاتورة متأخرة -->
            <div class="invoice-card">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5 class="mb-2">فاتورة #INV-202406-0015</h5>
                        <p class="mb-1"><strong>العميل:</strong> عمر خليل إبراهيم</p>
                        <p class="mb-1"><strong>الاشتراك:</strong> قاعدة بيانات - Azure</p>
                        <p class="mb-1"><strong>المبلغ:</strong> $598.00</p>
                        <p class="mb-1"><strong>تاريخ الإصدار:</strong> 2024-06-01</p>
                        <p class="mb-1"><strong>تاريخ الاستحقاق:</strong> 2024-06-30</p>
                        <p class="mb-0"><strong>الحالة:</strong> متأخرة الدفع</p>
                    </div>
                    <div class="text-end">
                        <span class="invoice-status status-overdue">متأخرة</span>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-danger me-1">
                                <i class="fas fa-exclamation-triangle"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary me-1">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-phone"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="text-center p-3" style="background: #e8f5e8; border-radius: 10px;">
                        <h4 style="color: #28a745;">12</h4>
                        <small>فواتير مدفوعة</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center p-3" style="background: #fff3cd; border-radius: 10px;">
                        <h4 style="color: #ffc107;">5</h4>
                        <small>فواتير معلقة</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center p-3" style="background: #f8d7da; border-radius: 10px;">
                        <h4 style="color: #dc3545;">2</h4>
                        <small>فواتير متأخرة</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center p-3" style="background: #d1ecf1; border-radius: 10px;">
                        <h4 style="color: #17a2b8;">$15,420</h4>
                        <small>إجمالي الإيرادات</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

# قالب إنشاء فاتورة جديدة
CREATE_INVOICE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء فاتورة جديدة - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .invoice-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 2rem 0;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            color: white;
        }

        .calculation-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .total-amount {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="{{ url_for('invoices') }}" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة لقائمة الفواتير
        </a>

        <div class="invoice-container">
            <h1 class="text-center mb-4" style="color: #667eea;">
                <i class="fas fa-plus me-3"></i>
                إنشاء فاتورة جديدة
            </h1>

            <form method="POST">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">العميل</label>
                        <select class="form-control" name="user_id" required>
                            <option value="">اختر العميل</option>
                            <option value="1">أحمد علي محمد</option>
                            <option value="2">سارة حسن أحمد</option>
                            <option value="3">عمر خليل إبراهيم</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الاشتراك (اختياري)</label>
                        <select class="form-control" name="subscription_id">
                            <option value="">اختر الاشتراك</option>
                            <option value="1">خادم ويب رئيسي - AdenLink</option>
                            <option value="2">خادم التطبيقات - AWS</option>
                            <option value="3">قاعدة بيانات - Azure</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">تاريخ الإصدار</label>
                        <input type="date" class="form-control" name="issue_date" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">تاريخ الاستحقاق</label>
                        <input type="date" class="form-control" name="due_date" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">المبلغ الفرعي</label>
                        <input type="number" class="form-control" name="subtotal" step="0.01" required id="subtotal">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">نسبة الضريبة (%)</label>
                        <input type="number" class="form-control" name="tax_rate" step="0.01" value="0" id="taxRate">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">مبلغ الخصم</label>
                        <input type="number" class="form-control" name="discount_amount" step="0.01" value="0" id="discount">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">العملة</label>
                        <select class="form-control" name="currency">
                            <option value="USD">دولار أمريكي (USD)</option>
                            <option value="EUR">يورو (EUR)</option>
                            <option value="IQD">دينار عراقي (IQD)</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">طريقة الدفع</label>
                        <select class="form-control" name="payment_method_id">
                            <option value="">اختر طريقة الدفع</option>
                            <option value="1">بطاقة ائتمان</option>
                            <option value="2">PayPal</option>
                            <option value="3">تحويل بنكي</option>
                            <option value="4">نقداً</option>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-control" name="notes" rows="3"></textarea>
                </div>

                <!-- قسم الحسابات -->
                <div class="calculation-section">
                    <h5 class="mb-3">حساب المبلغ الإجمالي</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <p><strong>المبلغ الفرعي:</strong></p>
                            <p id="displaySubtotal">$0.00</p>
                        </div>
                        <div class="col-md-3">
                            <p><strong>مبلغ الضريبة:</strong></p>
                            <p id="displayTax">$0.00</p>
                        </div>
                        <div class="col-md-3">
                            <p><strong>مبلغ الخصم:</strong></p>
                            <p id="displayDiscount">$0.00</p>
                        </div>
                        <div class="col-md-3">
                            <p><strong>المبلغ الإجمالي:</strong></p>
                            <p class="total-amount" id="displayTotal">$0.00</p>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        إنشاء الفاتورة
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // حساب المبلغ الإجمالي تلقائياً
        function calculateTotal() {
            const subtotal = parseFloat(document.getElementById('subtotal').value) || 0;
            const taxRate = parseFloat(document.getElementById('taxRate').value) || 0;
            const discount = parseFloat(document.getElementById('discount').value) || 0;

            const taxAmount = (subtotal * taxRate) / 100;
            const total = subtotal + taxAmount - discount;

            document.getElementById('displaySubtotal').textContent = '$' + subtotal.toFixed(2);
            document.getElementById('displayTax').textContent = '$' + taxAmount.toFixed(2);
            document.getElementById('displayDiscount').textContent = '$' + discount.toFixed(2);
            document.getElementById('displayTotal').textContent = '$' + total.toFixed(2);
        }

        // ربط الأحداث
        document.getElementById('subtotal').addEventListener('input', calculateTotal);
        document.getElementById('taxRate').addEventListener('input', calculateTotal);
        document.getElementById('discount').addEventListener('input', calculateTotal);

        // تعيين التاريخ الحالي كافتراضي
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.querySelector('input[name="issue_date"]').value = today;

            const nextMonth = new Date();
            nextMonth.setMonth(nextMonth.getMonth() + 1);
            document.querySelector('input[name="due_date"]').value = nextMonth.toISOString().split('T')[0];
        });
    </script>
</body>
</html>
'''

# قالب تقارير الاشتراكات
SUBSCRIPTION_REPORTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الاشتراكات - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .reports-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 2rem 0;
        }

        .report-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .report-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .report-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table th {
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem;
        }

        .table td {
            padding: 1rem;
            border-color: #e9ecef;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }

        .status-expiring {
            background: #fff3cd;
            color: #856404;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            color: white;
        }

        .export-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 0.5rem 1rem;
            color: white;
            margin: 0 0.25rem;
            transition: all 0.3s ease;
        }

        .export-btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للوحة التحكم
        </a>

        <div class="reports-container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 style="color: #667eea;">
                    <i class="fas fa-file-alt me-3"></i>
                    تقارير الاشتراكات
                </h1>
                <div>
                    <button class="export-btn">
                        <i class="fas fa-download me-1"></i>
                        تصدير Excel
                    </button>
                    <button class="export-btn">
                        <i class="fas fa-file-pdf me-1"></i>
                        تصدير PDF
                    </button>
                    <button class="export-btn">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                </div>
            </div>

            <!-- ملخص الإحصائيات -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="report-card text-center">
                        <h3 style="color: #667eea;">25</h3>
                        <p class="mb-0">إجمالي الاشتراكات</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="report-card text-center">
                        <h3 style="color: #28a745;">18</h3>
                        <p class="mb-0">اشتراكات نشطة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="report-card text-center">
                        <h3 style="color: #ffc107;">3</h3>
                        <p class="mb-0">تنتهي قريباً</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="report-card text-center">
                        <h3 style="color: #dc3545;">4</h3>
                        <p class="mb-0">منتهية</p>
                    </div>
                </div>
            </div>

            <!-- جدول تفصيلي للاشتراكات -->
            <div class="report-table">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>اسم الاشتراك</th>
                            <th>العميل</th>
                            <th>المزود</th>
                            <th>السعر</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الحالة</th>
                            <th>الأيام المتبقية</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>خادم ويب رئيسي</strong></td>
                            <td>أحمد علي محمد</td>
                            <td>AdenLink</td>
                            <td>$1,200.00</td>
                            <td>2024-01-01</td>
                            <td>2024-12-31</td>
                            <td><span class="status-badge status-active">نشط</span></td>
                            <td>158 يوم</td>
                        </tr>
                        <tr>
                            <td><strong>خادم التطبيقات</strong></td>
                            <td>سارة حسن أحمد</td>
                            <td>AWS</td>
                            <td>$150.00</td>
                            <td>2024-06-01</td>
                            <td>2024-07-01</td>
                            <td><span class="status-badge status-expiring">ينتهي قريباً</span></td>
                            <td>7 أيام</td>
                        </tr>
                        <tr>
                            <td><strong>قاعدة بيانات</strong></td>
                            <td>عمر خليل إبراهيم</td>
                            <td>Azure</td>
                            <td>$600.00</td>
                            <td>2024-03-01</td>
                            <td>2024-09-01</td>
                            <td><span class="status-badge status-active">نشط</span></td>
                            <td>39 يوم</td>
                        </tr>
                        <tr>
                            <td><strong>خدمة التخزين</strong></td>
                            <td>أحمد علي محمد</td>
                            <td>Google Cloud</td>
                            <td>$75.00</td>
                            <td>2024-05-01</td>
                            <td>2024-08-01</td>
                            <td><span class="status-badge status-active">نشط</span></td>
                            <td>8 أيام</td>
                        </tr>
                        <tr>
                            <td><strong>خادم تطوير</strong></td>
                            <td>سارة حسن أحمد</td>
                            <td>DigitalOcean</td>
                            <td>$50.00</td>
                            <td>2024-01-01</td>
                            <td>2024-06-01</td>
                            <td><span class="status-badge status-expired">منتهي</span></td>
                            <td>-23 يوم</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- تحليل الإيرادات -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="report-card">
                        <h5 class="mb-3">تحليل الإيرادات الشهرية</h5>
                        <div class="row">
                            <div class="col-6">
                                <p><strong>يناير:</strong> $1,200</p>
                                <p><strong>فبراير:</strong> $1,350</p>
                                <p><strong>مارس:</strong> $1,800</p>
                            </div>
                            <div class="col-6">
                                <p><strong>أبريل:</strong> $1,950</p>
                                <p><strong>مايو:</strong> $2,025</p>
                                <p><strong>يونيو:</strong> $2,450</p>
                            </div>
                        </div>
                        <hr>
                        <p class="mb-0"><strong>إجمالي الإيرادات:</strong> <span style="color: #667eea; font-size: 1.2rem;">$10,775</span></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="report-card">
                        <h5 class="mb-3">توزيع المزودين</h5>
                        <div class="row">
                            <div class="col-6">
                                <p><strong>AdenLink:</strong> 8 اشتراكات</p>
                                <p><strong>AWS:</strong> 6 اشتراكات</p>
                                <p><strong>Azure:</strong> 4 اشتراكات</p>
                            </div>
                            <div class="col-6">
                                <p><strong>Google Cloud:</strong> 4 اشتراكات</p>
                                <p><strong>DigitalOcean:</strong> 2 اشتراكات</p>
                                <p><strong>أخرى:</strong> 1 اشتراك</p>
                            </div>
                        </div>
                        <hr>
                        <p class="mb-0"><strong>أكثر المزودين استخداماً:</strong> <span style="color: #667eea;">AdenLink</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''





if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام إدارة الاشتراكات المتقدم...")
    print("=" * 60)
    
    # إنشاء الجداول وتهيئة البيانات
    create_tables()
    initialize_data()
    
    print("\n🌐 معلومات الوصول:")
    print("   🔗 الرابط: http://localhost:5090")
    print("   👤 اسم المستخدم: admin")
    print("   🔑 كلمة المرور: 123456")
    print("=" * 60)

    # تشغيل التطبيق
    app.run(debug=True, host='0.0.0.0', port=5090)
