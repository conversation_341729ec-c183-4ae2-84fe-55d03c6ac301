{% extends "base.html" %}

{% block title %}تحليلات الاشتراكات - نظام إدارة الاشتراكات{% endblock %}

{% block extra_css %}
<style>
.analytics-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.analytics-title {
    color: #00f5ff;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 0 20px #00f5ff;
    margin-bottom: 10px;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.overview-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 25px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #00f5ff, #bf00ff);
}

.overview-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.overview-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #00f5ff, #bf00ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.overview-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #00f5ff;
    text-shadow: 0 0 10px #00f5ff;
    margin-bottom: 10px;
}

.overview-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.chart-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.chart-title {
    color: #bf00ff;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 0 10px #bf00ff;
}

.chart-container {
    position: relative;
    height: 300px;
}

.spending-summary {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.spending-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.spending-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.spending-amount {
    font-size: 1.8rem;
    font-weight: 700;
    color: #00f5ff;
    text-shadow: 0 0 10px #00f5ff;
}

.spending-label {
    color: rgba(255, 255, 255, 0.7);
    margin-top: 5px;
}

.expiry-alerts {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid rgba(255, 0, 0, 0.3);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
}

.alert-title {
    color: #ff6b6b;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.alert-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-content {
    flex: 1;
}

.alert-name {
    color: white;
    font-weight: 600;
    margin-bottom: 5px;
}

.alert-date {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.alert-days {
    color: #ff6b6b;
    font-weight: 600;
    font-size: 1.1rem;
}

.no-data {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    padding: 40px;
    font-size: 1.1rem;
}

@media (max-width: 768px) {
    .analytics-title {
        font-size: 2rem;
    }
    
    .stats-overview {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .spending-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .chart-container {
        height: 250px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="analytics-header">
        <h1 class="analytics-title">
            <i class="fas fa-chart-line me-3"></i>
            تحليلات الاشتراكات
        </h1>
        <p style="color: rgba(255, 255, 255, 0.7); font-size: 1.1rem;">
            نظرة شاملة على إحصائيات وتحليلات اشتراكاتك
        </p>
    </div>
    
    <!-- Stats Overview -->
    <div class="stats-overview">
        <div class="overview-card hologram-card">
            <div class="overview-icon">
                <i class="fas fa-subscription"></i>
            </div>
            <div class="overview-number">{{ data.total_subscriptions }}</div>
            <div class="overview-label">إجمالي الاشتراكات</div>
        </div>
        
        <div class="overview-card hologram-card">
            <div class="overview-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="overview-number">{{ data.active_subscriptions }}</div>
            <div class="overview-label">الاشتراكات النشطة</div>
        </div>
        
        <div class="overview-card hologram-card">
            <div class="overview-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="overview-number">${{ "%.2f"|format(data.estimated_monthly_cost) }}</div>
            <div class="overview-label">التكلفة الشهرية المقدرة</div>
        </div>
        
        <div class="overview-card hologram-card">
            <div class="overview-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="overview-number">{{ data.upcoming_expiry|length }}</div>
            <div class="overview-label">تنتهي خلال 30 يوم</div>
        </div>
    </div>
    
    <!-- Charts -->
    <div class="charts-grid">
        <!-- Categories Chart -->
        <div class="chart-card">
            <h3 class="chart-title">
                <i class="fas fa-chart-pie me-2"></i>
                توزيع الاشتراكات حسب الفئة
            </h3>
            <div class="chart-container">
                <canvas id="categoriesChart"></canvas>
            </div>
        </div>
        
        <!-- Providers Chart -->
        <div class="chart-card">
            <h3 class="chart-title">
                <i class="fas fa-chart-bar me-2"></i>
                أهم المزودين
            </h3>
            <div class="chart-container">
                <canvas id="providersChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Spending Summary -->
    <div class="spending-summary">
        <h3 style="color: #bf00ff; margin-bottom: 20px; text-align: center;">
            <i class="fas fa-calculator me-2"></i>
            ملخص الإنفاق
        </h3>
        
        <div class="spending-grid">
            <div class="spending-item">
                <div class="spending-amount">${{ "%.2f"|format(data.monthly_spending) }}</div>
                <div class="spending-label">الاشتراكات الشهرية</div>
            </div>
            
            <div class="spending-item">
                <div class="spending-amount">${{ "%.2f"|format(data.yearly_spending) }}</div>
                <div class="spending-label">الاشتراكات السنوية</div>
            </div>
            
            <div class="spending-item">
                <div class="spending-amount">${{ "%.2f"|format(data.yearly_spending / 12) }}</div>
                <div class="spending-label">السنوية (شهرياً)</div>
            </div>
            
            <div class="spending-item">
                <div class="spending-amount">${{ "%.2f"|format(data.estimated_monthly_cost * 12) }}</div>
                <div class="spending-label">التكلفة السنوية المقدرة</div>
            </div>
        </div>
    </div>
    
    <!-- Expiry Alerts -->
    {% if data.upcoming_expiry %}
    <div class="expiry-alerts">
        <h3 class="alert-title">
            <i class="fas fa-exclamation-triangle me-2"></i>
            اشتراكات تنتهي قريباً
        </h3>
        
        {% for subscription in data.upcoming_expiry %}
        <div class="alert-item">
            <div class="alert-content">
                <div class="alert-name">{{ subscription.name }}</div>
                <div class="alert-date">ينتهي في {{ subscription.end_date.strftime('%Y-%m-%d') }}</div>
            </div>
            <div class="alert-days">
                {{ subscription.days_until_expiry() }} يوم
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if data.total_subscriptions == 0 %}
    <div class="no-data">
        <i class="fas fa-chart-line" style="font-size: 4rem; margin-bottom: 20px; opacity: 0.3;"></i>
        <h3>لا توجد بيانات للتحليل</h3>
        <p>أضف بعض الاشتراكات لعرض التحليلات والإحصائيات</p>
        <a href="{{ url_for('add_subscription') }}" class="btn btn-primary btn-lg crystal-btn">
            <i class="fas fa-plus me-2"></i>
            إضافة اشتراك
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // بيانات الفئات
    const categoriesData = {
        labels: [
            {% for category in data.categories %}
                '{% if category.category == "streaming" %}خدمات البث{% elif category.category == "software" %}برمجيات{% elif category.category == "hosting" %}استضافة{% elif category.category == "vpn" %}VPN{% elif category.category == "cloud" %}خدمات سحابية{% else %}أخرى{% endif %}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            data: [{% for category in data.categories %}{{ category.count }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                'rgba(0, 245, 255, 0.8)',
                'rgba(191, 0, 255, 0.8)',
                'rgba(255, 0, 128, 0.8)',
                'rgba(102, 126, 234, 0.8)',
                'rgba(118, 75, 162, 0.8)',
                'rgba(240, 147, 251, 0.8)'
            ],
            borderColor: [
                'rgba(0, 245, 255, 1)',
                'rgba(191, 0, 255, 1)',
                'rgba(255, 0, 128, 1)',
                'rgba(102, 126, 234, 1)',
                'rgba(118, 75, 162, 1)',
                'rgba(240, 147, 251, 1)'
            ],
            borderWidth: 2
        }]
    };
    
    // بيانات المزودين
    const providersData = {
        labels: [{% for provider in data.providers %}'{{ provider.provider }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'عدد الاشتراكات',
            data: [{% for provider in data.providers %}{{ provider.count }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: 'rgba(0, 245, 255, 0.6)',
            borderColor: 'rgba(0, 245, 255, 1)',
            borderWidth: 2,
            borderRadius: 10
        }]
    };
    
    // رسم بياني للفئات
    const categoriesCtx = document.getElementById('categoriesChart');
    if (categoriesCtx && {{ data.categories|length }} > 0) {
        new Chart(categoriesCtx, {
            type: 'doughnut',
            data: categoriesData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: 'white',
                            padding: 20,
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });
    }
    
    // رسم بياني للمزودين
    const providersCtx = document.getElementById('providersChart');
    if (providersCtx && {{ data.providers|length }} > 0) {
        new Chart(providersCtx, {
            type: 'bar',
            data: providersData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            stepSize: 1
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            maxRotation: 45
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    }
    
    // تأثير الظهور التدريجي
    const cards = document.querySelectorAll('.overview-card, .chart-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
