{% extends "base.html" %}

{% block title %}{{ subscription.name }} - تفاصيل الاشتراك{% endblock %}

{% block extra_css %}
<style>
.subscription-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.subscription-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
}

.subscription-title {
    color: #00f5ff;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 0 20px #00f5ff;
    margin-bottom: 10px;
}

.subscription-provider {
    color: #bf00ff;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.subscription-status {
    display: inline-block;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-active {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: white;
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.5);
}

.status-suspended {
    background: linear-gradient(135deg, #fa709a, #fee140);
    color: white;
    box-shadow: 0 0 20px rgba(250, 112, 154, 0.5);
}

.status-expired {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.detail-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.detail-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #00f5ff, #bf00ff);
}

.detail-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.card-title {
    color: #bf00ff;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    text-shadow: 0 0 10px #bf00ff;
}

.card-title i {
    margin-left: 10px;
    font-size: 1.1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.detail-value {
    color: white;
    font-weight: 600;
    text-align: left;
}

.price-display {
    font-size: 1.5rem;
    color: #00f5ff;
    text-shadow: 0 0 10px #00f5ff;
}

.days-remaining {
    font-size: 1.3rem;
    font-weight: 700;
}

.days-warning {
    color: #ff6b6b;
    text-shadow: 0 0 10px #ff6b6b;
}

.days-normal {
    color: #4facfe;
    text-shadow: 0 0 10px #4facfe;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 12px 25px;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    border: none;
    cursor: pointer;
}

.btn-edit {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-delete {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-back {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    color: white;
    text-decoration: none;
}

.description-card {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.tag {
    background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(191, 0, 255, 0.2));
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 0.9rem;
    color: #00f5ff;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin-top: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    border-radius: 10px;
    transition: width 0.3s ease;
}

@media (max-width: 768px) {
    .subscription-title {
        font-size: 2rem;
    }
    
    .details-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .detail-value {
        text-align: right;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="subscription-header">
        <h1 class="subscription-title">
            <i class="fas fa-subscription me-3"></i>
            {{ subscription.name }}
        </h1>
        <div class="subscription-provider">
            <i class="fas fa-building me-2"></i>
            {{ subscription.provider }}
        </div>
        <div class="subscription-status status-{{ subscription.status }}">
            {% if subscription.status == 'active' %}
                <i class="fas fa-check-circle me-2"></i>نشط
            {% elif subscription.status == 'suspended' %}
                <i class="fas fa-pause-circle me-2"></i>معلق
            {% elif subscription.status == 'expired' %}
                <i class="fas fa-times-circle me-2"></i>منتهي
            {% endif %}
        </div>
    </div>
    
    <!-- Details Grid -->
    <div class="details-grid">
        <!-- Basic Information -->
        <div class="detail-card hologram-card">
            <h3 class="card-title">
                <i class="fas fa-info-circle"></i>
                المعلومات الأساسية
            </h3>
            
            <div class="detail-item">
                <span class="detail-label">الفئة</span>
                <span class="detail-value">
                    {% if subscription.category == 'streaming' %}خدمات البث
                    {% elif subscription.category == 'software' %}برمجيات
                    {% elif subscription.category == 'hosting' %}استضافة
                    {% elif subscription.category == 'vpn' %}VPN
                    {% elif subscription.category == 'cloud' %}خدمات سحابية
                    {% else %}أخرى{% endif %}
                </span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">الأولوية</span>
                <span class="detail-value">
                    {% if subscription.priority == 'low' %}منخفضة
                    {% elif subscription.priority == 'medium' %}متوسطة
                    {% elif subscription.priority == 'high' %}عالية
                    {% else %}حرجة{% endif %}
                </span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">تاريخ الإنشاء</span>
                <span class="detail-value">{{ subscription.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">التجديد التلقائي</span>
                <span class="detail-value">
                    {% if subscription.auto_renewal %}
                        <i class="fas fa-check text-success"></i> مفعل
                    {% else %}
                        <i class="fas fa-times text-danger"></i> معطل
                    {% endif %}
                </span>
            </div>
        </div>
        
        <!-- Subscription Details -->
        <div class="detail-card hologram-card">
            <h3 class="card-title">
                <i class="fas fa-credit-card"></i>
                تفاصيل الاشتراك
            </h3>
            
            <div class="detail-item">
                <span class="detail-label">النوع</span>
                <span class="detail-value">
                    {% if subscription.subscription_type == 'monthly' %}شهري
                    {% elif subscription.subscription_type == 'yearly' %}سنوي
                    {% else %}مدى الحياة{% endif %}
                </span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">السعر</span>
                <span class="detail-value price-display">
                    ${{ "%.2f"|format(subscription.price) }} {{ subscription.currency }}
                </span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">تاريخ البداية</span>
                <span class="detail-value">{{ subscription.start_date.strftime('%Y-%m-%d') }}</span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">تاريخ النهاية</span>
                <span class="detail-value">{{ subscription.end_date.strftime('%Y-%m-%d') }}</span>
            </div>
        </div>
        
        <!-- Time Remaining -->
        <div class="detail-card hologram-card">
            <h3 class="card-title">
                <i class="fas fa-clock"></i>
                الوقت المتبقي
            </h3>
            
            <div class="detail-item">
                <span class="detail-label">الأيام المتبقية</span>
                <span class="detail-value days-remaining {% if subscription.days_until_expiry() <= 7 %}days-warning{% else %}days-normal{% endif %}">
                    {% set days = subscription.days_until_expiry() %}
                    {% if days > 0 %}
                        {{ days }} يوم
                    {% else %}
                        منتهي
                    {% endif %}
                </span>
            </div>
            
            {% if subscription.days_until_expiry() > 0 %}
            <div class="progress-bar">
                {% set total_days = (subscription.end_date - subscription.start_date).days %}
                {% set remaining_days = subscription.days_until_expiry() %}
                {% set progress = ((total_days - remaining_days) / total_days * 100) if total_days > 0 else 0 %}
                <div class="progress-fill" style="width: {{ progress }}%"></div>
            </div>
            <small style="color: rgba(255, 255, 255, 0.6); margin-top: 5px; display: block;">
                {{ "%.1f"|format(progress) }}% من فترة الاشتراك
            </small>
            {% endif %}
        </div>
        
        <!-- Server Information -->
        {% if subscription.server_name or subscription.server_ip or subscription.api_key %}
        <div class="detail-card hologram-card">
            <h3 class="card-title">
                <i class="fas fa-server"></i>
                معلومات السيرفر
            </h3>
            
            {% if subscription.server_name %}
            <div class="detail-item">
                <span class="detail-label">اسم السيرفر</span>
                <span class="detail-value">{{ subscription.server_name }}</span>
            </div>
            {% endif %}
            
            {% if subscription.server_ip %}
            <div class="detail-item">
                <span class="detail-label">عنوان IP</span>
                <span class="detail-value">{{ subscription.server_ip }}</span>
            </div>
            {% endif %}
            
            {% if subscription.port %}
            <div class="detail-item">
                <span class="detail-label">البورت</span>
                <span class="detail-value">{{ subscription.port }}</span>
            </div>
            {% endif %}
            
            {% if subscription.username %}
            <div class="detail-item">
                <span class="detail-label">اسم المستخدم</span>
                <span class="detail-value">{{ subscription.username }}</span>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
    
    <!-- Description and Notes -->
    {% if subscription.description or subscription.notes %}
    <div class="detail-card">
        {% if subscription.description %}
        <h4 style="color: #00f5ff; margin-bottom: 15px;">
            <i class="fas fa-align-left me-2"></i>
            الوصف
        </h4>
        <div class="description-card">
            {{ subscription.description }}
        </div>
        {% endif %}
        
        {% if subscription.notes %}
        <h4 style="color: #00f5ff; margin-bottom: 15px; margin-top: 25px;">
            <i class="fas fa-sticky-note me-2"></i>
            الملاحظات
        </h4>
        <div class="description-card">
            {{ subscription.notes }}
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- Tags -->
    {% if subscription.tags %}
    <div class="detail-card">
        <h4 style="color: #00f5ff; margin-bottom: 15px;">
            <i class="fas fa-tags me-2"></i>
            العلامات
        </h4>
        <div class="tags-container">
            {% for tag in subscription.tags.split(',') %}
                <span class="tag">{{ tag.strip() }}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{{ url_for('edit_subscription', id=subscription.id) }}" class="action-btn btn-edit crystal-btn ripple">
            <i class="fas fa-edit"></i>
            تعديل الاشتراك
        </a>
        
        <a href="{{ url_for('delete_subscription', id=subscription.id) }}" 
           class="action-btn btn-delete crystal-btn delete-btn"
           data-message="هل أنت متأكد من حذف اشتراك {{ subscription.name }}؟">
            <i class="fas fa-trash"></i>
            حذف الاشتراك
        </a>
        
        <a href="{{ url_for('subscriptions') }}" class="action-btn btn-back crystal-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للقائمة
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الظهور التدريجي للبطاقات
    const cards = document.querySelectorAll('.detail-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
    
    // تحديث شريط التقدم
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
        const targetWidth = progressFill.style.width;
        progressFill.style.width = '0%';
        
        setTimeout(() => {
            progressFill.style.width = targetWidth;
        }, 1000);
    }
});
</script>
{% endblock %}
