#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات المتطور
مطور بواسطة: المهندس محمد ياسر الجبيري
الإصدار: 1.0.0
"""

import os
import logging
from datetime import datetime, timedelta
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from flask_wtf import FlaskForm
from flask_wtf.csrf import CSRFProtect
from werkzeug.security import generate_password_hash, check_password_hash
from wtforms import StringField, PasswordField, SelectField, TextAreaField, FloatField, DateField, BooleanField
from wtforms.validators import DataRequired, Email, Length, NumberRange

# إعدادات التطبيق
class Config:
    SQLALCHEMY_DATABASE_URI = 'sqlite:///subscription_manager.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SECRET_KEY = 'your-secret-key-here-change-in-production'
    WTF_CSRF_ENABLED = True

# إنشاء التطبيق
app = Flask(__name__)
app.config.from_object(Config)

# إنشاء قاعدة البيانات والإضافات
db = SQLAlchemy(app)
login_manager = LoginManager(app)
csrf = CSRFProtect(app)

# إعدادات نظام تسجيل الدخول
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user', nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

class Subscription(db.Model):
    __tablename__ = 'subscriptions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    provider = db.Column(db.String(50), nullable=False)
    category = db.Column(db.String(30), nullable=False)
    server_name = db.Column(db.String(100))
    server_ip = db.Column(db.String(45))
    api_key = db.Column(db.String(255))
    port = db.Column(db.Integer)
    username = db.Column(db.String(100))
    password = db.Column(db.String(255))
    subscription_type = db.Column(db.String(20), nullable=False)
    price = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='active', nullable=False)
    auto_renewal = db.Column(db.Boolean, default=True)
    priority = db.Column(db.String(10), default='medium')
    notes = db.Column(db.Text)
    tags = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def days_until_expiry(self):
        return (self.end_date - datetime.now().date()).days

class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')
    status = db.Column(db.String(20), default='pending', nullable=False)  # pending, paid, cancelled
    issue_date = db.Column(db.Date, default=datetime.utcnow, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.Date)
    payment_method = db.Column(db.String(50))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # العلاقات
    subscription = db.relationship('Subscription', backref='invoices')
    user = db.relationship('User', backref='invoices')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# إنشاء قاعدة البيانات
with app.app_context():
    db.create_all()
    
    # إنشاء المستخدم الافتراضي
    admin_user = User.query.filter_by(username='admin').first()
    if not admin_user:
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            full_name='المدير العام',
            role='admin'
        )
        admin_user.set_password('123456')
        db.session.add(admin_user)
        db.session.commit()
        print("تم إنشاء المستخدم الافتراضي: admin / 123456")

# الصفحة الرئيسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('welcome'))

@app.route('/welcome')
def welcome():
    return render_template('welcome.html')

# نظام المصادقة
class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)])
    remember_me = BooleanField('تذكرني')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()

        if user and user.check_password(form.password.data):
            login_user(user, remember=form.remember_me.data)
            flash('تم تسجيل الدخول بنجاح!', 'success')

            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة.', 'error')

    return render_template('auth/simple_login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('welcome'))

# لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    # إحصائيات سريعة
    total_subscriptions = Subscription.query.count()
    active_subscriptions = Subscription.query.filter_by(status='active').count()
    total_invoices = Invoice.query.count()
    pending_invoices = Invoice.query.filter_by(status='pending').count()
    
    # الاشتراكات المنتهية قريباً (خلال 7 أيام)
    upcoming_expiry = Subscription.query.filter(
        Subscription.end_date <= datetime.now() + timedelta(days=7),
        Subscription.status == 'active'
    ).all()
    
    # آخر الأنشطة
    recent_subscriptions = Subscription.query.order_by(Subscription.created_at.desc()).limit(5).all()
    recent_invoices = Invoice.query.order_by(Invoice.created_at.desc()).limit(5).all()
    
    stats = {
        'total_subscriptions': total_subscriptions,
        'active_subscriptions': active_subscriptions,
        'total_invoices': total_invoices,
        'pending_invoices': pending_invoices,
        'upcoming_expiry': len(upcoming_expiry),
        'total_revenue': sum([inv.amount for inv in Invoice.query.filter_by(status='paid').all()])
    }
    
    return render_template('simple_dashboard.html',
                         stats=stats,
                         upcoming_expiry=upcoming_expiry,
                         recent_subscriptions=recent_subscriptions,
                         recent_invoices=recent_invoices)

# سيتم إضافة الوظائف المتقدمة لاحقاً

# صفحات مبسطة للاختبار
@app.route('/subscriptions')
@login_required
def subscriptions():
    return "<h1 style='color: white; text-align: center; margin-top: 100px;'>صفحة الاشتراكات - قيد التطوير</h1>"

@app.route('/subscriptions/add')
@login_required
def add_subscription():
    return "<h1 style='color: white; text-align: center; margin-top: 100px;'>إضافة اشتراك جديد - قيد التطوير</h1>"

@app.route('/subscription_analytics')
@login_required
def subscription_analytics():
    return "<h1 style='color: white; text-align: center; margin-top: 100px;'>التحليلات والتقارير - قيد التطوير</h1>"

@app.route('/send_email')
@login_required
def send_email():
    return "<h1 style='color: white; text-align: center; margin-top: 100px;'>مركز التواصل - قيد التطوير</h1>"

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
