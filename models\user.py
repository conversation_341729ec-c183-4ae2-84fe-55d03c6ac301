#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج المستخدم
"""

from datetime import datetime, timed<PERSON>ta
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

# استيراد db من app.py سيتم لاحقاً
db = None

class User(UserMixin, db.Model):
    """نموذج المستخدم"""
    
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user', nullable=False)  # admin, user
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    
    # معلومات إضافية
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    avatar = db.Column(db.String(255))
    
    # تواريخ مهمة
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # إعدادات الأمان
    failed_login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime)
    
    # إعدادات المستخدم
    language = db.Column(db.String(5), default='ar')
    timezone = db.Column(db.String(50), default='Asia/Baghdad')
    email_notifications = db.Column(db.Boolean, default=True)
    
    # العلاقات
    subscriptions = db.relationship('Subscription', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    invoices = db.relationship('Invoice', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    notifications = db.relationship('Notification', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(User, self).__init__(**kwargs)
    
    def set_password(self, password):
        """تشفير كلمة المرور"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        """التحقق من صلاحيات المدير"""
        return self.role == 'admin'
    
    def is_locked(self):
        """التحقق من حالة القفل"""
        if self.locked_until:
            return datetime.utcnow() < self.locked_until
        return False
    
    def lock_account(self, duration_minutes=15):
        """قفل الحساب لفترة محددة"""
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        self.failed_login_attempts = 0
    
    def unlock_account(self):
        """إلغاء قفل الحساب"""
        self.locked_until = None
        self.failed_login_attempts = 0
    
    def increment_failed_login(self):
        """زيادة عدد محاولات تسجيل الدخول الفاشلة"""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:
            self.lock_account()
    
    def reset_failed_login(self):
        """إعادة تعيين محاولات تسجيل الدخول الفاشلة"""
        self.failed_login_attempts = 0
        self.last_login = datetime.utcnow()
    
    def get_active_subscriptions(self):
        """الحصول على الاشتراكات النشطة"""
        return self.subscriptions.filter_by(status='active').all()
    
    def get_total_spent(self):
        """إجمالي المبلغ المدفوع"""
        return sum([inv.amount for inv in self.invoices.filter_by(status='paid').all()])
    
    def to_dict(self):
        """تحويل البيانات إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'role': self.role,
            'is_active': self.is_active,
            'phone': self.phone,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'language': self.language,
            'timezone': self.timezone
        }
    
    def __repr__(self):
        return f'<User {self.username}>'
