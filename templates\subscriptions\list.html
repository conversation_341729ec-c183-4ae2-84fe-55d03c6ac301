{% extends "base.html" %}

{% block title %}الاشتراكات - نظام إدارة الاشتراكات{% endblock %}

{% block extra_css %}
<style>
.subscriptions-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.subscriptions-title {
    color: #00f5ff;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 0 20px #00f5ff;
    margin-bottom: 10px;
}

.filters-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: 15px;
    align-items: end;
}

.subscription-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.subscription-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #00f5ff, #bf00ff);
}

.subscription-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.subscription-info h3 {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.subscription-provider {
    color: #00f5ff;
    font-weight: 500;
    margin-bottom: 10px;
}

.subscription-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.meta-item {
    text-align: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.meta-label {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.meta-value {
    color: white;
    font-weight: 600;
}

.subscription-status {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-align: center;
}

.status-active {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: white;
}

.status-suspended {
    background: linear-gradient(135deg, #fa709a, #fee140);
    color: white;
}

.status-expired {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.subscription-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 8px 15px;
    border-radius: 10px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-view {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-edit {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}

.btn-delete {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    color: white;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.pagination {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 10px;
    backdrop-filter: blur(20px);
}

.page-link {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    margin: 0 2px;
}

.page-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #00f5ff;
    border-color: #00f5ff;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-color: #667eea;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.3);
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .subscriptions-title {
        font-size: 2rem;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .subscription-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .subscription-meta {
        grid-template-columns: 1fr 1fr;
    }
    
    .subscription-actions {
        justify-content: center;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="subscriptions-header">
        <h1 class="subscriptions-title">
            <i class="fas fa-subscription me-3"></i>
            إدارة الاشتراكات
        </h1>
        <p style="color: rgba(255, 255, 255, 0.7); font-size: 1.1rem;">
            عرض وإدارة جميع اشتراكاتك في مكان واحد
        </p>
    </div>
    
    <!-- Filters -->
    <div class="filters-section">
        <form method="GET" class="filter-form">
            <div class="filter-row">
                <div class="form-group">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="ابحث في الاشتراكات..." value="{{ search }}">
                </div>
                
                <div class="form-group">
                    <label class="form-label">الفئة</label>
                    <select name="category" class="form-control">
                        <option value="">جميع الفئات</option>
                        <option value="streaming" {% if category == 'streaming' %}selected{% endif %}>خدمات البث</option>
                        <option value="software" {% if category == 'software' %}selected{% endif %}>برمجيات</option>
                        <option value="hosting" {% if category == 'hosting' %}selected{% endif %}>استضافة</option>
                        <option value="vpn" {% if category == 'vpn' %}selected{% endif %}>VPN</option>
                        <option value="cloud" {% if category == 'cloud' %}selected{% endif %}>خدمات سحابية</option>
                        <option value="other" {% if category == 'other' %}selected{% endif %}>أخرى</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-control">
                        <option value="">جميع الحالات</option>
                        <option value="active" {% if status == 'active' %}selected{% endif %}>نشط</option>
                        <option value="suspended" {% if status == 'suspended' %}selected{% endif %}>معلق</option>
                        <option value="expired" {% if status == 'expired' %}selected{% endif %}>منتهي</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary crystal-btn">
                        <i class="fas fa-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Add Button -->
    <div class="text-center mb-4">
        <a href="{{ url_for('add_subscription') }}" class="btn btn-success btn-lg crystal-btn ripple">
            <i class="fas fa-plus me-2"></i>
            إضافة اشتراك جديد
        </a>
    </div>
    
    <!-- Subscriptions List -->
    {% if subscriptions.items %}
        {% for subscription in subscriptions.items %}
        <div class="subscription-card hologram-card">
            <div class="subscription-header">
                <div class="subscription-info">
                    <h3>{{ subscription.name }}</h3>
                    <div class="subscription-provider">
                        <i class="fas fa-building me-2"></i>
                        {{ subscription.provider }}
                    </div>
                    {% if subscription.description %}
                    <p style="color: rgba(255, 255, 255, 0.7); margin-bottom: 0;">
                        {{ subscription.description[:100] }}{% if subscription.description|length > 100 %}...{% endif %}
                    </p>
                    {% endif %}
                </div>
                
                <div class="subscription-status status-{{ subscription.status }}">
                    {% if subscription.status == 'active' %}
                        <i class="fas fa-check-circle me-1"></i>نشط
                    {% elif subscription.status == 'suspended' %}
                        <i class="fas fa-pause-circle me-1"></i>معلق
                    {% elif subscription.status == 'expired' %}
                        <i class="fas fa-times-circle me-1"></i>منتهي
                    {% endif %}
                </div>
            </div>
            
            <div class="subscription-meta">
                <div class="meta-item">
                    <div class="meta-label">السعر</div>
                    <div class="meta-value">${{ "%.2f"|format(subscription.price) }}</div>
                </div>
                
                <div class="meta-item">
                    <div class="meta-label">النوع</div>
                    <div class="meta-value">
                        {% if subscription.subscription_type == 'monthly' %}شهري
                        {% elif subscription.subscription_type == 'yearly' %}سنوي
                        {% else %}مدى الحياة{% endif %}
                    </div>
                </div>
                
                <div class="meta-item">
                    <div class="meta-label">تاريخ الانتهاء</div>
                    <div class="meta-value">{{ subscription.end_date.strftime('%Y-%m-%d') }}</div>
                </div>
                
                <div class="meta-item">
                    <div class="meta-label">الأيام المتبقية</div>
                    <div class="meta-value">
                        {% set days = subscription.days_until_expiry() %}
                        {% if days > 0 %}
                            {{ days }} يوم
                        {% else %}
                            <span style="color: #ff6b6b;">منتهي</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="subscription-actions">
                <a href="{{ url_for('view_subscription', id=subscription.id) }}" class="action-btn btn-view">
                    <i class="fas fa-eye"></i>
                    عرض
                </a>
                <a href="{{ url_for('edit_subscription', id=subscription.id) }}" class="action-btn btn-edit">
                    <i class="fas fa-edit"></i>
                    تعديل
                </a>
                <a href="{{ url_for('delete_subscription', id=subscription.id) }}" 
                   class="action-btn btn-delete delete-btn"
                   data-message="هل أنت متأكد من حذف اشتراك {{ subscription.name }}؟">
                    <i class="fas fa-trash"></i>
                    حذف
                </a>
            </div>
        </div>
        {% endfor %}
        
        <!-- Pagination -->
        {% if subscriptions.pages > 1 %}
        <div class="pagination-container">
            <nav>
                <ul class="pagination">
                    {% if subscriptions.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('subscriptions', page=subscriptions.prev_num, search=search, category=category, status=status) }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in subscriptions.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != subscriptions.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('subscriptions', page=page_num, search=search, category=category, status=status) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if subscriptions.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('subscriptions', page=subscriptions.next_num, search=search, category=category, status=status) }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
        
    {% else %}
        <!-- Empty State -->
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-subscription"></i>
            </div>
            <h3>لا توجد اشتراكات</h3>
            <p>لم يتم العثور على أي اشتراكات تطابق معايير البحث.</p>
            <a href="{{ url_for('add_subscription') }}" class="btn btn-primary btn-lg crystal-btn">
                <i class="fas fa-plus me-2"></i>
                إضافة أول اشتراك
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الظهور التدريجي للبطاقات
    const cards = document.querySelectorAll('.subscription-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
