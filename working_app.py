#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات - نسخة عاملة 100%
مطور بواسطة: المهندس محمد ياسر الجبوري
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'subscription-manager-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///subscription_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

# نماذج قاعدة البيانات المتطورة

# نموذج المستخدم المحسن
class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user', nullable=False)  # admin, user
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    phone = db.Column(db.String(20))
    company = db.Column(db.String(100))
    avatar = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime)
    email_verified = db.Column(db.Boolean, default=False)

    # العلاقات
    subscriptions = db.relationship('Subscription', backref='user', lazy=True, cascade='all, delete-orphan')
    invoices = db.relationship('Invoice', backref='user', lazy=True, cascade='all, delete-orphan')
    notifications = db.relationship('Notification', backref='user', lazy=True, cascade='all, delete-orphan')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

# نموذج مزودي الخدمة السحابية
class CloudProvider(db.Model):
    __tablename__ = 'cloud_providers'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    logo = db.Column(db.String(255))
    website = db.Column(db.String(255))
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    subscriptions = db.relationship('Subscription', backref='provider', lazy=True)

# نموذج الاشتراكات المتطور
class Subscription(db.Model):
    __tablename__ = 'subscriptions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_providers.id'), nullable=False)

    # معلومات أساسية
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    service_type = db.Column(db.String(50))  # compute, storage, database, etc.

    # معلومات الاتصال
    api_key = db.Column(db.String(500))
    server_ip = db.Column(db.String(45))
    port = db.Column(db.Integer)
    username = db.Column(db.String(100))
    password = db.Column(db.String(255))
    endpoint_url = db.Column(db.String(255))
    region = db.Column(db.String(50))

    # معلومات الاشتراك
    subscription_type = db.Column(db.String(20), nullable=False)  # monthly, semi_annual, annual
    price = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)

    # الحالة والإعدادات
    status = db.Column(db.String(20), default='active')  # active, suspended, expired, cancelled
    auto_renewal = db.Column(db.Boolean, default=True)
    priority = db.Column(db.String(10), default='medium')  # low, medium, high, critical

    # معلومات إضافية
    notes = db.Column(db.Text)
    tags = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    invoices = db.relationship('Invoice', backref='subscription', lazy=True, cascade='all, delete-orphan')

    def days_until_expiry(self):
        from datetime import date
        return (self.end_date - date.today()).days

    def is_expiring_soon(self, days=30):
        return 0 <= self.days_until_expiry() <= days

    def get_monthly_cost(self):
        if self.subscription_type == 'monthly':
            return self.price
        elif self.subscription_type == 'semi_annual':
            return self.price / 6
        elif self.subscription_type == 'annual':
            return self.price / 12
        return 0

# نموذج طرق الدفع
class PaymentMethod(db.Model):
    __tablename__ = 'payment_methods'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(50), nullable=False)  # credit_card, paypal, bank_transfer, crypto
    processing_fee = db.Column(db.Float, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    is_active = db.Column(db.Boolean, default=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    invoices = db.relationship('Invoice', backref='payment_method', lazy=True)

# نموذج الفواتير المتطور
class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=False)
    payment_method_id = db.Column(db.Integer, db.ForeignKey('payment_methods.id'))

    # معلومات مالية
    subtotal = db.Column(db.Float, nullable=False)
    tax_rate = db.Column(db.Float, default=0.0)
    tax_amount = db.Column(db.Float, default=0.0)
    discount_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')

    # التواريخ
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.Date)

    # الحالة
    status = db.Column(db.String(20), default='pending')  # pending, paid, overdue, cancelled
    payment_status = db.Column(db.String(20), default='unpaid')  # unpaid, partial, paid, refunded

    # معلومات إضافية
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, **kwargs):
        super(Invoice, self).__init__(**kwargs)
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()

    def generate_invoice_number(self):
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        return f'INV-{timestamp}'

    def calculate_total(self):
        self.tax_amount = self.subtotal * (self.tax_rate / 100)
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        return self.total_amount

    def is_overdue(self):
        from datetime import date
        return self.status == 'pending' and self.due_date < date.today()

# نموذج الإشعارات
class Notification(db.Model):
    __tablename__ = 'notifications'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(50), default='info')  # info, warning, error, success
    is_read = db.Column(db.Boolean, default=False)
    action_url = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    read_at = db.Column(db.DateTime)

    def mark_as_read(self):
        self.is_read = True
        self.read_at = datetime.utcnow()
        db.session.commit()

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# قوالب HTML مدمجة
WELCOME_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

            --neon-blue: #00f5ff;
            --neon-purple: #bf00ff;
            --neon-pink: #ff0080;

            --dark-bg: #0a0a0a;
            --dark-card: #1a1a1a;
            --dark-text: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--dark-bg);
            color: var(--dark-text);
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Matrix Background Effect */
        .matrix-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e);
            opacity: 0.8;
        }

        .matrix-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, var(--neon-blue) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, var(--neon-purple) 0%, transparent 50%);
            opacity: 0.1;
            animation: matrixMove 20s ease-in-out infinite;
        }

        @keyframes matrixMove {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            33% { transform: translate(30px, -30px) rotate(120deg); }
            66% { transform: translate(-20px, 20px) rotate(240deg); }
        }

        .welcome-page {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .welcome-container {
            text-align: center;
            max-width: 800px;
            padding: 40px;
        }

        .welcome-title {
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(135deg, #00f5ff, #bf00ff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            animation: titleGlow 3s ease-in-out infinite;
        }

        @keyframes titleGlow {
            0%, 100% { filter: brightness(1); }
            50% { filter: brightness(1.2); }
        }

        .welcome-subtitle {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
        }

        .welcome-description {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 50px;
            line-height: 1.8;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 50px 0;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff);
        }

        .feature-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .feature-icon {
            font-size: 3rem;
            color: #00f5ff;
            margin-bottom: 20px;
            text-shadow: 0 0 20px #00f5ff;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: white;
            margin-bottom: 15px;
        }

        .feature-description {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
        }

        .cta-buttons {
            margin-top: 50px;
        }

        .cta-btn {
            display: inline-block;
            padding: 15px 40px;
            margin: 10px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cta-btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: 2px solid transparent;
        }

        .cta-btn-secondary {
            background: transparent;
            color: #00f5ff;
            border: 2px solid #00f5ff;
        }

        .cta-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }

        .cta-btn-primary:hover {
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .cta-btn-secondary:hover {
            background: #00f5ff;
            color: #0a0a0a;
            box-shadow: 0 15px 30px rgba(0, 245, 255, 0.4);
        }

        .developer-info {
            margin-top: 60px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .developer-name {
            color: #bf00ff;
            font-weight: 600;
            text-shadow: 0 0 10px #bf00ff;
        }

        @media (max-width: 768px) {
            .welcome-title {
                font-size: 2.5rem;
            }

            .welcome-subtitle {
                font-size: 1.2rem;
            }

            .welcome-description {
                font-size: 1rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .cta-btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body class="welcome-page">
    <!-- Matrix Background Effect -->
    <div class="matrix-background"></div>

    <div class="welcome-container">
        <h1 class="welcome-title">
            <i class="fas fa-rocket me-3"></i>
            نظام إدارة الاشتراكات
        </h1>

        <p class="welcome-subtitle">
            الحل المتطور لإدارة اشتراكاتك وفواتيرك بكفاءة عالية
        </p>

        <p class="welcome-description">
            نظام شامل ومتطور لإدارة جميع اشتراكاتك الرقمية والخدمات المختلفة،
            مع واجهة عصرية وميزات متقدمة لتتبع الفواتير والمدفوعات وإرسال التذكيرات التلقائية.
        </p>

        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-subscription"></i>
                </div>
                <h3 class="feature-title">إدارة الاشتراكات</h3>
                <p class="feature-description">
                    تتبع جميع اشتراكاتك مع تفاصيل السيرفرات والتواريخ والأسعار
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-file-invoice-dollar"></i>
                </div>
                <h3 class="feature-title">نظام الفواتير</h3>
                <p class="feature-description">
                    إنشاء وإدارة الفواتير مع تتبع المدفوعات وتصدير PDF
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="feature-title">التحليلات المتقدمة</h3>
                <p class="feature-description">
                    رسوم بيانية تفاعلية وإحصائيات مفصلة لاتخاذ قرارات ذكية
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <h3 class="feature-title">مركز التواصل</h3>
                <p class="feature-description">
                    إرسال رسائل تلقائية للعملاء مع قوالب جاهزة ومتغيرات ديناميكية
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="feature-title">الأمان المتقدم</h3>
                <p class="feature-description">
                    حماية متعددة الطبقات مع تشفير البيانات وسجل الأنشطة
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h3 class="feature-title">تصميم متجاوب</h3>
                <p class="feature-description">
                    واجهة عصرية تعمل بسلاسة على جميع الأجهزة والشاشات
                </p>
            </div>
        </div>

        <div class="cta-buttons">
            <a href="{{ url_for('login') }}" class="cta-btn cta-btn-primary">
                <i class="fas fa-sign-in-alt me-2"></i>
                تسجيل الدخول
            </a>
            <a href="#features" class="cta-btn cta-btn-secondary">
                <i class="fas fa-info-circle me-2"></i>
                المزيد من المعلومات
            </a>
        </div>

        <div class="developer-info">
            <p class="mb-2">
                <i class="fas fa-code text-primary me-2"></i>
                مطور بواسطة: <span class="developer-name">المهندس محمد ياسر الجبوري</span>
            </p>
            <p class="mb-0">
                <i class="fas fa-heart text-danger me-2"></i>
                صُنع بحب وإتقان لخدمة المجتمع التقني العربي
            </p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الكتابة التدريجية للعنوان
        const title = document.querySelector('.welcome-title');
        const text = title.textContent;
        title.textContent = '';

        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                title.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        };

        setTimeout(typeWriter, 500);

        // تأثير الظهور التدريجي للبطاقات
        const cards = document.querySelectorAll('.feature-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 1000 + (index * 200));
        });
    });
    </script>
</body>
</html>
'''

LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            max-width: 450px;
            width: 100%;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-logo {
            font-size: 4rem;
            color: #00f5ff;
            text-shadow: 0 0 30px #00f5ff;
            margin-bottom: 20px;
            animation: logoGlow 2s ease-in-out infinite alternate;
        }

        @keyframes logoGlow {
            from {
                text-shadow: 0 0 20px #00f5ff, 0 0 30px #00f5ff, 0 0 40px #00f5ff;
            }
            to {
                text-shadow: 0 0 10px #00f5ff, 0 0 20px #00f5ff, 0 0 30px #00f5ff;
            }
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 10px;
        }

        .login-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00f5ff;
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
            color: white;
            outline: none;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .input-group {
            position: relative;
        }

        .input-group-text {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-left: none;
            border-radius: 0 15px 15px 0;
            color: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
        }

        .input-group .form-control {
            border-left: none;
            border-radius: 15px 0 0 15px;
        }

        .password-toggle {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: #00f5ff;
        }

        .form-check {
            margin: 20px 0;
        }

        .form-check-input {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
        }

        .form-check-input:checked {
            background: #00f5ff;
            border-color: #00f5ff;
        }

        .form-check-label {
            color: rgba(255, 255, 255, 0.8);
            margin-right: 10px;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .demo-info {
            margin-top: 30px;
            padding: 20px;
            background: rgba(0, 245, 255, 0.1);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 15px;
            text-align: center;
        }

        .demo-title {
            color: #00f5ff;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .demo-credentials {
            color: rgba(255, 255, 255, 0.8);
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .back-link {
            text-align: center;
            margin-top: 30px;
        }

        .back-link a {
            color: #bf00ff;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-link a:hover {
            color: #ff0080;
            text-shadow: 0 0 10px #ff0080;
        }

        .error-message {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff6b6b;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .success-message {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            color: #4ade80;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .login-card {
                padding: 30px 20px;
                margin: 10px;
            }

            .login-logo {
                font-size: 3rem;
            }

            .login-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-rocket"></i>
                </div>
                <h1 class="login-title">تسجيل الدخول</h1>
                <p class="login-subtitle">مرحباً بك في نظام إدارة الاشتراكات</p>
            </div>

            <!-- عرض الرسائل -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="{{ 'error-message' if category == 'error' else 'success-message' }}">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" novalidate>
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-2"></i>
                        اسم المستخدم
                    </label>
                    <div class="input-group">
                        <input type="text" name="username" class="form-control" placeholder="أدخل اسم المستخدم" required>
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-2"></i>
                        كلمة المرور
                    </label>
                    <div class="input-group">
                        <input type="password" name="password" class="form-control" placeholder="أدخل كلمة المرور" id="password-input" required>
                        <span class="input-group-text">
                            <button type="button" class="password-toggle" data-target="#password-input">
                                <i class="fas fa-eye"></i>
                            </button>
                        </span>
                    </div>
                </div>

                <div class="form-check">
                    <input type="checkbox" name="remember_me" class="form-check-input" id="remember_me">
                    <label class="form-check-label" for="remember_me">تذكرني</label>
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </form>

            <div class="demo-info">
                <div class="demo-title">
                    <i class="fas fa-info-circle me-2"></i>
                    بيانات تجريبية
                </div>
                <div class="demo-credentials">
                    اسم المستخدم: <strong>admin</strong><br>
                    كلمة المرور: <strong>123456</strong>
                </div>
                <small style="color: rgba(255, 255, 255, 0.6);">
                    يمكنك استخدام هذه البيانات لتجربة النظام
                </small>
            </div>

            <div class="back-link">
                <a href="{{ url_for('welcome') }}">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة إلى الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي
        const card = document.querySelector('.login-card');
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px) scale(0.9)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0) scale(1)';
        }, 200);

        // تبديل إظهار كلمة المرور
        document.addEventListener('click', function(e) {
            if (e.target.closest('.password-toggle')) {
                const button = e.target.closest('.password-toggle');
                const input = document.querySelector(button.dataset.target);
                const icon = button.querySelector('i');

                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }
        });

        // تركيز تلقائي على حقل اسم المستخدم
        const usernameInput = document.querySelector('input[name="username"]');
        if (usernameInput) {
            usernameInput.focus();
        }

        // تأثير الكتابة على الحقول
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    });
    </script>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* الشريط الجانبي الأيسر */
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #bf00ff, #ff0080);
        }

        /* شعار النظام */
        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            color: #00f5ff;
            font-size: 1.8rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        /* قائمة التنقل */
        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 0;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(191, 0, 255, 0.2));
            color: #00f5ff;
            border-right: 4px solid #00f5ff;
        }

        .nav-link i {
            width: 20px;
            margin-left: 15px;
            font-size: 1.1rem;
        }

        /* معلومات المستخدم */
        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .user-name {
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            padding: 0;
            overflow-x: auto;
        }

        /* شريط التمرير الأفقي المخصص */
        .main-content::-webkit-scrollbar {
            height: 8px;
        }

        .main-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .main-content::-webkit-scrollbar-thumb {
            background: linear-gradient(90deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .main-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(90deg, #bf00ff, #ff0080);
        }

        /* الشريط العلوي */
        .top-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
        }

        .top-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .action-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        /* منطقة المحتوى */
        .content-area {
            padding: 30px;
            min-width: 1200px;
        }

        /* بطاقات الإحصائيات الأفقية */
        .stats-container {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            overflow-x: auto;
            padding-bottom: 10px;
        }

        .stats-container::-webkit-scrollbar {
            height: 6px;
        }

        .stats-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .stats-container::-webkit-scrollbar-thumb {
            background: linear-gradient(90deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .stats-card {
            min-width: 250px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .stats-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .stats-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            margin-bottom: 10px;
            animation: numberGlow 2s ease-in-out infinite alternate;
        }

        @keyframes numberGlow {
            from { text-shadow: 0 0 10px #00f5ff; }
            to { text-shadow: 0 0 20px #00f5ff, 0 0 30px #00f5ff; }
        }

        .stats-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            font-weight: 500;
        }

        /* الأقسام الأفقية */
        .horizontal-section {
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-title {
            color: #bf00ff;
            font-size: 1.5rem;
            font-weight: 600;
            text-shadow: 0 0 10px #bf00ff;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        /* قائمة أفقية للعناصر */
        .horizontal-list {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding-bottom: 15px;
        }

        .horizontal-list::-webkit-scrollbar {
            height: 6px;
        }

        .horizontal-list::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .horizontal-list::-webkit-scrollbar-thumb {
            background: linear-gradient(90deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .list-item {
            min-width: 300px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .list-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 245, 255, 0.2);
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .item-title {
            color: #00f5ff;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .item-status {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(0, 255, 0, 0.2);
            color: #4ade80;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .status-pending {
            background: rgba(255, 165, 0, 0.2);
            color: #ffa500;
            border: 1px solid rgba(255, 165, 0, 0.3);
        }

        .status-expired {
            background: rgba(255, 0, 0, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 0, 0, 0.3);
        }

        .item-details {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .item-actions {
            margin-top: 15px;
            display: flex;
            gap: 8px;
        }

        .item-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 8px;
            color: white;
            text-decoration: none;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .btn-success {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .item-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            color: white;
        }

        /* حالة فارغة */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: rgba(255, 255, 255, 0.5);
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        /* تجاوب الشاشات الصغيرة */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .content-area {
                min-width: auto;
                padding: 20px;
            }

            .stats-container {
                flex-direction: column;
            }

            .stats-card {
                min-width: auto;
            }

            .horizontal-list {
                flex-direction: column;
            }

            .list-item {
                min-width: auto;
            }
        }

        /* زر تبديل الشريط الجانبي للموبايل */
        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 10px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>

        .dashboard-header {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
        }

        .dashboard-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 10px;
        }

        .dashboard-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .stats-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .stats-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            margin-bottom: 10px;
            animation: numberGlow 2s ease-in-out infinite alternate;
        }

        @keyframes numberGlow {
            from { text-shadow: 0 0 10px #00f5ff; }
            to { text-shadow: 0 0 20px #00f5ff, 0 0 30px #00f5ff; }
        }

        .stats-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            font-weight: 500;
        }

        .quick-actions {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .quick-actions h3 {
            color: #bf00ff;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #bf00ff;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .action-btn i {
            font-size: 1.5rem;
            margin-left: 15px;
            color: #00f5ff;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 0;
        }

        .navbar-brand {
            color: #00f5ff !important;
            font-weight: 700;
            font-size: 1.5rem;
            text-shadow: 0 0 20px #00f5ff;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: #00f5ff !important;
            text-shadow: 0 0 10px #00f5ff;
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            color: #4ade80;
        }

        .alert-error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff6b6b;
        }

        .developer-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            margin-top: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .developer-name {
            color: #bf00ff;
            font-weight: 600;
            text-shadow: 0 0 10px #bf00ff;
        }

        @media (max-width: 768px) {
            .dashboard-title {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .action-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- زر تبديل الشريط الجانبي للموبايل -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- الشريط الجانبي الأيسر -->
    <div class="sidebar" id="sidebar">
        <!-- شعار النظام -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-rocket"></i>
                نظام إدارة الاشتراكات
            </div>
            <div class="sidebar-subtitle">شركة محمد الجبوري - AdenLink</div>
        </div>

        <!-- قائمة التنقل -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscriptions') }}" class="nav-link">
                    <i class="fas fa-subscription"></i>
                    إدارة الاشتراكات
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('add_subscription') }}" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    إضافة اشتراك جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('analytics') }}" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    التحليلات والتقارير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('invoices') }}" class="nav-link">
                    <i class="fas fa-file-invoice"></i>
                    إدارة الفواتير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('communication') }}" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    مركز التواصل
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscription_charts') }}" class="nav-link">
                    <i class="fas fa-chart-pie"></i>
                    مخططات الاشتراكات
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('customer_statement') }}" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    كشف حساب العملاء
                </a>
            </div>
            {% if current_user.is_admin() %}
            <div class="nav-item">
                <a href="{{ url_for('users') }}" class="nav-link">
                    <i class="fas fa-users"></i>
                    إدارة المستخدمين
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('providers') }}" class="nav-link">
                    <i class="fas fa-cloud"></i>
                    مزودي الخدمة
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('payment_methods') }}" class="nav-link">
                    <i class="fas fa-credit-card"></i>
                    طرق الدفع
                </a>
            </div>
            {% endif %}
        </nav>

        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-name">{{ current_user.full_name }}</div>
            <div class="user-role">
                {% if current_user.is_admin() %}
                    <i class="fas fa-crown me-1"></i>مدير النظام
                {% else %}
                    <i class="fas fa-user me-1"></i>مستخدم عادي
                {% endif %}
            </div>
            <div style="margin-top: 10px;">
                <a href="{{ url_for('logout') }}" class="item-btn btn-danger" style="width: 100%; justify-content: center;">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-bar">
            <div class="page-title">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم
            </div>
            <div class="top-actions">
                <a href="{{ url_for('add_subscription') }}" class="action-btn">
                    <i class="fas fa-plus"></i>
                    إضافة اشتراك
                </a>
                <a href="{{ url_for('subscriptions') }}" class="action-btn">
                    <i class="fas fa-list"></i>
                    عرض الكل
                </a>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show">
                            <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- بطاقات الإحصائيات الأفقية -->
            <div class="stats-container">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-subscription"></i>
                    </div>
                    <div class="stats-number">{{ stats.total_subscriptions }}</div>
                    <div class="stats-label">إجمالي الاشتراكات</div>
                </div>

                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-number">{{ stats.active_subscriptions }}</div>
                    <div class="stats-label">الاشتراكات النشطة</div>
                </div>

                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stats-number">{{ stats.total_invoices }}</div>
                    <div class="stats-label">إجمالي الفواتير</div>
                </div>

                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stats-number">{{ stats.pending_invoices }}</div>
                    <div class="stats-label">الفواتير المعلقة</div>
                </div>

                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stats-number">{{ stats.total_revenue }}</div>
                    <div class="stats-label">إجمالي الإيرادات</div>
                </div>

                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stats-number">{{ stats.expiring_soon }}</div>
                    <div class="stats-label">تنتهي قريباً</div>
                </div>
            </div>

            <!-- الاشتراكات الحديثة -->
            <div class="horizontal-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-subscription me-2"></i>
                        الاشتراكات الحديثة
                    </h3>
                    <div class="section-actions">
                        <a href="{{ url_for('add_subscription') }}" class="action-btn">
                            <i class="fas fa-plus"></i>
                            إضافة جديد
                        </a>
                        <a href="{{ url_for('subscriptions') }}" class="action-btn">
                            <i class="fas fa-list"></i>
                            عرض الكل
                        </a>
                    </div>
                </div>

                <div class="horizontal-list">
                    <!-- سيتم ملء هذا القسم بالاشتراكات الحديثة -->
                    <div class="list-item">
                        <div class="item-header">
                            <div class="item-title">
                                <i class="fas fa-server me-2"></i>
                                خادم الويب الرئيسي
                            </div>
                            <div class="item-status status-active">نشط</div>
                        </div>
                        <div class="item-details">
                            <div><strong>المزود:</strong> AdenLink - شركة محمد الجبوري</div>
                            <div><strong>النوع:</strong> شهري</div>
                            <div><strong>السعر:</strong> $29.99</div>
                            <div><strong>ينتهي في:</strong> 15 يوم</div>
                        </div>
                        <div class="item-actions">
                            <a href="#" class="item-btn btn-primary">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </a>
                            <a href="#" class="item-btn btn-success">
                                <i class="fas fa-file-invoice"></i>
                                فاتورة
                            </a>
                        </div>
                    </div>

                    <div class="list-item">
                        <div class="item-header">
                            <div class="item-title">
                                <i class="fas fa-database me-2"></i>
                                قاعدة البيانات الرئيسية
                            </div>
                            <div class="item-status status-active">نشط</div>
                        </div>
                        <div class="item-details">
                            <div><strong>المزود:</strong> Amazon Web Services</div>
                            <div><strong>النوع:</strong> سنوي</div>
                            <div><strong>السعر:</strong> $299.99</div>
                            <div><strong>ينتهي في:</strong> 120 يوم</div>
                        </div>
                        <div class="item-actions">
                            <a href="#" class="item-btn btn-primary">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </a>
                            <a href="#" class="item-btn btn-success">
                                <i class="fas fa-file-invoice"></i>
                                فاتورة
                            </a>
                        </div>
                    </div>

                    <div class="list-item">
                        <div class="item-header">
                            <div class="item-title">
                                <i class="fas fa-shield-alt me-2"></i>
                                خدمة الحماية المتقدمة
                            </div>
                            <div class="item-status status-pending">معلق</div>
                        </div>
                        <div class="item-details">
                            <div><strong>المزود:</strong> Microsoft Azure</div>
                            <div><strong>النوع:</strong> شهري</div>
                            <div><strong>السعر:</strong> $49.99</div>
                            <div><strong>ينتهي في:</strong> 5 أيام</div>
                        </div>
                        <div class="item-actions">
                            <a href="#" class="item-btn btn-primary">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </a>
                            <a href="#" class="item-btn btn-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                تجديد
                            </a>
                        </div>
                    </div>

                    <!-- إضافة المزيد من العناصر حسب الحاجة -->
                    <div class="list-item" style="background: rgba(255, 255, 255, 0.05); border: 2px dashed rgba(255, 255, 255, 0.3); display: flex; align-items: center; justify-content: center; min-height: 200px;">
                        <div style="text-align: center; color: rgba(255, 255, 255, 0.5);">
                            <i class="fas fa-plus" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <div>إضافة اشتراك جديد</div>
                            <a href="{{ url_for('add_subscription') }}" class="item-btn btn-primary" style="margin-top: 10px;">
                                <i class="fas fa-plus"></i>
                                إضافة
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الفواتير الحديثة -->
            <div class="horizontal-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-file-invoice me-2"></i>
                        الفواتير الحديثة
                    </h3>
                    <div class="section-actions">
                        <a href="#" class="action-btn">
                            <i class="fas fa-plus"></i>
                            إنشاء فاتورة
                        </a>
                        <a href="#" class="action-btn">
                            <i class="fas fa-list"></i>
                            عرض الكل
                        </a>
                    </div>
                </div>

                <div class="horizontal-list">
                    <div class="list-item">
                        <div class="item-header">
                            <div class="item-title">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                فاتورة #INV-001
                            </div>
                            <div class="item-status status-pending">معلقة</div>
                        </div>
                        <div class="item-details">
                            <div><strong>المبلغ:</strong> $29.99</div>
                            <div><strong>تاريخ الإصدار:</strong> 2024-01-15</div>
                            <div><strong>تاريخ الاستحقاق:</strong> 2024-02-15</div>
                            <div><strong>الاشتراك:</strong> خادم الويب الرئيسي</div>
                        </div>
                        <div class="item-actions">
                            <a href="#" class="item-btn btn-success">
                                <i class="fas fa-eye"></i>
                                عرض
                            </a>
                            <a href="#" class="item-btn btn-primary">
                                <i class="fas fa-download"></i>
                                تحميل
                            </a>
                        </div>
                    </div>

                    <div class="list-item">
                        <div class="item-header">
                            <div class="item-title">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                فاتورة #INV-002
                            </div>
                            <div class="item-status status-active">مدفوعة</div>
                        </div>
                        <div class="item-details">
                            <div><strong>المبلغ:</strong> $299.99</div>
                            <div><strong>تاريخ الدفع:</strong> 2024-01-10</div>
                            <div><strong>طريقة الدفع:</strong> بطاقة ائتمان</div>
                            <div><strong>الاشتراك:</strong> قاعدة البيانات الرئيسية</div>
                        </div>
                        <div class="item-actions">
                            <a href="#" class="item-btn btn-success">
                                <i class="fas fa-eye"></i>
                                عرض
                            </a>
                            <a href="#" class="item-btn btn-primary">
                                <i class="fas fa-download"></i>
                                تحميل
                            </a>
                        </div>
                    </div>

                    <!-- حالة فارغة للفواتير -->
                    <div class="list-item" style="background: rgba(255, 255, 255, 0.05); border: 2px dashed rgba(255, 255, 255, 0.3); display: flex; align-items: center; justify-content: center; min-height: 200px;">
                        <div style="text-align: center; color: rgba(255, 255, 255, 0.5);">
                            <i class="fas fa-file-invoice" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <div>إنشاء فاتورة جديدة</div>
                            <a href="#" class="item-btn btn-primary" style="margin-top: 10px;">
                                <i class="fas fa-plus"></i>
                                إنشاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات المطور -->
            <div class="horizontal-section" style="text-align: center; margin-top: 50px;">
                <div style="color: rgba(255, 255, 255, 0.7);">
                    <p style="margin-bottom: 10px;">
                        <i class="fas fa-code text-primary me-2"></i>
                        مطور بواسطة: <span style="color: #bf00ff; font-weight: 600;">المهندس محمد ياسر الجبوري</span>
                    </p>
                    <p style="margin-bottom: 0;">
                        <i class="fas fa-heart text-danger me-2"></i>
                        صُنع بحب وإتقان لخدمة المجتمع التقني العربي - شركة AdenLink 🇮🇶
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تبديل الشريط الجانبي للموبايل
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }

    // إغلاق الشريط الجانبي عند النقر خارجه في الموبايل
    document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('sidebar');
        const toggle = document.querySelector('.sidebar-toggle');

        if (window.innerWidth <= 768) {
            if (!sidebar.contains(event.target) && !toggle.contains(event.target)) {
                sidebar.classList.remove('active');
            }
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي للبطاقات
        const cards = document.querySelectorAll('.stats-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // تأثير الظهور التدريجي للعناصر الأفقية
        const listItems = document.querySelectorAll('.list-item');
        listItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(30px)';

            setTimeout(() => {
                item.style.transition = 'all 0.6s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, 500 + (index * 100));
        });

        // تأثير العد التصاعدي للأرقام
        const numbers = document.querySelectorAll('.stats-number');
        numbers.forEach(number => {
            const finalValue = parseInt(number.textContent) || 0;
            let currentValue = 0;
            const increment = finalValue / 20;

            const counter = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    number.textContent = finalValue === 0 ? '0' : finalValue.toString();
                    clearInterval(counter);
                } else {
                    number.textContent = Math.floor(currentValue).toString();
                }
            }, 50);
        });

        // تحديد الرابط النشط في الشريط الجانبي
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });

        // تأثيرات التمرير السلس للقوائم الأفقية
        const horizontalLists = document.querySelectorAll('.horizontal-list');
        horizontalLists.forEach(list => {
            let isDown = false;
            let startX;
            let scrollLeft;

            list.addEventListener('mousedown', (e) => {
                isDown = true;
                list.classList.add('active');
                startX = e.pageX - list.offsetLeft;
                scrollLeft = list.scrollLeft;
            });

            list.addEventListener('mouseleave', () => {
                isDown = false;
                list.classList.remove('active');
            });

            list.addEventListener('mouseup', () => {
                isDown = false;
                list.classList.remove('active');
            });

            list.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - list.offsetLeft;
                const walk = (x - startX) * 2;
                list.scrollLeft = scrollLeft - walk;
            });
        });

        // تأثيرات hover للبطاقات
        const allCards = document.querySelectorAll('.stats-card, .list-item');
        allCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = this.classList.contains('stats-card')
                    ? 'translateY(-10px) scale(1.05)'
                    : 'translateY(-5px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = this.classList.contains('stats-card')
                    ? 'translateY(0) scale(1)'
                    : 'translateY(0)';
            });
        });

        // تحديث الوقت كل ثانية (اختياري)
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            const dateString = now.toLocaleDateString('ar-SA');

            // يمكن إضافة عنصر لعرض الوقت إذا لزم الأمر
        }

        // تشغيل تحديث الوقت كل ثانية
        setInterval(updateTime, 1000);
        updateTime();

        // تأثيرات إضافية للأزرار
        const actionButtons = document.querySelectorAll('.action-btn, .item-btn');
        actionButtons.forEach(btn => {
            btn.addEventListener('click', function(e) {
                // تأثير الموجة عند النقر
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    });

    // تحسين الأداء للشاشات الصغيرة
    window.addEventListener('resize', function() {
        const sidebar = document.getElementById('sidebar');
        if (window.innerWidth > 768) {
            sidebar.classList.remove('active');
        }
    });
    </script>

    <style>
    /* تأثير الموجة للأزرار */
    .action-btn, .item-btn {
        position: relative;
        overflow: hidden;
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    /* تحسينات إضافية للتمرير */
    .horizontal-list.active {
        cursor: grabbing;
        cursor: -webkit-grabbing;
    }

    .horizontal-list {
        cursor: grab;
        cursor: -webkit-grab;
    }

    /* تأثيرات إضافية للشريط الجانبي */
    .sidebar .nav-link::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 0;
        background: linear-gradient(135deg, rgba(0, 245, 255, 0.1), rgba(191, 0, 255, 0.1));
        transition: width 0.3s ease;
    }

    .sidebar .nav-link:hover::before {
        width: 100%;
    }
    </style>
</body>
</html>
'''

# قالب صفحة إدارة الاشتراكات بالتصميم الجديد
SUBSCRIPTIONS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الاشتراكات - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* استخدام نفس أنماط الشريط الجانبي من لوحة التحكم */
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            color: #00f5ff;
            font-size: 1.8rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(191, 0, 255, 0.2));
            color: #00f5ff;
            border-right: 4px solid #00f5ff;
        }

        .nav-link i {
            width: 20px;
            margin-left: 15px;
            font-size: 1.1rem;
        }

        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .user-name {
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            overflow-x: auto;
        }

        .main-content::-webkit-scrollbar {
            height: 8px;
        }

        .main-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .main-content::-webkit-scrollbar-thumb {
            background: linear-gradient(90deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .top-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
        }

        .top-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .action-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .content-area {
            padding: 30px;
            min-width: 1200px;
        }

        /* قائمة الاشتراكات الأفقية */
        .subscriptions-container {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding-bottom: 20px;
        }

        .subscriptions-container::-webkit-scrollbar {
            height: 8px;
        }

        .subscriptions-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .subscriptions-container::-webkit-scrollbar-thumb {
            background: linear-gradient(90deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .subscription-card {
            min-width: 350px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .subscription-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .subscription-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .subscription-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .subscription-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
        }

        .subscription-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(0, 255, 0, 0.2);
            color: #4ade80;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .status-expired {
            background: rgba(255, 0, 0, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 0, 0, 0.3);
        }

        .status-suspended {
            background: rgba(255, 165, 0, 0.2);
            color: #ffa500;
            border: 1px solid rgba(255, 165, 0, 0.3);
        }

        .subscription-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detail-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .detail-value {
            color: white;
            font-weight: 600;
        }

        .subscription-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .item-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 8px;
            color: white;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-edit {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .btn-delete {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .btn-invoice {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .item-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 100px 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            border: 2px dashed rgba(255, 255, 255, 0.2);
        }

        .empty-icon {
            font-size: 5rem;
            color: rgba(255, 255, 255, 0.3);
            margin-bottom: 30px;
        }

        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 10px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .content-area {
                min-width: auto;
                padding: 20px;
            }

            .subscriptions-container {
                flex-direction: column;
            }

            .subscription-card {
                min-width: auto;
            }

            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>

        .page-header {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
        }

        .page-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 10px;
        }

        .subscription-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .subscription-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .subscription-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 245, 255, 0.3);
        }

        .subscription-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .subscription-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
        }

        .subscription-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(0, 255, 0, 0.2);
            color: #4ade80;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .status-expired {
            background: rgba(255, 0, 0, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 0, 0, 0.3);
        }

        .status-suspended {
            background: rgba(255, 165, 0, 0.2);
            color: #ffa500;
            border: 1px solid rgba(255, 165, 0, 0.3);
        }

        .subscription-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .detail-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detail-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .detail-value {
            color: white;
            font-weight: 600;
        }

        .subscription-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 8px;
            color: white;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-edit {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .btn-renew {
            background: linear-gradient(135deg, #4ade80, #22c55e);
        }

        .btn-delete {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .btn-invoice {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .add-subscription-btn {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .add-subscription-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.6);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .empty-icon {
            font-size: 4rem;
            color: rgba(255, 255, 255, 0.3);
            margin-bottom: 20px;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 0;
        }

        .navbar-brand {
            color: #00f5ff !important;
            font-weight: 700;
            font-size: 1.5rem;
            text-shadow: 0 0 20px #00f5ff;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: #00f5ff !important;
            text-shadow: 0 0 10px #00f5ff;
        }

        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }

            .subscription-details {
                grid-template-columns: 1fr;
            }

            .subscription-actions {
                justify-content: center;
            }

            .add-subscription-btn {
                bottom: 20px;
                left: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- زر تبديل الشريط الجانبي للموبايل -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- الشريط الجانبي الأيسر -->
    <div class="sidebar" id="sidebar">
        <!-- شعار النظام -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-rocket"></i>
                نظام إدارة الاشتراكات
            </div>
            <div class="sidebar-subtitle">شركة محمد الجبوري - AdenLink</div>
        </div>

        <!-- قائمة التنقل -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscriptions') }}" class="nav-link active">
                    <i class="fas fa-subscription"></i>
                    إدارة الاشتراكات
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('add_subscription') }}" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    إضافة اشتراك جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('analytics') }}" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    التحليلات والتقارير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('invoices') }}" class="nav-link">
                    <i class="fas fa-file-invoice"></i>
                    إدارة الفواتير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('communication') }}" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    مركز التواصل
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscription_charts') }}" class="nav-link">
                    <i class="fas fa-chart-pie"></i>
                    مخططات الاشتراكات
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('customer_statement') }}" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    كشف حساب العملاء
                </a>
            </div>
            {% if current_user.is_admin() %}
            <div class="nav-item">
                <a href="{{ url_for('users') }}" class="nav-link">
                    <i class="fas fa-users"></i>
                    إدارة المستخدمين
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('providers') }}" class="nav-link">
                    <i class="fas fa-cloud"></i>
                    مزودي الخدمة
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('payment_methods') }}" class="nav-link">
                    <i class="fas fa-credit-card"></i>
                    طرق الدفع
                </a>
            </div>
            {% endif %}
        </nav>

        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-name">{{ current_user.full_name }}</div>
            <div class="user-role">
                {% if current_user.is_admin() %}
                    <i class="fas fa-crown me-1"></i>مدير النظام
                {% else %}
                    <i class="fas fa-user me-1"></i>مستخدم عادي
                {% endif %}
            </div>
            <div style="margin-top: 10px;">
                <a href="{{ url_for('logout') }}" class="item-btn btn-danger" style="width: 100%; justify-content: center;">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-bar">
            <div class="page-title">
                <i class="fas fa-subscription me-2"></i>
                إدارة الاشتراكات
            </div>
            <div class="top-actions">
                <a href="{{ url_for('add_subscription') }}" class="action-btn">
                    <i class="fas fa-plus"></i>
                    إضافة اشتراك
                </a>
                <a href="{{ url_for('dashboard') }}" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">

            <!-- قائمة الاشتراكات الأفقية -->
            {% if subscriptions %}
                <div class="subscriptions-container">
                    {% for subscription in subscriptions %}
                    <div class="subscription-card">
                        <div class="subscription-header">
                            <div class="subscription-name">
                                <i class="fas fa-server me-2"></i>
                                {{ subscription.name }}
                            </div>
                            <div class="subscription-status status-{{ subscription.status }}">
                                {% if subscription.status == 'active' %}
                                    <i class="fas fa-check-circle me-1"></i>
                                    نشط
                                {% elif subscription.status == 'expired' %}
                                    <i class="fas fa-times-circle me-1"></i>
                                    منتهي
                                {% elif subscription.status == 'suspended' %}
                                    <i class="fas fa-pause-circle me-1"></i>
                                    موقوف
                                {% endif %}
                            </div>
                        </div>

                        <div class="subscription-details">
                            <div class="detail-row">
                                <div class="detail-label">مزود الخدمة</div>
                                <div class="detail-value">
                                    <i class="fas fa-cloud me-1"></i>
                                    {{ subscription.provider.name }}
                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">نوع الاشتراك</div>
                                <div class="detail-value">
                                    <i class="fas fa-calendar me-1"></i>
                                    {% if subscription.subscription_type == 'monthly' %}
                                        شهري
                                    {% elif subscription.subscription_type == 'semi_annual' %}
                                        نصف سنوي
                                    {% elif subscription.subscription_type == 'annual' %}
                                        سنوي
                                    {% endif %}
                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">السعر</div>
                                <div class="detail-value">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    {{ "%.2f"|format(subscription.price) }} {{ subscription.currency }}
                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">تاريخ الانتهاء</div>
                                <div class="detail-value">
                                    <i class="fas fa-calendar-times me-1"></i>
                                    {{ subscription.end_date.strftime('%Y-%m-%d') }}
                                </div>
                            </div>

                            {% if subscription.server_ip %}
                            <div class="detail-row">
                                <div class="detail-label">عنوان الخادم</div>
                                <div class="detail-value">
                                    <i class="fas fa-network-wired me-1"></i>
                                    {{ subscription.server_ip }}{% if subscription.port %}:{{ subscription.port }}{% endif %}
                                </div>
                            </div>
                            {% endif %}

                            <div class="detail-row">
                                <div class="detail-label">الأولوية</div>
                                <div class="detail-value">
                                    {% if subscription.priority == 'critical' %}
                                        <i class="fas fa-exclamation-triangle text-danger me-1"></i>
                                        حرجة
                                    {% elif subscription.priority == 'high' %}
                                        <i class="fas fa-arrow-up text-warning me-1"></i>
                                        عالية
                                    {% elif subscription.priority == 'medium' %}
                                        <i class="fas fa-minus text-info me-1"></i>
                                        متوسطة
                                    {% else %}
                                        <i class="fas fa-arrow-down text-success me-1"></i>
                                        منخفضة
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="subscription-actions">
                            <a href="{{ url_for('edit_subscription', subscription_id=subscription.id) }}" class="item-btn btn-edit">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </a>
                            <a href="{{ url_for('renew_subscription', subscription_id=subscription.id) }}" class="item-btn btn-renew">
                                <i class="fas fa-redo"></i>
                                تجديد
                            </a>
                            <a href="{{ url_for('add_invoice') }}?subscription_id={{ subscription.id }}" class="item-btn btn-invoice">
                                <i class="fas fa-file-invoice"></i>
                                إنشاء فاتورة
                            </a>
                            {% if current_user.is_admin() or subscription.user_id == current_user.id %}
                            <a href="{{ url_for('delete_subscription', subscription_id=subscription.id) }}"
                               class="item-btn btn-delete"
                               onclick="return confirm('هل أنت متأكد من حذف هذا الاشتراك؟')">
                                <i class="fas fa-trash"></i>
                                حذف
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}

                    <!-- بطاقة إضافة اشتراك جديد -->
                    <div class="subscription-card" style="background: rgba(255, 255, 255, 0.05); border: 2px dashed rgba(255, 255, 255, 0.3); display: flex; align-items: center; justify-content: center; min-height: 300px;">
                        <div style="text-align: center; color: rgba(255, 255, 255, 0.5);">
                            <i class="fas fa-plus" style="font-size: 3rem; margin-bottom: 20px;"></i>
                            <div style="font-size: 1.2rem; margin-bottom: 15px;">إضافة اشتراك جديد</div>
                            <a href="{{ url_for('add_subscription') }}" class="item-btn btn-edit">
                                <i class="fas fa-plus"></i>
                                إضافة
                            </a>
                        </div>
                    </div>
                </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-subscription"></i>
                </div>
                <h3 style="color: rgba(255, 255, 255, 0.7); margin-bottom: 15px;">
                    لا توجد اشتراكات حالياً
                </h3>
                <p style="color: rgba(255, 255, 255, 0.5); margin-bottom: 25px;">
                    ابدأ بإضافة اشتراكك الأول في الخدمات السحابية
                </p>
                <a href="{{ url_for('add_subscription') }}" class="action-btn btn-edit">
                    <i class="fas fa-plus me-2"></i>
                    إضافة اشتراك جديد
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Add Subscription Button -->
    <a href="{{ url_for('add_subscription') }}" class="add-subscription-btn" title="إضافة اشتراك جديد">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تبديل الشريط الجانبي للموبايل
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }

    // إغلاق الشريط الجانبي عند النقر خارجه في الموبايل
    document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('sidebar');
        const toggle = document.querySelector('.sidebar-toggle');

        if (window.innerWidth <= 768) {
            if (!sidebar.contains(event.target) && !toggle.contains(event.target)) {
                sidebar.classList.remove('active');
            }
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي للبطاقات
        const cards = document.querySelectorAll('.subscription-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateX(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateX(0)';
            }, index * 100);
        });

        // تحديد الرابط النشط في الشريط الجانبي
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });

        // تأثيرات التمرير السلس للقائمة الأفقية
        const container = document.querySelector('.subscriptions-container');
        if (container) {
            let isDown = false;
            let startX;
            let scrollLeft;

            container.addEventListener('mousedown', (e) => {
                isDown = true;
                container.classList.add('active');
                startX = e.pageX - container.offsetLeft;
                scrollLeft = container.scrollLeft;
            });

            container.addEventListener('mouseleave', () => {
                isDown = false;
                container.classList.remove('active');
            });

            container.addEventListener('mouseup', () => {
                isDown = false;
                container.classList.remove('active');
            });

            container.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - container.offsetLeft;
                const walk = (x - startX) * 2;
                container.scrollLeft = scrollLeft - walk;
            });
        }

        // تأثيرات hover للبطاقات
        const allCards = document.querySelectorAll('.subscription-card');
        allCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    });

    // تحسين الأداء للشاشات الصغيرة
    window.addEventListener('resize', function() {
        const sidebar = document.getElementById('sidebar');
        if (window.innerWidth > 768) {
            sidebar.classList.remove('active');
        }
    });
    </script>
</body>
</html>
'''

# قالب صفحة إضافة اشتراك جديد
ADD_SUBSCRIPTION_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة اشتراك جديد - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* الشريط الجانبي */
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            color: #00f5ff;
            font-size: 1.8rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(191, 0, 255, 0.2));
            color: #00f5ff;
            border-right: 4px solid #00f5ff;
        }

        .nav-link i {
            width: 20px;
            margin-left: 15px;
            font-size: 1.1rem;
        }

        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .user-name {
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            overflow-x: auto;
        }

        .top-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
        }

        .top-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .action-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .content-area {
            padding: 30px;
        }

        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 10px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .sidebar-toggle {
                display: block;
            }
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 30px;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .form-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 10px;
        }

        .form-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
        }

        .form-section {
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-title {
            color: #bf00ff;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #bf00ff;
        }

        .form-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00f5ff;
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
            color: white;
            outline: none;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-select option {
            background: #1a1a2e;
            color: white;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .cancel-btn {
            width: 100%;
            padding: 15px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: transparent;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-top: 10px;
        }

        .cancel-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 0;
        }

        .navbar-brand {
            color: #00f5ff !important;
            font-weight: 700;
            font-size: 1.5rem;
            text-shadow: 0 0 20px #00f5ff;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: #00f5ff !important;
            text-shadow: 0 0 10px #00f5ff;
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            color: #4ade80;
        }

        .alert-error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff6b6b;
        }

        @media (max-width: 768px) {
            .form-container {
                padding: 15px;
            }

            .form-card {
                padding: 25px 20px;
            }

            .form-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- زر تبديل الشريط الجانبي للموبايل -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- الشريط الجانبي الأيسر -->
    <div class="sidebar" id="sidebar">
        <!-- شعار النظام -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-rocket"></i>
                نظام إدارة الاشتراكات
            </div>
            <div class="sidebar-subtitle">شركة محمد الجبوري - AdenLink</div>
        </div>

        <!-- قائمة التنقل -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscriptions') }}" class="nav-link">
                    <i class="fas fa-subscription"></i>
                    إدارة الاشتراكات
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('add_subscription') }}" class="nav-link active">
                    <i class="fas fa-plus-circle"></i>
                    إضافة اشتراك جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('analytics') }}" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    التحليلات والتقارير
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-file-invoice"></i>
                    إدارة الفواتير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('communication') }}" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    مركز التواصل
                </a>
            </div>
        </nav>

        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-name">{{ current_user.full_name }}</div>
            <div class="user-role">
                {% if current_user.is_admin() %}
                    <i class="fas fa-crown me-1"></i>مدير النظام
                {% else %}
                    <i class="fas fa-user me-1"></i>مستخدم عادي
                {% endif %}
            </div>
            <div style="margin-top: 10px;">
                <a href="{{ url_for('logout') }}" class="item-btn btn-danger" style="width: 100%; justify-content: center;">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-bar">
            <div class="page-title">
                <i class="fas fa-plus-circle me-2"></i>
                إضافة اشتراك جديد
            </div>
            <div class="top-actions">
                <a href="{{ url_for('subscriptions') }}" class="action-btn">
                    <i class="fas fa-list"></i>
                    عرض الاشتراكات
                </a>
                <a href="{{ url_for('dashboard') }}" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <div class="form-container">
        <div class="form-card">
            <div class="form-header">
                <h1 class="form-title">
                    <i class="fas fa-plus-circle me-3"></i>
                    إضافة اشتراك جديد
                </h1>
                <p class="form-subtitle">
                    أضف اشتراكاً جديداً في الخدمات السحابية أو التقنية
                </p>
            </div>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'error' }}">
                            <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" novalidate>
                <!-- معلومات أساسية -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-info-circle me-2"></i>
                        المعلومات الأساسية
                    </h3>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="provider_id" class="form-label">مزود الخدمة *</label>
                            <select name="provider_id" class="form-select" required>
                                <option value="">اختر مزود الخدمة</option>
                                {% for provider in providers %}
                                <option value="{{ provider.id }}">{{ provider.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الاشتراك *</label>
                            <input type="text" name="name" class="form-control" placeholder="مثال: خادم الويب الرئيسي" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="service_type" class="form-label">نوع الخدمة</label>
                            <select name="service_type" class="form-select">
                                <option value="">اختر نوع الخدمة</option>
                                <option value="compute">خوادم حاسوبية</option>
                                <option value="storage">تخزين</option>
                                <option value="database">قواعد بيانات</option>
                                <option value="networking">شبكات</option>
                                <option value="security">أمان</option>
                                <option value="monitoring">مراقبة</option>
                                <option value="backup">نسخ احتياطي</option>
                                <option value="cdn">شبكة توصيل المحتوى</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select name="priority" class="form-select">
                                <option value="low">منخفضة</option>
                                <option value="medium" selected>متوسطة</option>
                                <option value="high">عالية</option>
                                <option value="critical">حرجة</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea name="description" class="form-control" rows="3" placeholder="وصف مختصر للاشتراك وغرضه"></textarea>
                    </div>
                </div>

                <!-- معلومات الاشتراك والسعر -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-credit-card me-2"></i>
                        معلومات الاشتراك والسعر
                    </h3>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="subscription_type" class="form-label">نوع الاشتراك *</label>
                            <select name="subscription_type" class="form-select" required>
                                <option value="">اختر نوع الاشتراك</option>
                                <option value="monthly">شهري</option>
                                <option value="semi_annual">نصف سنوي</option>
                                <option value="annual">سنوي</option>
                            </select>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="price" class="form-label">السعر *</label>
                            <input type="number" name="price" class="form-control" step="0.01" placeholder="0.00" required>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="currency" class="form-label">العملة</label>
                            <select name="currency" class="form-select">
                                <option value="USD">دولار أمريكي (USD)</option>
                                <option value="EUR">يورو (EUR)</option>
                                <option value="IQD">دينار عراقي (IQD)</option>
                                <option value="SAR">ريال سعودي (SAR)</option>
                                <option value="AED">درهم إماراتي (AED)</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="start_date" class="form-label">تاريخ البدء *</label>
                        <input type="date" name="start_date" class="form-control" required>
                    </div>
                </div>

                <!-- معلومات الاتصال (اختيارية) -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-network-wired me-2"></i>
                        معلومات الاتصال (اختيارية)
                    </h3>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="server_ip" class="form-label">عنوان IP الخادم</label>
                            <input type="text" name="server_ip" class="form-control" placeholder="*************">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="port" class="form-label">المنفذ</label>
                            <input type="number" name="port" class="form-control" placeholder="22">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" name="username" class="form-control" placeholder="admin">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="region" class="form-label">المنطقة</label>
                            <input type="text" name="region" class="form-control" placeholder="us-east-1">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <input type="password" name="password" class="form-control" placeholder="••••••••">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="api_key" class="form-label">مفتاح API</label>
                            <input type="text" name="api_key" class="form-control" placeholder="API Key">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea name="notes" class="form-control" rows="3" placeholder="أي ملاحظات أو معلومات إضافية"></textarea>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="row">
                    <div class="col-md-6">
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-save me-2"></i>
                            حفظ الاشتراك
                        </button>
                    </div>
                    <div class="col-md-6">
                        <a href="{{ url_for('subscriptions') }}" class="cancel-btn">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تبديل الشريط الجانبي للموبايل
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }

    // إغلاق الشريط الجانبي عند النقر خارجه في الموبايل
    document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('sidebar');
        const toggle = document.querySelector('.sidebar-toggle');

        if (window.innerWidth <= 768) {
            if (!sidebar.contains(event.target) && !toggle.contains(event.target)) {
                sidebar.classList.remove('active');
            }
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        // تعيين تاريخ اليوم كتاريخ افتراضي
        const startDateInput = document.querySelector('input[name="start_date"]');
        if (startDateInput) {
            const today = new Date().toISOString().split('T')[0];
            startDateInput.value = today;
        }

        // تأثير الظهور التدريجي
        const formCard = document.querySelector('.form-card');
        formCard.style.opacity = '0';
        formCard.style.transform = 'translateY(30px)';

        setTimeout(() => {
            formCard.style.transition = 'all 0.6s ease';
            formCard.style.opacity = '1';
            formCard.style.transform = 'translateY(0)';
        }, 200);

        // تأثيرات تفاعلية للحقول
        const inputs = document.querySelectorAll('.form-control, .form-select');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // تحديد الرابط النشط في الشريط الجانبي
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });
    });

    // تحسين الأداء للشاشات الصغيرة
    window.addEventListener('resize', function() {
        const sidebar = document.getElementById('sidebar');
        if (window.innerWidth > 768) {
            sidebar.classList.remove('active');
        }
    });
    </script>
</body>
</html>
'''

# قالب صفحة التحليلات
ANALYTICS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحليلات والتقارير - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* نفس أنماط الشريط الجانبي */
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            color: #00f5ff;
            font-size: 1.8rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(191, 0, 255, 0.2));
            color: #00f5ff;
            border-right: 4px solid #00f5ff;
        }

        .nav-link i {
            width: 20px;
            margin-left: 15px;
            font-size: 1.1rem;
        }

        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .user-name {
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            overflow-x: auto;
        }

        .top-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
        }

        .top-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .action-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .content-area {
            padding: 30px;
            min-width: 1200px;
        }

        /* بطاقات التحليلات */
        .analytics-grid {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            overflow-x: auto;
            padding-bottom: 10px;
        }

        .analytics-card {
            min-width: 250px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .analytics-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .analytics-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .analytics-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .analytics-number {
            font-size: 2rem;
            font-weight: 700;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            margin-bottom: 10px;
        }

        .analytics-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            font-weight: 500;
        }

        /* قسم الرسوم البيانية */
        .charts-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-title {
            color: #bf00ff;
            font-size: 1.5rem;
            font-weight: 600;
            text-shadow: 0 0 10px #bf00ff;
            margin-bottom: 20px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chart-title {
            color: #00f5ff;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
        }

        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 10px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .content-area {
                min-width: auto;
                padding: 20px;
            }

            .analytics-grid {
                flex-direction: column;
            }

            .analytics-card {
                min-width: auto;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }

            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- زر تبديل الشريط الجانبي للموبايل -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- الشريط الجانبي الأيسر -->
    <div class="sidebar" id="sidebar">
        <!-- شعار النظام -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-rocket"></i>
                نظام إدارة الاشتراكات
            </div>
            <div class="sidebar-subtitle">شركة محمد الجبوري - AdenLink</div>
        </div>

        <!-- قائمة التنقل -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscriptions') }}" class="nav-link">
                    <i class="fas fa-subscription"></i>
                    إدارة الاشتراكات
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('add_subscription') }}" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    إضافة اشتراك جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('analytics') }}" class="nav-link active">
                    <i class="fas fa-chart-bar"></i>
                    التحليلات والتقارير
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link">
                    <i class="fas fa-file-invoice"></i>
                    إدارة الفواتير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('communication') }}" class="nav-link">
                    <i class="fas fa-envelope"></i>
                    مركز التواصل
                </a>
            </div>
        </nav>

        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-name">{{ current_user.full_name }}</div>
            <div class="user-role">
                {% if current_user.is_admin() %}
                    <i class="fas fa-crown me-1"></i>مدير النظام
                {% else %}
                    <i class="fas fa-user me-1"></i>مستخدم عادي
                {% endif %}
            </div>
            <div style="margin-top: 10px;">
                <a href="{{ url_for('logout') }}" class="action-btn" style="width: 100%; justify-content: center;">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-bar">
            <div class="page-title">
                <i class="fas fa-chart-bar me-2"></i>
                التحليلات والتقارير
            </div>
            <div class="top-actions">
                <a href="{{ url_for('dashboard') }}" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="{{ url_for('subscriptions') }}" class="action-btn">
                    <i class="fas fa-list"></i>
                    الاشتراكات
                </a>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- بطاقات التحليلات الأفقية -->
            <div class="analytics-grid">
                <div class="analytics-card">
                    <div class="analytics-icon">
                        <i class="fas fa-subscription"></i>
                    </div>
                    <div class="analytics-number">{{ analytics.total_subscriptions }}</div>
                    <div class="analytics-label">إجمالي الاشتراكات</div>
                </div>

                <div class="analytics-card">
                    <div class="analytics-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="analytics-number">{{ analytics.active_subscriptions }}</div>
                    <div class="analytics-label">الاشتراكات النشطة</div>
                </div>

                <div class="analytics-card">
                    <div class="analytics-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="analytics-number">{{ analytics.total_revenue }}</div>
                    <div class="analytics-label">إجمالي الإيرادات</div>
                </div>

                <div class="analytics-card">
                    <div class="analytics-icon">
                        <i class="fas fa-calendar-month"></i>
                    </div>
                    <div class="analytics-number">{{ analytics.monthly_revenue }}</div>
                    <div class="analytics-label">إيرادات هذا الشهر</div>
                </div>

                <div class="analytics-card">
                    <div class="analytics-icon">
                        <i class="fas fa-trending-up"></i>
                    </div>
                    <div class="analytics-number">{{ analytics.growth_rate }}%</div>
                    <div class="analytics-label">معدل النمو</div>
                </div>

                <div class="analytics-card">
                    <div class="analytics-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="analytics-number">{{ analytics.avg_subscription_value }}</div>
                    <div class="analytics-label">متوسط قيمة الاشتراك</div>
                </div>
            </div>

            <!-- قسم الرسوم البيانية -->
            <div class="charts-section">
                <h3 class="section-title">
                    <i class="fas fa-chart-line me-2"></i>
                    الرسوم البيانية والتحليلات
                </h3>

                <div class="charts-grid">
                    <div class="chart-container">
                        <h4 class="chart-title">الإيرادات الشهرية</h4>
                        <canvas id="revenueChart" width="400" height="200"></canvas>
                    </div>

                    <div class="chart-container">
                        <h4 class="chart-title">توزيع الاشتراكات</h4>
                        <canvas id="subscriptionChart" width="400" height="200"></canvas>
                    </div>

                    <div class="chart-container">
                        <h4 class="chart-title">نمو الاشتراكات</h4>
                        <canvas id="growthChart" width="400" height="200"></canvas>
                    </div>

                    <div class="chart-container">
                        <h4 class="chart-title">مزودي الخدمة</h4>
                        <canvas id="providersChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تبديل الشريط الجانبي للموبايل
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // إنشاء الرسوم البيانية

        // رسم الإيرادات الشهرية
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات ($)',
                    data: [1200, 1900, 3000, 5000, 2000, 3000],
                    borderColor: '#00f5ff',
                    backgroundColor: 'rgba(0, 245, 255, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white'
                        }
                    }
                },
                scales: {
                    y: {
                        ticks: {
                            color: 'white'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: 'white'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });

        // رسم توزيع الاشتراكات
        const subscriptionCtx = document.getElementById('subscriptionChart').getContext('2d');
        new Chart(subscriptionCtx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'منتهي', 'موقوف'],
                datasets: [{
                    data: [65, 25, 10],
                    backgroundColor: ['#00f5ff', '#ff6b6b', '#ffa500']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white'
                        }
                    }
                }
            }
        });

        // رسم نمو الاشتراكات
        const growthCtx = document.getElementById('growthChart').getContext('2d');
        new Chart(growthCtx, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'اشتراكات جديدة',
                    data: [12, 19, 30, 50, 20, 30],
                    backgroundColor: 'rgba(191, 0, 255, 0.8)'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white'
                        }
                    }
                },
                scales: {
                    y: {
                        ticks: {
                            color: 'white'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: 'white'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });

        // رسم مزودي الخدمة
        const providersCtx = document.getElementById('providersChart').getContext('2d');
        new Chart(providersCtx, {
            type: 'pie',
            data: {
                labels: ['AdenLink', 'AWS', 'Azure', 'GCP', 'أخرى'],
                datasets: [{
                    data: [40, 25, 15, 10, 10],
                    backgroundColor: ['#00f5ff', '#ff6b6b', '#ffa500', '#4ade80', '#bf00ff']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white'
                        }
                    }
                }
            }
        });

        // تأثير الظهور التدريجي للبطاقات
        const cards = document.querySelectorAll('.analytics-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateX(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateX(0)';
            }, index * 100);
        });
    });
    </script>
</body>
</html>
'''

# قالب صفحة إدارة المستخدمين
USERS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* نفس أنماط الشريط الجانبي */
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            color: #00f5ff;
            font-size: 1.8rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(191, 0, 255, 0.2));
            color: #00f5ff;
            border-right: 4px solid #00f5ff;
        }

        .nav-link i {
            width: 20px;
            margin-left: 15px;
            font-size: 1.1rem;
        }

        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .user-name {
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            overflow-x: auto;
        }

        .top-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
        }

        .top-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .action-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .content-area {
            padding: 30px;
            min-width: 1200px;
        }

        /* قائمة المستخدمين الأفقية */
        .users-container {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding-bottom: 20px;
        }

        .users-container::-webkit-scrollbar {
            height: 8px;
        }

        .users-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .users-container::-webkit-scrollbar-thumb {
            background: linear-gradient(90deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .user-card {
            min-width: 320px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .user-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .user-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .user-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .user-avatar-card {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 1.5rem;
        }

        .user-info-card {
            flex: 1;
        }

        .user-name-card {
            font-size: 1.2rem;
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .user-username {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .user-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detail-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .detail-value {
            color: white;
            font-weight: 600;
        }

        .user-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(0, 255, 0, 0.2);
            color: #4ade80;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .status-inactive {
            background: rgba(255, 0, 0, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 0, 0, 0.3);
        }

        .role-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .role-admin {
            background: rgba(255, 215, 0, 0.2);
            color: #ffd700;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .role-user {
            background: rgba(0, 123, 255, 0.2);
            color: #007bff;
            border: 1px solid rgba(0, 123, 255, 0.3);
        }

        .user-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .item-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 8px;
            color: white;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-edit {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .btn-toggle {
            background: linear-gradient(135deg, #ffa500, #ff6b6b);
        }

        .item-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 10px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .content-area {
                min-width: auto;
                padding: 20px;
            }

            .users-container {
                flex-direction: column;
            }

            .user-card {
                min-width: auto;
            }

            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- زر تبديل الشريط الجانبي للموبايل -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- الشريط الجانبي الأيسر -->
    <div class="sidebar" id="sidebar">
        <!-- شعار النظام -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-rocket"></i>
                نظام إدارة الاشتراكات
            </div>
            <div class="sidebar-subtitle">شركة محمد الجبوري - AdenLink</div>
        </div>

        <!-- قائمة التنقل -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscriptions') }}" class="nav-link">
                    <i class="fas fa-subscription"></i>
                    إدارة الاشتراكات
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('add_subscription') }}" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    إضافة اشتراك جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('analytics') }}" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    التحليلات والتقارير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('invoices') }}" class="nav-link">
                    <i class="fas fa-file-invoice"></i>
                    إدارة الفواتير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('users') }}" class="nav-link active">
                    <i class="fas fa-users"></i>
                    إدارة المستخدمين
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('providers') }}" class="nav-link">
                    <i class="fas fa-cloud"></i>
                    مزودي الخدمة
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('payment_methods') }}" class="nav-link">
                    <i class="fas fa-credit-card"></i>
                    طرق الدفع
                </a>
            </div>
        </nav>

        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-name">{{ current_user.full_name }}</div>
            <div class="user-role">
                <i class="fas fa-crown me-1"></i>مدير النظام
            </div>
            <div style="margin-top: 10px;">
                <a href="{{ url_for('logout') }}" class="action-btn" style="width: 100%; justify-content: center;">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-bar">
            <div class="page-title">
                <i class="fas fa-users me-2"></i>
                إدارة المستخدمين
            </div>
            <div class="top-actions">
                <a href="{{ url_for('add_user') }}" class="action-btn">
                    <i class="fas fa-plus"></i>
                    إضافة مستخدم
                </a>
                <a href="{{ url_for('dashboard') }}" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- قائمة المستخدمين الأفقية -->
            {% if users %}
                <div class="users-container">
                    {% for user in users %}
                    <div class="user-card">
                        <div class="user-header">
                            <div class="user-avatar-card">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="user-info-card">
                                <div class="user-name-card">{{ user.full_name }}</div>
                                <div class="user-username">@{{ user.username }}</div>
                            </div>
                        </div>

                        <div class="user-details">
                            <div class="detail-row">
                                <div class="detail-label">البريد الإلكتروني</div>
                                <div class="detail-value">{{ user.email }}</div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">الدور</div>
                                <div class="detail-value">
                                    <span class="role-badge role-{{ user.role }}">
                                        {% if user.role == 'admin' %}
                                            <i class="fas fa-crown me-1"></i>مدير
                                        {% else %}
                                            <i class="fas fa-user me-1"></i>مستخدم عادي
                                        {% endif %}
                                    </span>
                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">الحالة</div>
                                <div class="detail-value">
                                    <span class="user-status status-{{ 'active' if user.is_active else 'inactive' }}">
                                        {% if user.is_active %}
                                            <i class="fas fa-check-circle me-1"></i>نشط
                                        {% else %}
                                            <i class="fas fa-times-circle me-1"></i>غير نشط
                                        {% endif %}
                                    </span>
                                </div>
                            </div>

                            {% if user.company %}
                            <div class="detail-row">
                                <div class="detail-label">الشركة</div>
                                <div class="detail-value">{{ user.company }}</div>
                            </div>
                            {% endif %}

                            {% if user.phone %}
                            <div class="detail-row">
                                <div class="detail-label">الهاتف</div>
                                <div class="detail-value">{{ user.phone }}</div>
                            </div>
                            {% endif %}

                            <div class="detail-row">
                                <div class="detail-label">تاريخ الإنشاء</div>
                                <div class="detail-value">{{ user.created_at.strftime('%Y-%m-%d') }}</div>
                            </div>
                        </div>

                        {% if user.id != current_user.id %}
                        <div class="user-actions">
                            <a href="{{ url_for('toggle_user_status', user_id=user.id) }}" class="item-btn btn-toggle">
                                {% if user.is_active %}
                                    <i class="fas fa-ban"></i>
                                    تعطيل
                                {% else %}
                                    <i class="fas fa-check"></i>
                                    تفعيل
                                {% endif %}
                            </a>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}

                    <!-- بطاقة إضافة مستخدم جديد -->
                    <div class="user-card" style="background: rgba(255, 255, 255, 0.05); border: 2px dashed rgba(255, 255, 255, 0.3); display: flex; align-items: center; justify-content: center; min-height: 300px;">
                        <div style="text-align: center; color: rgba(255, 255, 255, 0.5);">
                            <i class="fas fa-plus" style="font-size: 3rem; margin-bottom: 20px;"></i>
                            <div style="font-size: 1.2rem; margin-bottom: 15px;">إضافة مستخدم جديد</div>
                            <a href="{{ url_for('add_user') }}" class="item-btn btn-edit">
                                <i class="fas fa-plus"></i>
                                إضافة
                            </a>
                        </div>
                    </div>
                </div>
            {% else %}
                <div style="text-align: center; padding: 100px 20px; background: rgba(255, 255, 255, 0.05); border-radius: 20px; border: 2px dashed rgba(255, 255, 255, 0.2);">
                    <div style="font-size: 5rem; color: rgba(255, 255, 255, 0.3); margin-bottom: 30px;">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 style="color: rgba(255, 255, 255, 0.7); margin-bottom: 15px;">
                        لا يوجد مستخدمون حالياً
                    </h3>
                    <p style="color: rgba(255, 255, 255, 0.5); margin-bottom: 25px;">
                        ابدأ بإضافة المستخدمين لإدارة النظام
                    </p>
                    <a href="{{ url_for('add_user') }}" class="action-btn">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مستخدم جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تبديل الشريط الجانبي للموبايل
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي للبطاقات
        const cards = document.querySelectorAll('.user-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateX(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateX(0)';
            }, index * 100);
        });
    });
    </script>
</body>
</html>
'''

# قالب إضافة مستخدم جديد
ADD_USER_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مستخدم جديد - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* نفس أنماط الشريط الجانبي */
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            color: #00f5ff;
            font-size: 1.8rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(191, 0, 255, 0.2));
            color: #00f5ff;
            border-right: 4px solid #00f5ff;
        }

        .nav-link i {
            width: 20px;
            margin-left: 15px;
            font-size: 1.1rem;
        }

        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .user-name {
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            overflow-x: auto;
        }

        .top-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
        }

        .top-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .action-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .content-area {
            padding: 30px;
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .form-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 10px;
        }

        .form-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
        }

        .form-section {
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-title {
            color: #bf00ff;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #bf00ff;
        }

        .form-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00f5ff;
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
            color: white;
            outline: none;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-select option {
            background: #1a1a2e;
            color: white;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .cancel-btn {
            width: 100%;
            padding: 15px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: transparent;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-top: 10px;
        }

        .cancel-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
        }

        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 10px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- زر تبديل الشريط الجانبي للموبايل -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- الشريط الجانبي الأيسر -->
    <div class="sidebar" id="sidebar">
        <!-- شعار النظام -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-rocket"></i>
                نظام إدارة الاشتراكات
            </div>
            <div class="sidebar-subtitle">شركة محمد الجبوري - AdenLink</div>
        </div>

        <!-- قائمة التنقل -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('users') }}" class="nav-link active">
                    <i class="fas fa-users"></i>
                    إدارة المستخدمين
                </a>
            </div>
        </nav>

        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-name">{{ current_user.full_name }}</div>
            <div class="user-role">
                <i class="fas fa-crown me-1"></i>مدير النظام
            </div>
            <div style="margin-top: 10px;">
                <a href="{{ url_for('logout') }}" class="action-btn" style="width: 100%; justify-content: center;">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-bar">
            <div class="page-title">
                <i class="fas fa-user-plus me-2"></i>
                إضافة مستخدم جديد
            </div>
            <div class="top-actions">
                <a href="{{ url_for('users') }}" class="action-btn">
                    <i class="fas fa-list"></i>
                    قائمة المستخدمين
                </a>
                <a href="{{ url_for('dashboard') }}" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <div class="form-container">
                <div class="form-card">
                    <div class="form-header">
                        <h1 class="form-title">
                            <i class="fas fa-user-plus me-3"></i>
                            إضافة مستخدم جديد
                        </h1>
                        <p class="form-subtitle">
                            أضف مستخدماً جديداً لإدارة النظام
                        </p>
                    </div>

                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show">
                                    <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="POST" novalidate>
                        <!-- معلومات أساسية -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-info-circle me-2"></i>
                                المعلومات الأساسية
                            </h3>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">اسم المستخدم *</label>
                                    <input type="text" name="username" class="form-control" placeholder="اسم المستخدم" required>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" name="email" class="form-control" placeholder="<EMAIL>" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" name="full_name" class="form-control" placeholder="الاسم الكامل" required>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="role" class="form-label">الدور *</label>
                                    <select name="role" class="form-select" required>
                                        <option value="user">مستخدم عادي</option>
                                        <option value="admin">مدير النظام</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور *</label>
                                <input type="password" name="password" class="form-control" placeholder="كلمة المرور" required>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-address-card me-2"></i>
                                معلومات إضافية (اختيارية)
                            </h3>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="company" class="form-label">الشركة</label>
                                    <input type="text" name="company" class="form-control" placeholder="اسم الشركة">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" name="phone" class="form-control" placeholder="+964-XXX-XXXX">
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="submit-btn">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ المستخدم
                                </button>
                            </div>
                            <div class="col-md-6">
                                <a href="{{ url_for('users') }}" class="cancel-btn">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تبديل الشريط الجانبي للموبايل
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي
        const formCard = document.querySelector('.form-card');
        formCard.style.opacity = '0';
        formCard.style.transform = 'translateY(30px)';

        setTimeout(() => {
            formCard.style.transition = 'all 0.6s ease';
            formCard.style.opacity = '1';
            formCard.style.transform = 'translateY(0)';
        }, 200);
    });
    </script>
</body>
</html>
'''

# قالب كشف حساب العملاء
CUSTOMER_STATEMENT_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كشف حساب العملاء - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* نفس أنماط الشريط الجانبي */
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            color: #00f5ff;
            font-size: 1.8rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(191, 0, 255, 0.2));
            color: #00f5ff;
            border-right: 4px solid #00f5ff;
        }

        .nav-link i {
            width: 20px;
            margin-left: 15px;
            font-size: 1.1rem;
        }

        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .user-name {
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            overflow-x: auto;
        }

        .top-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
        }

        .top-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .action-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .content-area {
            padding: 30px;
        }

        /* فلاتر البحث */
        .filters-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .filters-title {
            color: #bf00ff;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #bf00ff;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00f5ff;
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
            color: white;
            outline: none;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-select option {
            background: #1a1a2e;
            color: white;
        }

        /* إحصائيات */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .stat-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            margin-bottom: 10px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            font-weight: 500;
        }

        /* جدول الفواتير */
        .invoices-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-title {
            color: #bf00ff;
            font-size: 1.5rem;
            font-weight: 600;
            text-shadow: 0 0 10px #bf00ff;
            margin-bottom: 20px;
        }

        .table-responsive {
            border-radius: 15px;
            overflow: hidden;
        }

        .table {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            margin-bottom: 0;
        }

        .table th {
            background: rgba(0, 245, 255, 0.2);
            color: #00f5ff;
            border: none;
            padding: 15px;
            font-weight: 600;
        }

        .table td {
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-paid {
            background: rgba(0, 255, 0, 0.2);
            color: #4ade80;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .status-pending {
            background: rgba(255, 165, 0, 0.2);
            color: #ffa500;
            border: 1px solid rgba(255, 165, 0, 0.3);
        }

        .status-cancelled {
            background: rgba(255, 0, 0, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 0, 0, 0.3);
        }

        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 10px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .content-area {
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- زر تبديل الشريط الجانبي للموبايل -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- الشريط الجانبي الأيسر -->
    <div class="sidebar" id="sidebar">
        <!-- شعار النظام -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-rocket"></i>
                نظام إدارة الاشتراكات
            </div>
            <div class="sidebar-subtitle">شركة محمد الجبوري - AdenLink</div>
        </div>

        <!-- قائمة التنقل -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscriptions') }}" class="nav-link">
                    <i class="fas fa-subscription"></i>
                    إدارة الاشتراكات
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('add_subscription') }}" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    إضافة اشتراك جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('analytics') }}" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    التحليلات والتقارير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('invoices') }}" class="nav-link">
                    <i class="fas fa-file-invoice"></i>
                    إدارة الفواتير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('customer_statement') }}" class="nav-link active">
                    <i class="fas fa-file-alt"></i>
                    كشف حساب العملاء
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscription_charts') }}" class="nav-link">
                    <i class="fas fa-chart-pie"></i>
                    مخططات الاشتراكات
                </a>
            </div>
            {% if current_user.is_admin() %}
            <div class="nav-item">
                <a href="{{ url_for('users') }}" class="nav-link">
                    <i class="fas fa-users"></i>
                    إدارة المستخدمين
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('providers') }}" class="nav-link">
                    <i class="fas fa-cloud"></i>
                    مزودي الخدمة
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('payment_methods') }}" class="nav-link">
                    <i class="fas fa-credit-card"></i>
                    طرق الدفع
                </a>
            </div>
            {% endif %}
        </nav>

        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-name">{{ current_user.full_name }}</div>
            <div class="user-role">
                {% if current_user.is_admin() %}
                    <i class="fas fa-crown me-1"></i>مدير النظام
                {% else %}
                    <i class="fas fa-user me-1"></i>مستخدم عادي
                {% endif %}
            </div>
            <div style="margin-top: 10px;">
                <a href="{{ url_for('logout') }}" class="action-btn" style="width: 100%; justify-content: center;">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-bar">
            <div class="page-title">
                <i class="fas fa-file-alt me-2"></i>
                كشف حساب العملاء
            </div>
            <div class="top-actions">
                <a href="{{ url_for('invoices') }}" class="action-btn">
                    <i class="fas fa-file-invoice"></i>
                    إدارة الفواتير
                </a>
                <a href="{{ url_for('dashboard') }}" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- فلاتر البحث -->
            <div class="filters-section">
                <h3 class="filters-title">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر البحث
                </h3>

                <form method="GET">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label" style="color: rgba(255, 255, 255, 0.9);">تاريخ البداية</label>
                            <input type="date" name="start_date" class="form-control" value="{{ stats.start_date }}">
                        </div>

                        <div class="col-md-3 mb-3">
                            <label class="form-label" style="color: rgba(255, 255, 255, 0.9);">تاريخ النهاية</label>
                            <input type="date" name="end_date" class="form-control" value="{{ stats.end_date }}">
                        </div>

                        {% if current_user.is_admin() %}
                        <div class="col-md-4 mb-3">
                            <label class="form-label" style="color: rgba(255, 255, 255, 0.9);">المستخدم</label>
                            <select name="user_id" class="form-select">
                                <option value="">جميع المستخدمين</option>
                                {% for user in users %}
                                <option value="{{ user.id }}" {{ 'selected' if selected_user_id == user.id|string else '' }}>
                                    {{ user.full_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}

                        <div class="col-md-2 mb-3 d-flex align-items-end">
                            <button type="submit" class="action-btn w-100">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- إحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="stat-number">{{ stats.total_invoices }}</div>
                    <div class="stat-label">إجمالي الفواتير</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-number">${{ "%.2f"|format(stats.total_amount) }}</div>
                    <div class="stat-label">إجمالي المبالغ</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-number">${{ "%.2f"|format(stats.paid_amount) }}</div>
                    <div class="stat-label">المبالغ المدفوعة</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number">${{ "%.2f"|format(stats.pending_amount) }}</div>
                    <div class="stat-label">المبالغ المعلقة</div>
                </div>
            </div>

            <!-- جدول الفواتير -->
            <div class="invoices-section">
                <h3 class="section-title">
                    <i class="fas fa-list me-2"></i>
                    تفاصيل الفواتير
                </h3>

                {% if invoices %}
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>الاشتراك</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td><strong>{{ invoice.invoice_number }}</strong></td>
                                <td>{{ invoice.subscription.name if invoice.subscription else 'غير محدد' }}</td>
                                <td>{{ invoice.user.full_name if invoice.user else 'غير محدد' }}</td>
                                <td><strong>${{ "%.2f"|format(invoice.total_amount) }}</strong></td>
                                <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if invoice.payment_status == 'paid' %}
                                        <span class="status-badge status-paid">
                                            <i class="fas fa-check-circle me-1"></i>مدفوع
                                        </span>
                                    {% elif invoice.payment_status == 'cancelled' %}
                                        <span class="status-badge status-cancelled">
                                            <i class="fas fa-times-circle me-1"></i>ملغي
                                        </span>
                                    {% else %}
                                        <span class="status-badge status-pending">
                                            <i class="fas fa-clock me-1"></i>معلق
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div style="text-align: center; padding: 50px; color: rgba(255, 255, 255, 0.6);">
                    <i class="fas fa-file-invoice" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h4>لا توجد فواتير في الفترة المحددة</h4>
                    <p>جرب تغيير فلاتر البحث أو إضافة فواتير جديدة</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تبديل الشريط الجانبي للموبايل
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي للبطاقات
        const cards = document.querySelectorAll('.stat-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // تأثير الظهور التدريجي للجدول
        const table = document.querySelector('.table-responsive');
        if (table) {
            table.style.opacity = '0';
            table.style.transform = 'translateY(30px)';

            setTimeout(() => {
                table.style.transition = 'all 0.6s ease';
                table.style.opacity = '1';
                table.style.transform = 'translateY(0)';
            }, 500);
        }
    });
    </script>
</body>
</html>
'''

# قالب مخططات الاشتراكات
SUBSCRIPTION_CHARTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مخططات الاشتراكات - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* نفس أنماط الشريط الجانبي */
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            color: #00f5ff;
            font-size: 1.8rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(191, 0, 255, 0.2));
            color: #00f5ff;
            border-right: 4px solid #00f5ff;
        }

        .nav-link i {
            width: 20px;
            margin-left: 15px;
            font-size: 1.1rem;
        }

        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .user-name {
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            overflow-x: auto;
        }

        .top-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
        }

        .top-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .action-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .content-area {
            padding: 30px;
        }

        /* قسم الرسوم البيانية */
        .charts-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-title {
            color: #bf00ff;
            font-size: 1.5rem;
            font-weight: 600;
            text-shadow: 0 0 10px #bf00ff;
            margin-bottom: 20px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .chart-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 245, 255, 0.2);
        }

        .chart-title {
            color: #00f5ff;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
        }

        /* إحصائيات سريعة */
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .stat-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            margin-bottom: 10px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            font-weight: 500;
        }

        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 10px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .content-area {
                padding: 20px;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }

            .quick-stats {
                grid-template-columns: 1fr;
            }

            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- زر تبديل الشريط الجانبي للموبايل -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- الشريط الجانبي الأيسر -->
    <div class="sidebar" id="sidebar">
        <!-- شعار النظام -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-rocket"></i>
                نظام إدارة الاشتراكات
            </div>
            <div class="sidebar-subtitle">شركة محمد الجبوري - AdenLink</div>
        </div>

        <!-- قائمة التنقل -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscriptions') }}" class="nav-link">
                    <i class="fas fa-subscription"></i>
                    إدارة الاشتراكات
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('add_subscription') }}" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    إضافة اشتراك جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('analytics') }}" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    التحليلات والتقارير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('invoices') }}" class="nav-link">
                    <i class="fas fa-file-invoice"></i>
                    إدارة الفواتير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('customer_statement') }}" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    كشف حساب العملاء
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscription_charts') }}" class="nav-link active">
                    <i class="fas fa-chart-pie"></i>
                    مخططات الاشتراكات
                </a>
            </div>
            {% if current_user.is_admin() %}
            <div class="nav-item">
                <a href="{{ url_for('users') }}" class="nav-link">
                    <i class="fas fa-users"></i>
                    إدارة المستخدمين
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('providers') }}" class="nav-link">
                    <i class="fas fa-cloud"></i>
                    مزودي الخدمة
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('payment_methods') }}" class="nav-link">
                    <i class="fas fa-credit-card"></i>
                    طرق الدفع
                </a>
            </div>
            {% endif %}
        </nav>

        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-name">{{ current_user.full_name }}</div>
            <div class="user-role">
                {% if current_user.is_admin() %}
                    <i class="fas fa-crown me-1"></i>مدير النظام
                {% else %}
                    <i class="fas fa-user me-1"></i>مستخدم عادي
                {% endif %}
            </div>
            <div style="margin-top: 10px;">
                <a href="{{ url_for('logout') }}" class="action-btn" style="width: 100%; justify-content: center;">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-bar">
            <div class="page-title">
                <i class="fas fa-chart-pie me-2"></i>
                مخططات الاشتراكات
            </div>
            <div class="top-actions">
                <a href="{{ url_for('analytics') }}" class="action-btn">
                    <i class="fas fa-chart-bar"></i>
                    التحليلات
                </a>
                <a href="{{ url_for('subscriptions') }}" class="action-btn">
                    <i class="fas fa-list"></i>
                    الاشتراكات
                </a>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- إحصائيات سريعة -->
            <div class="quick-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-number">{{ charts_data.status_distribution.active }}</div>
                    <div class="stat-label">اشتراكات نشطة</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stat-number">{{ charts_data.status_distribution.expired }}</div>
                    <div class="stat-label">اشتراكات منتهية</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="stat-number">{{ charts_data.status_distribution.suspended }}</div>
                    <div class="stat-label">اشتراكات موقوفة</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <div class="stat-number">{{ charts_data.providers_distribution|length }}</div>
                    <div class="stat-label">مزودي الخدمة</div>
                </div>
            </div>

            <!-- قسم الرسوم البيانية -->
            <div class="charts-section">
                <h3 class="section-title">
                    <i class="fas fa-chart-line me-2"></i>
                    الرسوم البيانية والمخططات
                </h3>

                <div class="charts-grid">
                    <div class="chart-container">
                        <h4 class="chart-title">توزيع الاشتراكات حسب الحالة</h4>
                        <canvas id="statusChart" width="400" height="300"></canvas>
                    </div>

                    <div class="chart-container">
                        <h4 class="chart-title">توزيع الاشتراكات حسب المزودين</h4>
                        <canvas id="providersChart" width="400" height="300"></canvas>
                    </div>

                    <div class="chart-container">
                        <h4 class="chart-title">توزيع الاشتراكات حسب النوع</h4>
                        <canvas id="typesChart" width="400" height="300"></canvas>
                    </div>

                    <div class="chart-container">
                        <h4 class="chart-title">مقارنة شاملة</h4>
                        <canvas id="comparisonChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تبديل الشريط الجانبي للموبايل
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // بيانات الرسوم البيانية
        const chartsData = {{ charts_data|tojson }};

        // رسم توزيع الحالة
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'منتهي', 'موقوف'],
                datasets: [{
                    data: [
                        chartsData.status_distribution.active,
                        chartsData.status_distribution.expired,
                        chartsData.status_distribution.suspended
                    ],
                    backgroundColor: ['#4ade80', '#ff6b6b', '#ffa500'],
                    borderColor: ['#22c55e', '#ef4444', '#f97316'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white',
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                }
            }
        });

        // رسم توزيع المزودين
        const providersCtx = document.getElementById('providersChart').getContext('2d');
        new Chart(providersCtx, {
            type: 'bar',
            data: {
                labels: chartsData.providers_distribution.map(p => p.name),
                datasets: [{
                    label: 'عدد الاشتراكات',
                    data: chartsData.providers_distribution.map(p => p.count),
                    backgroundColor: 'rgba(0, 245, 255, 0.8)',
                    borderColor: '#00f5ff',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white',
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        ticks: {
                            color: 'white'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: 'white'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });

        // رسم توزيع الأنواع
        const typesCtx = document.getElementById('typesChart').getContext('2d');
        new Chart(typesCtx, {
            type: 'pie',
            data: {
                labels: chartsData.types_distribution.map(t => {
                    if (t.type === 'monthly') return 'شهري';
                    if (t.type === 'semi_annual') return 'نصف سنوي';
                    if (t.type === 'annual') return 'سنوي';
                    return t.type;
                }),
                datasets: [{
                    data: chartsData.types_distribution.map(t => t.count),
                    backgroundColor: ['#bf00ff', '#00f5ff', '#4ade80'],
                    borderColor: ['#a855f7', '#0ea5e9', '#22c55e'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white',
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                }
            }
        });

        // رسم المقارنة الشاملة
        const comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
        new Chart(comparisonCtx, {
            type: 'radar',
            data: {
                labels: ['نشط', 'منتهي', 'موقوف', 'شهري', 'نصف سنوي', 'سنوي'],
                datasets: [{
                    label: 'توزيع الاشتراكات',
                    data: [
                        chartsData.status_distribution.active,
                        chartsData.status_distribution.expired,
                        chartsData.status_distribution.suspended,
                        chartsData.types_distribution.find(t => t.type === 'monthly')?.count || 0,
                        chartsData.types_distribution.find(t => t.type === 'semi_annual')?.count || 0,
                        chartsData.types_distribution.find(t => t.type === 'annual')?.count || 0
                    ],
                    backgroundColor: 'rgba(191, 0, 255, 0.2)',
                    borderColor: '#bf00ff',
                    borderWidth: 2,
                    pointBackgroundColor: '#00f5ff',
                    pointBorderColor: '#00f5ff',
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white',
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        ticks: {
                            color: 'white'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        pointLabels: {
                            color: 'white',
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                }
            }
        });

        // تأثير الظهور التدريجي للبطاقات
        const cards = document.querySelectorAll('.stat-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // تأثير الظهور التدريجي للرسوم البيانية
        const chartContainers = document.querySelectorAll('.chart-container');
        chartContainers.forEach((container, index) => {
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';

            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 500 + (index * 200));
        });
    });
    </script>
</body>
</html>
'''

# قالب إدارة الفواتير
INVOICES_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفواتير - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* نفس أنماط الشريط الجانبي */
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            color: #00f5ff;
            font-size: 1.8rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(191, 0, 255, 0.2));
            color: #00f5ff;
            border-right: 4px solid #00f5ff;
        }

        .nav-link i {
            width: 20px;
            margin-left: 15px;
            font-size: 1.1rem;
        }

        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .user-name {
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            overflow-x: auto;
        }

        .top-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
        }

        .top-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .action-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .content-area {
            padding: 30px;
            min-width: 1200px;
        }

        /* قائمة الفواتير الأفقية */
        .invoices-container {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding-bottom: 20px;
        }

        .invoices-container::-webkit-scrollbar {
            height: 8px;
        }

        .invoices-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .invoices-container::-webkit-scrollbar-thumb {
            background: linear-gradient(90deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .invoice-card {
            min-width: 350px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .invoice-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .invoice-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .invoice-number {
            font-size: 1.2rem;
            font-weight: 600;
            color: #00f5ff;
        }

        .invoice-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-paid {
            background: rgba(0, 255, 0, 0.2);
            color: #4ade80;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .status-pending {
            background: rgba(255, 165, 0, 0.2);
            color: #ffa500;
            border: 1px solid rgba(255, 165, 0, 0.3);
        }

        .status-cancelled {
            background: rgba(255, 0, 0, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 0, 0, 0.3);
        }

        .invoice-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detail-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .detail-value {
            color: white;
            font-weight: 600;
        }

        .invoice-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .item-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 8px;
            color: white;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-paid {
            background: linear-gradient(135deg, #4ade80, #22c55e);
        }

        .btn-cancel {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .item-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 10px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .content-area {
                min-width: auto;
                padding: 20px;
            }

            .invoices-container {
                flex-direction: column;
            }

            .invoice-card {
                min-width: auto;
            }

            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- زر تبديل الشريط الجانبي للموبايل -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- الشريط الجانبي الأيسر -->
    <div class="sidebar" id="sidebar">
        <!-- شعار النظام -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-rocket"></i>
                نظام إدارة الاشتراكات
            </div>
            <div class="sidebar-subtitle">شركة محمد الجبوري - AdenLink</div>
        </div>

        <!-- قائمة التنقل -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscriptions') }}" class="nav-link">
                    <i class="fas fa-subscription"></i>
                    إدارة الاشتراكات
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('add_subscription') }}" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    إضافة اشتراك جديد
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('analytics') }}" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    التحليلات والتقارير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('invoices') }}" class="nav-link active">
                    <i class="fas fa-file-invoice"></i>
                    إدارة الفواتير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('customer_statement') }}" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    كشف حساب العملاء
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscription_charts') }}" class="nav-link">
                    <i class="fas fa-chart-pie"></i>
                    مخططات الاشتراكات
                </a>
            </div>
            {% if current_user.is_admin() %}
            <div class="nav-item">
                <a href="{{ url_for('users') }}" class="nav-link">
                    <i class="fas fa-users"></i>
                    إدارة المستخدمين
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('providers') }}" class="nav-link">
                    <i class="fas fa-cloud"></i>
                    مزودي الخدمة
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('payment_methods') }}" class="nav-link">
                    <i class="fas fa-credit-card"></i>
                    طرق الدفع
                </a>
            </div>
            {% endif %}
        </nav>

        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-name">{{ current_user.full_name }}</div>
            <div class="user-role">
                {% if current_user.is_admin() %}
                    <i class="fas fa-crown me-1"></i>مدير النظام
                {% else %}
                    <i class="fas fa-user me-1"></i>مستخدم عادي
                {% endif %}
            </div>
            <div style="margin-top: 10px;">
                <a href="{{ url_for('logout') }}" class="action-btn" style="width: 100%; justify-content: center;">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-bar">
            <div class="page-title">
                <i class="fas fa-file-invoice me-2"></i>
                إدارة الفواتير
            </div>
            <div class="top-actions">
                <a href="{{ url_for('add_invoice') }}" class="action-btn">
                    <i class="fas fa-plus"></i>
                    إضافة فاتورة
                </a>
                <a href="{{ url_for('customer_statement') }}" class="action-btn">
                    <i class="fas fa-file-alt"></i>
                    كشف الحساب
                </a>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <!-- قائمة الفواتير الأفقية -->
            {% if invoices %}
                <div class="invoices-container">
                    {% for invoice in invoices %}
                    <div class="invoice-card">
                        <div class="invoice-header">
                            <div class="invoice-number">{{ invoice.invoice_number }}</div>
                            <div class="invoice-status status-{{ invoice.payment_status }}">
                                {% if invoice.payment_status == 'paid' %}
                                    <i class="fas fa-check-circle me-1"></i>مدفوع
                                {% elif invoice.payment_status == 'cancelled' %}
                                    <i class="fas fa-times-circle me-1"></i>ملغي
                                {% else %}
                                    <i class="fas fa-clock me-1"></i>معلق
                                {% endif %}
                            </div>
                        </div>

                        <div class="invoice-details">
                            <div class="detail-row">
                                <div class="detail-label">الاشتراك</div>
                                <div class="detail-value">{{ invoice.subscription.name if invoice.subscription else 'غير محدد' }}</div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">العميل</div>
                                <div class="detail-value">{{ invoice.user.full_name if invoice.user else 'غير محدد' }}</div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">المبلغ الإجمالي</div>
                                <div class="detail-value">${{ "%.2f"|format(invoice.total_amount) }}</div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">تاريخ الإصدار</div>
                                <div class="detail-value">{{ invoice.issue_date.strftime('%Y-%m-%d') }}</div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label">تاريخ الاستحقاق</div>
                                <div class="detail-value">{{ invoice.due_date.strftime('%Y-%m-%d') }}</div>
                            </div>

                            {% if invoice.payment_method %}
                            <div class="detail-row">
                                <div class="detail-label">طريقة الدفع</div>
                                <div class="detail-value">{{ invoice.payment_method.name }}</div>
                            </div>
                            {% endif %}
                        </div>

                        {% if invoice.payment_status == 'pending' %}
                        <div class="invoice-actions">
                            <a href="{{ url_for('update_invoice_status', invoice_id=invoice.id, status='paid') }}"
                               class="item-btn btn-paid"
                               onclick="return confirm('هل أنت متأكد من تحديث حالة الفاتورة إلى مدفوع؟')">
                                <i class="fas fa-check"></i>
                                تحديد كمدفوع
                            </a>
                            <a href="{{ url_for('update_invoice_status', invoice_id=invoice.id, status='cancelled') }}"
                               class="item-btn btn-cancel"
                               onclick="return confirm('هل أنت متأكد من إلغاء هذه الفاتورة؟')">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}

                    <!-- بطاقة إضافة فاتورة جديدة -->
                    <div class="invoice-card" style="background: rgba(255, 255, 255, 0.05); border: 2px dashed rgba(255, 255, 255, 0.3); display: flex; align-items: center; justify-content: center; min-height: 300px;">
                        <div style="text-align: center; color: rgba(255, 255, 255, 0.5);">
                            <i class="fas fa-plus" style="font-size: 3rem; margin-bottom: 20px;"></i>
                            <div style="font-size: 1.2rem; margin-bottom: 15px;">إضافة فاتورة جديدة</div>
                            <a href="{{ url_for('add_invoice') }}" class="item-btn" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                <i class="fas fa-plus"></i>
                                إضافة
                            </a>
                        </div>
                    </div>
                </div>
            {% else %}
                <div style="text-align: center; padding: 100px 20px; background: rgba(255, 255, 255, 0.05); border-radius: 20px; border: 2px dashed rgba(255, 255, 255, 0.2);">
                    <div style="font-size: 5rem; color: rgba(255, 255, 255, 0.3); margin-bottom: 30px;">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <h3 style="color: rgba(255, 255, 255, 0.7); margin-bottom: 15px;">
                        لا توجد فواتير حالياً
                    </h3>
                    <p style="color: rgba(255, 255, 255, 0.5); margin-bottom: 25px;">
                        ابدأ بإنشاء فاتورتك الأولى
                    </p>
                    <a href="{{ url_for('add_invoice') }}" class="action-btn">
                        <i class="fas fa-plus me-2"></i>
                        إضافة فاتورة جديدة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تبديل الشريط الجانبي للموبايل
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي للبطاقات
        const cards = document.querySelectorAll('.invoice-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateX(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateX(0)';
            }, index * 100);
        });
    });
    </script>
</body>
</html>
'''

# قالب إدارة مزودي الخدمة
PROVIDERS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مزودي الخدمة - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* نفس أنماط الشريط الجانبي */
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 10px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            color: #00f5ff;
            font-size: 1.8rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #00f5ff;
            text-shadow: 0 0 10px #00f5ff;
            transform: translateX(-5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 245, 255, 0.2), rgba(191, 0, 255, 0.2));
            color: #00f5ff;
            border-right: 4px solid #00f5ff;
        }

        .nav-link i {
            width: 20px;
            margin-left: 15px;
            font-size: 1.1rem;
        }

        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .user-name {
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            overflow-x: auto;
        }

        .top-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
        }

        .top-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .action-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .content-area {
            padding: 30px;
        }

        /* قائمة مزودي الخدمة */
        .providers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .provider-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .provider-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .provider-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .provider-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .provider-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 2rem;
            color: white;
        }

        .provider-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .provider-website {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .provider-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 20px;
            text-align: center;
        }

        .provider-stats {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #bf00ff;
            text-shadow: 0 0 10px #bf00ff;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .add-provider-card {
            background: rgba(255, 255, 255, 0.05);
            border: 2px dashed rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 250px;
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
        }

        .add-provider-card:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: #00f5ff;
            color: #00f5ff;
        }

        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 10px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .content-area {
                padding: 20px;
            }

            .providers-grid {
                grid-template-columns: 1fr;
            }

            .sidebar-toggle {
                display: block;
            }
        }
    </style>
</head>
<body>
    <!-- زر تبديل الشريط الجانبي للموبايل -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- الشريط الجانبي الأيسر -->
    <div class="sidebar" id="sidebar">
        <!-- شعار النظام -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <i class="fas fa-rocket"></i>
                نظام إدارة الاشتراكات
            </div>
            <div class="sidebar-subtitle">شركة محمد الجبوري - AdenLink</div>
        </div>

        <!-- قائمة التنقل -->
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="{{ url_for('dashboard') }}" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('subscriptions') }}" class="nav-link">
                    <i class="fas fa-subscription"></i>
                    إدارة الاشتراكات
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('providers') }}" class="nav-link active">
                    <i class="fas fa-cloud"></i>
                    مزودي الخدمة
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('invoices') }}" class="nav-link">
                    <i class="fas fa-file-invoice"></i>
                    إدارة الفواتير
                </a>
            </div>
            <div class="nav-item">
                <a href="{{ url_for('payment_methods') }}" class="nav-link">
                    <i class="fas fa-credit-card"></i>
                    طرق الدفع
                </a>
            </div>
        </nav>

        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-name">{{ current_user.full_name }}</div>
            <div class="user-role">
                <i class="fas fa-crown me-1"></i>مدير النظام
            </div>
            <div style="margin-top: 10px;">
                <a href="{{ url_for('logout') }}" class="action-btn" style="width: 100%; justify-content: center;">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الشريط العلوي -->
        <div class="top-bar">
            <div class="page-title">
                <i class="fas fa-cloud me-2"></i>
                مزودي الخدمة
            </div>
            <div class="top-actions">
                <a href="{{ url_for('add_provider') }}" class="action-btn">
                    <i class="fas fa-plus"></i>
                    إضافة مزود
                </a>
                <a href="{{ url_for('dashboard') }}" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>

        <!-- منطقة المحتوى -->
        <div class="content-area">
            <div class="providers-grid">
                {% for provider in providers %}
                <div class="provider-card">
                    <div class="provider-header">
                        <div class="provider-icon">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <div class="provider-name">{{ provider.name }}</div>
                        {% if provider.website %}
                        <div class="provider-website">{{ provider.website }}</div>
                        {% endif %}
                    </div>

                    {% if provider.description %}
                    <div class="provider-description">
                        {{ provider.description }}
                    </div>
                    {% endif %}

                    <div class="provider-stats">
                        <div class="stat-number">{{ provider.subscriptions|length }}</div>
                        <div class="stat-label">اشتراك نشط</div>
                    </div>
                </div>
                {% endfor %}

                <!-- بطاقة إضافة مزود جديد -->
                <div class="provider-card add-provider-card">
                    <div>
                        <i class="fas fa-plus" style="font-size: 3rem; margin-bottom: 20px;"></i>
                        <div style="font-size: 1.2rem; margin-bottom: 15px;">إضافة مزود خدمة جديد</div>
                        <a href="{{ url_for('add_provider') }}" class="action-btn">
                            <i class="fas fa-plus"></i>
                            إضافة مزود
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تبديل الشريط الجانبي للموبايل
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي للبطاقات
        const cards = document.querySelectorAll('.provider-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
    </script>
</body>
</html>
'''

# قالب إضافة مزود خدمة
ADD_PROVIDER_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مزود خدمة - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            overflow-x: auto;
        }

        .content-area {
            padding: 30px;
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .form-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 30px;
            text-align: center;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00f5ff;
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
            color: white;
            outline: none;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="content-area">
            <div class="form-container">
                <div class="form-card">
                    <h1 class="form-title">
                        <i class="fas fa-cloud me-3"></i>
                        إضافة مزود خدمة جديد
                    </h1>

                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">اسم المزود</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">الرمز (Slug)</label>
                            <input type="text" name="slug" class="form-control" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">رابط الموقع</label>
                            <input type="url" name="website" class="form-control">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف الخدمات</label>
                            <textarea name="description" class="form-control" rows="4"></textarea>
                        </div>

                        <button type="submit" class="submit-btn">
                            <i class="fas fa-save me-2"></i>
                            حفظ المزود
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

# قالب إضافة فاتورة
ADD_INVOICE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة فاتورة - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        .main-content {
            margin: 0;
            min-height: 100vh;
            padding: 30px;
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .form-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 30px;
            text-align: center;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00f5ff;
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
            color: white;
            outline: none;
        }

        .form-select option {
            background: #1a1a2e;
            color: white;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="form-container">
            <div class="form-card">
                <h1 class="form-title">
                    <i class="fas fa-file-invoice me-3"></i>
                    إضافة فاتورة جديدة
                </h1>

                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاشتراك</label>
                            <select name="subscription_id" class="form-select" required>
                                <option value="">اختر الاشتراك</option>
                                {% for subscription in subscriptions %}
                                <option value="{{ subscription.id }}">{{ subscription.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">طريقة الدفع</label>
                            <select name="payment_method_id" class="form-select">
                                <option value="">اختر طريقة الدفع</option>
                                {% for method in payment_methods %}
                                <option value="{{ method.id }}">{{ method.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">المبلغ الفرعي</label>
                            <input type="number" name="subtotal" class="form-control" step="0.01" required>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label class="form-label">معدل الضريبة (%)</label>
                            <input type="number" name="tax_rate" class="form-control" step="0.01" value="0">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label class="form-label">مبلغ الخصم</label>
                            <input type="number" name="discount_amount" class="form-control" step="0.01" value="0">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">أيام الاستحقاق</label>
                            <input type="number" name="due_days" class="form-control" value="30">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-save me-2"></i>
                        إنشاء الفاتورة
                    </button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
'''

# قالب طرق الدفع
PAYMENT_METHODS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طرق الدفع - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        .main-content {
            margin: 0;
            min-height: 100vh;
            padding: 30px;
        }

        .page-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 30px;
            text-align: center;
        }

        .methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .method-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .method-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .method-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 245, 255, 0.3);
        }

        .method-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .method-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 2rem;
            color: white;
        }

        .method-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #00f5ff;
            margin-bottom: 5px;
        }

        .method-type {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .method-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detail-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .detail-value {
            color: white;
            font-weight: 600;
        }

        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(0, 255, 0, 0.2);
            color: #4ade80;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .status-inactive {
            background: rgba(255, 0, 0, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="main-content">
        <h1 class="page-title">
            <i class="fas fa-credit-card me-3"></i>
            طرق الدفع المتاحة
        </h1>

        <div class="methods-grid">
            {% for method in methods %}
            <div class="method-card">
                <div class="method-header">
                    <div class="method-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="method-name">{{ method.name }}</div>
                    <div class="method-type">{{ method.payment_type }}</div>
                </div>

                <div class="method-details">
                    <div class="detail-row">
                        <div class="detail-label">رسوم المعالجة</div>
                        <div class="detail-value">{{ method.processing_fee }}%</div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">العملة المقبولة</div>
                        <div class="detail-value">{{ method.supported_currency }}</div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-label">الحالة</div>
                        <div class="detail-value">
                            <span class="status-badge status-{{ 'active' if method.is_active else 'inactive' }}">
                                {% if method.is_active %}
                                    <i class="fas fa-check-circle me-1"></i>نشط
                                {% else %}
                                    <i class="fas fa-times-circle me-1"></i>غير نشط
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>
'''

# قالب تعديل الاشتراك
EDIT_SUBSCRIPTION_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الاشتراك - نظام إدارة الاشتراكات</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        .main-content {
            margin: 0;
            min-height: 100vh;
            padding: 30px;
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .form-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 30px;
            text-align: center;
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00f5ff;
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
            color: white;
            outline: none;
        }

        .form-select option {
            background: #1a1a2e;
            color: white;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="form-container">
            <div class="form-card">
                <h1 class="form-title">
                    <i class="fas fa-edit me-3"></i>
                    تعديل الاشتراك: {{ subscription.name }}
                </h1>

                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الاشتراك</label>
                            <input type="text" name="name" class="form-control" value="{{ subscription.name }}" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الخدمة</label>
                            <select name="service_type" class="form-select" required>
                                <option value="web_hosting" {{ 'selected' if subscription.service_type == 'web_hosting' else '' }}>استضافة ويب</option>
                                <option value="cloud_server" {{ 'selected' if subscription.service_type == 'cloud_server' else '' }}>خادم سحابي</option>
                                <option value="database" {{ 'selected' if subscription.service_type == 'database' else '' }}>قاعدة بيانات</option>
                                <option value="storage" {{ 'selected' if subscription.service_type == 'storage' else '' }}>تخزين</option>
                                <option value="development" {{ 'selected' if subscription.service_type == 'development' else '' }}>تطوير</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea name="description" class="form-control" rows="3">{{ subscription.description }}</textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">السعر</label>
                            <input type="number" name="price" class="form-control" step="0.01" value="{{ subscription.price }}" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">العملة</label>
                            <select name="currency" class="form-select" required>
                                <option value="USD" {{ 'selected' if subscription.currency == 'USD' else '' }}>دولار أمريكي (USD)</option>
                                <option value="EUR" {{ 'selected' if subscription.currency == 'EUR' else '' }}>يورو (EUR)</option>
                                <option value="IQD" {{ 'selected' if subscription.currency == 'IQD' else '' }}>دينار عراقي (IQD)</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">عنوان الخادم</label>
                            <input type="text" name="server_ip" class="form-control" value="{{ subscription.server_ip }}">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">المنفذ</label>
                            <input type="number" name="port" class="form-control" value="{{ subscription.port }}">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" name="username" class="form-control" value="{{ subscription.username }}">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" name="password" class="form-control" value="{{ subscription.password }}">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">مفتاح API</label>
                            <input type="text" name="api_key" class="form-control" value="{{ subscription.api_key }}">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">المنطقة</label>
                            <input type="text" name="region" class="form-control" value="{{ subscription.region }}">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الأولوية</label>
                        <select name="priority" class="form-select" required>
                            <option value="low" {{ 'selected' if subscription.priority == 'low' else '' }}>منخفضة</option>
                            <option value="medium" {{ 'selected' if subscription.priority == 'medium' else '' }}>متوسطة</option>
                            <option value="high" {{ 'selected' if subscription.priority == 'high' else '' }}>عالية</option>
                            <option value="critical" {{ 'selected' if subscription.priority == 'critical' else '' }}>حرجة</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" class="form-control" rows="3">{{ subscription.notes }}</textarea>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-save me-2"></i>
                        حفظ التعديلات
                    </button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
'''

# المسارات
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('welcome'))

@app.route('/welcome')
def welcome():
    return render_template_string(WELCOME_TEMPLATE)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember_me = request.form.get('remember_me')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user, remember=bool(remember_me))
            flash('تم تسجيل الدخول بنجاح! مرحباً بك في النظام.', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة. جرب: admin / 123456', 'error')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/dashboard')
@login_required
def dashboard():
    # حساب الإحصائيات
    if current_user.is_admin():
        # إحصائيات المدير - جميع البيانات
        total_subscriptions = Subscription.query.count()
        active_subscriptions = Subscription.query.filter_by(status='active').count()
        total_invoices = Invoice.query.count()
        pending_invoices = Invoice.query.filter_by(status='pending').count()
        total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(payment_status='paid').scalar() or 0
        expiring_soon = Subscription.query.filter(
            Subscription.end_date <= date.today() + timedelta(days=30),
            Subscription.status == 'active'
        ).count()
    else:
        # إحصائيات المستخدم العادي - بياناته فقط
        total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
        active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
        total_invoices = Invoice.query.filter_by(user_id=current_user.id).count()
        pending_invoices = Invoice.query.filter_by(user_id=current_user.id, status='pending').count()
        total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(
            user_id=current_user.id, payment_status='paid'
        ).scalar() or 0
        expiring_soon = Subscription.query.filter(
            Subscription.user_id == current_user.id,
            Subscription.end_date <= date.today() + timedelta(days=30),
            Subscription.status == 'active'
        ).count()

    # تمرير البيانات إلى القالب
    stats = {
        'total_subscriptions': total_subscriptions,
        'active_subscriptions': active_subscriptions,
        'total_invoices': total_invoices,
        'pending_invoices': pending_invoices,
        'total_revenue': f"${total_revenue:,.2f}",
        'expiring_soon': expiring_soon
    }

    return render_template_string(DASHBOARD_TEMPLATE, stats=stats)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح. نراك قريباً!', 'success')
    return redirect(url_for('welcome'))

# صفحة إدارة الاشتراكات المتطورة
@app.route('/subscriptions')
@login_required
def subscriptions():
    # جلب الاشتراكات حسب صلاحية المستخدم
    if current_user.is_admin():
        subscriptions_list = Subscription.query.all()
    else:
        subscriptions_list = Subscription.query.filter_by(user_id=current_user.id).all()

    # جلب مزودي الخدمة
    providers = CloudProvider.query.filter_by(is_active=True).all()

    return render_template_string(SUBSCRIPTIONS_TEMPLATE,
                                subscriptions=subscriptions_list,
                                providers=providers)

@app.route('/add_subscription', methods=['GET', 'POST'])
@login_required
def add_subscription():
    if request.method == 'POST':
        try:
            # جلب البيانات من النموذج
            provider_id = request.form.get('provider_id')
            name = request.form.get('name')
            description = request.form.get('description')
            service_type = request.form.get('service_type')
            subscription_type = request.form.get('subscription_type')
            price = float(request.form.get('price', 0))
            currency = request.form.get('currency', 'USD')
            start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d').date()

            # حساب تاريخ الانتهاء
            if subscription_type == 'monthly':
                end_date = start_date + timedelta(days=30)
            elif subscription_type == 'semi_annual':
                end_date = start_date + timedelta(days=180)
            elif subscription_type == 'annual':
                end_date = start_date + timedelta(days=365)

            # معلومات اختيارية
            server_ip = request.form.get('server_ip')
            port = request.form.get('port')
            username = request.form.get('username')
            password = request.form.get('password')
            api_key = request.form.get('api_key')
            region = request.form.get('region')
            priority = request.form.get('priority', 'medium')
            notes = request.form.get('notes')

            # إنشاء الاشتراك الجديد
            new_subscription = Subscription(
                user_id=current_user.id,
                provider_id=provider_id,
                name=name,
                description=description,
                service_type=service_type,
                subscription_type=subscription_type,
                price=price,
                currency=currency,
                start_date=start_date,
                end_date=end_date,
                server_ip=server_ip,
                port=int(port) if port else None,
                username=username,
                password=password,
                api_key=api_key,
                region=region,
                priority=priority,
                notes=notes,
                status='active'
            )

            db.session.add(new_subscription)
            db.session.commit()

            flash('تم إضافة الاشتراك بنجاح!', 'success')
            return redirect(url_for('subscriptions'))

        except Exception as e:
            flash(f'حدث خطأ أثناء إضافة الاشتراك: {str(e)}', 'error')

    # جلب مزودي الخدمة للنموذج
    providers = CloudProvider.query.filter_by(is_active=True).all()
    return render_template_string(ADD_SUBSCRIPTION_TEMPLATE, providers=providers)

@app.route('/analytics')
@login_required
def analytics():
    # حساب بيانات التحليلات
    if current_user.is_admin():
        total_subscriptions = Subscription.query.count()
        active_subscriptions = Subscription.query.filter_by(status='active').count()
        total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(payment_status='paid').scalar() or 0
        monthly_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter(
            Invoice.payment_status == 'paid',
            Invoice.paid_date >= date.today().replace(day=1)
        ).scalar() or 0
    else:
        total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
        active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
        total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(
            user_id=current_user.id, payment_status='paid'
        ).scalar() or 0
        monthly_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter(
            Invoice.user_id == current_user.id,
            Invoice.payment_status == 'paid',
            Invoice.paid_date >= date.today().replace(day=1)
        ).scalar() or 0

    analytics_data = {
        'total_subscriptions': total_subscriptions,
        'active_subscriptions': active_subscriptions,
        'total_revenue': f"${total_revenue:,.2f}",
        'monthly_revenue': f"${monthly_revenue:,.2f}",
        'growth_rate': 15.5,  # يمكن حسابها لاحقاً
        'avg_subscription_value': f"${(total_revenue / max(total_subscriptions, 1)):,.2f}"
    }

    return render_template_string(ANALYTICS_TEMPLATE, analytics=analytics_data)

# صفحة إدارة المستخدمين
@app.route('/users')
@login_required
def users():
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    users_list = User.query.all()
    return render_template_string(USERS_TEMPLATE, users=users_list)

# صفحة إضافة مستخدم جديد
@app.route('/add_user', methods=['GET', 'POST'])
@login_required
def add_user():
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        try:
            username = request.form.get('username')
            email = request.form.get('email')
            full_name = request.form.get('full_name')
            password = request.form.get('password')
            role = request.form.get('role', 'user')
            company = request.form.get('company')
            phone = request.form.get('phone')

            # التحقق من عدم وجود المستخدم مسبقاً
            existing_user = User.query.filter(
                (User.username == username) | (User.email == email)
            ).first()

            if existing_user:
                flash('اسم المستخدم أو البريد الإلكتروني موجود مسبقاً', 'error')
                return redirect(url_for('add_user'))

            # إنشاء المستخدم الجديد
            new_user = User(
                username=username,
                email=email,
                full_name=full_name,
                role=role,
                company=company,
                phone=phone,
                is_active=True,
                email_verified=True
            )
            new_user.set_password(password)

            db.session.add(new_user)
            db.session.commit()

            flash(f'تم إضافة المستخدم {full_name} بنجاح!', 'success')
            return redirect(url_for('users'))

        except Exception as e:
            flash(f'حدث خطأ أثناء إضافة المستخدم: {str(e)}', 'error')

    return render_template_string(ADD_USER_TEMPLATE)

# تعديل حالة المستخدم
@app.route('/toggle_user_status/<int:user_id>')
@login_required
def toggle_user_status(user_id):
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لهذا الإجراء', 'error')
        return redirect(url_for('dashboard'))

    user = db.session.get(User, user_id)
    if user and user.id != current_user.id:  # لا يمكن تعطيل الحساب الحالي
        user.is_active = not user.is_active
        db.session.commit()
        status = 'تم تفعيل' if user.is_active else 'تم تعطيل'
        flash(f'{status} حساب {user.full_name}', 'success')
    else:
        flash('لا يمكن تعديل هذا المستخدم', 'error')

    return redirect(url_for('users'))

# صفحة إدارة مزودي الخدمة
@app.route('/providers')
@login_required
def providers():
    providers_list = CloudProvider.query.all()
    return render_template_string(PROVIDERS_TEMPLATE, providers=providers_list)

# صفحة إضافة مزود خدمة جديد
@app.route('/add_provider', methods=['GET', 'POST'])
@login_required
def add_provider():
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        try:
            name = request.form.get('name')
            slug = request.form.get('slug')
            website = request.form.get('website')
            description = request.form.get('description')

            # التحقق من عدم وجود المزود مسبقاً
            existing_provider = CloudProvider.query.filter_by(slug=slug).first()
            if existing_provider:
                flash('هذا المزود موجود مسبقاً', 'error')
                return redirect(url_for('add_provider'))

            new_provider = CloudProvider(
                name=name,
                slug=slug,
                website=website,
                description=description,
                is_active=True
            )

            db.session.add(new_provider)
            db.session.commit()

            flash(f'تم إضافة مزود الخدمة {name} بنجاح!', 'success')
            return redirect(url_for('providers'))

        except Exception as e:
            flash(f'حدث خطأ أثناء إضافة مزود الخدمة: {str(e)}', 'error')

    return render_template_string(ADD_PROVIDER_TEMPLATE)

# صفحة إدارة الفواتير
@app.route('/invoices')
@login_required
def invoices():
    if current_user.is_admin():
        invoices_list = Invoice.query.all()
    else:
        invoices_list = Invoice.query.filter_by(user_id=current_user.id).all()

    return render_template_string(INVOICES_TEMPLATE, invoices=invoices_list)

# صفحة إضافة فاتورة جديدة
@app.route('/add_invoice', methods=['GET', 'POST'])
@login_required
def add_invoice():
    if request.method == 'POST':
        try:
            subscription_id = request.form.get('subscription_id')
            payment_method_id = request.form.get('payment_method_id')
            subtotal = float(request.form.get('subtotal', 0))
            tax_rate = float(request.form.get('tax_rate', 0))
            discount_amount = float(request.form.get('discount_amount', 0))
            due_days = int(request.form.get('due_days', 30))
            notes = request.form.get('notes')

            # التحقق من الاشتراك
            subscription = db.session.get(Subscription, subscription_id)
            if not subscription:
                flash('الاشتراك المحدد غير موجود', 'error')
                return redirect(url_for('add_invoice'))

            # التحقق من الصلاحية
            if not current_user.is_admin() and subscription.user_id != current_user.id:
                flash('ليس لديك صلاحية لإنشاء فاتورة لهذا الاشتراك', 'error')
                return redirect(url_for('add_invoice'))

            # إنشاء الفاتورة
            new_invoice = Invoice(
                user_id=subscription.user_id,
                subscription_id=subscription_id,
                payment_method_id=payment_method_id if payment_method_id else None,
                subtotal=subtotal,
                tax_rate=tax_rate,
                discount_amount=discount_amount,
                issue_date=date.today(),
                due_date=date.today() + timedelta(days=due_days),
                notes=notes,
                currency=subscription.currency
            )

            # حساب المبلغ الإجمالي
            new_invoice.calculate_total()

            db.session.add(new_invoice)
            db.session.commit()

            flash(f'تم إنشاء الفاتورة {new_invoice.invoice_number} بنجاح!', 'success')
            return redirect(url_for('invoices'))

        except Exception as e:
            flash(f'حدث خطأ أثناء إنشاء الفاتورة: {str(e)}', 'error')

    # جلب الاشتراكات وطرق الدفع للنموذج
    if current_user.is_admin():
        subscriptions = Subscription.query.filter_by(status='active').all()
    else:
        subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').all()

    payment_methods = PaymentMethod.query.filter_by(is_active=True).all()

    return render_template_string(ADD_INVOICE_TEMPLATE,
                                subscriptions=subscriptions,
                                payment_methods=payment_methods)

# تحديث حالة الفاتورة
@app.route('/update_invoice_status/<int:invoice_id>/<status>')
@login_required
def update_invoice_status(invoice_id, status):
    invoice = db.session.get(Invoice, invoice_id)
    if not invoice:
        flash('الفاتورة غير موجودة', 'error')
        return redirect(url_for('invoices'))

    # التحقق من الصلاحية
    if not current_user.is_admin() and invoice.user_id != current_user.id:
        flash('ليس لديك صلاحية لتعديل هذه الفاتورة', 'error')
        return redirect(url_for('invoices'))

    if status in ['paid', 'cancelled']:
        invoice.payment_status = status
        if status == 'paid':
            invoice.paid_date = date.today()
            invoice.status = 'paid'
        else:
            invoice.status = 'cancelled'

        db.session.commit()
        flash(f'تم تحديث حالة الفاتورة {invoice.invoice_number}', 'success')
    else:
        flash('حالة غير صحيحة', 'error')

    return redirect(url_for('invoices'))

# صفحة إدارة طرق الدفع
@app.route('/payment_methods')
@login_required
def payment_methods():
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    methods_list = PaymentMethod.query.all()
    return render_template_string(PAYMENT_METHODS_TEMPLATE, methods=methods_list)

# تجديد الاشتراك
@app.route('/renew_subscription/<int:subscription_id>')
@login_required
def renew_subscription(subscription_id):
    subscription = db.session.get(Subscription, subscription_id)
    if not subscription:
        flash('الاشتراك غير موجود', 'error')
        return redirect(url_for('subscriptions'))

    # التحقق من الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        flash('ليس لديك صلاحية لتجديد هذا الاشتراك', 'error')
        return redirect(url_for('subscriptions'))

    # حساب تاريخ الانتهاء الجديد
    if subscription.subscription_type == 'monthly':
        new_end_date = subscription.end_date + timedelta(days=30)
    elif subscription.subscription_type == 'semi_annual':
        new_end_date = subscription.end_date + timedelta(days=180)
    elif subscription.subscription_type == 'annual':
        new_end_date = subscription.end_date + timedelta(days=365)

    subscription.end_date = new_end_date
    subscription.status = 'active'
    subscription.updated_at = datetime.utcnow()

    # إنشاء فاتورة تجديد تلقائية
    renewal_invoice = Invoice(
        user_id=subscription.user_id,
        subscription_id=subscription.id,
        subtotal=subscription.price,
        tax_rate=0.0,
        discount_amount=0.0,
        issue_date=date.today(),
        due_date=date.today() + timedelta(days=30),
        notes=f'فاتورة تجديد الاشتراك {subscription.name}',
        currency=subscription.currency
    )
    renewal_invoice.calculate_total()

    db.session.add(renewal_invoice)
    db.session.commit()

    flash(f'تم تجديد الاشتراك {subscription.name} بنجاح! تم إنشاء فاتورة {renewal_invoice.invoice_number}', 'success')
    return redirect(url_for('subscriptions'))

# تعديل الاشتراك
@app.route('/edit_subscription/<int:subscription_id>', methods=['GET', 'POST'])
@login_required
def edit_subscription(subscription_id):
    subscription = db.session.get(Subscription, subscription_id)
    if not subscription:
        flash('الاشتراك غير موجود', 'error')
        return redirect(url_for('subscriptions'))

    # التحقق من الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        flash('ليس لديك صلاحية لتعديل هذا الاشتراك', 'error')
        return redirect(url_for('subscriptions'))

    if request.method == 'POST':
        try:
            # تحديث البيانات
            subscription.name = request.form.get('name')
            subscription.description = request.form.get('description')
            subscription.service_type = request.form.get('service_type')
            subscription.price = float(request.form.get('price', 0))
            subscription.currency = request.form.get('currency', 'USD')
            subscription.server_ip = request.form.get('server_ip')
            subscription.port = int(request.form.get('port')) if request.form.get('port') else None
            subscription.username = request.form.get('username')
            subscription.password = request.form.get('password')
            subscription.api_key = request.form.get('api_key')
            subscription.region = request.form.get('region')
            subscription.priority = request.form.get('priority', 'medium')
            subscription.notes = request.form.get('notes')
            subscription.updated_at = datetime.utcnow()

            db.session.commit()
            flash(f'تم تحديث الاشتراك {subscription.name} بنجاح!', 'success')
            return redirect(url_for('subscriptions'))

        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث الاشتراك: {str(e)}', 'error')

    providers = CloudProvider.query.filter_by(is_active=True).all()
    return render_template_string(EDIT_SUBSCRIPTION_TEMPLATE,
                                subscription=subscription,
                                providers=providers)

# كشف حساب العملاء
@app.route('/customer_statement')
@login_required
def customer_statement():
    # الحصول على التاريخ من المعاملات
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    user_id = request.args.get('user_id')

    # تحديد التواريخ الافتراضية (آخر 30 يوم)
    if not start_date:
        start_date = (date.today() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = date.today().strftime('%Y-%m-%d')

    # بناء الاستعلام
    query = Invoice.query

    # تطبيق فلتر المستخدم
    if current_user.is_admin():
        if user_id:
            query = query.filter_by(user_id=user_id)
        users_list = User.query.all()
    else:
        query = query.filter_by(user_id=current_user.id)
        users_list = [current_user]

    # تطبيق فلتر التاريخ
    query = query.filter(
        Invoice.issue_date >= datetime.strptime(start_date, '%Y-%m-%d').date(),
        Invoice.issue_date <= datetime.strptime(end_date, '%Y-%m-%d').date()
    )

    invoices = query.order_by(Invoice.issue_date.desc()).all()

    # حساب الإحصائيات
    total_amount = sum(invoice.total_amount for invoice in invoices)
    paid_amount = sum(invoice.total_amount for invoice in invoices if invoice.payment_status == 'paid')
    pending_amount = total_amount - paid_amount

    stats = {
        'total_invoices': len(invoices),
        'total_amount': total_amount,
        'paid_amount': paid_amount,
        'pending_amount': pending_amount,
        'start_date': start_date,
        'end_date': end_date
    }

    return render_template_string(CUSTOMER_STATEMENT_TEMPLATE,
                                invoices=invoices,
                                stats=stats,
                                users=users_list,
                                selected_user_id=user_id)

# حذف الاشتراك
@app.route('/delete_subscription/<int:subscription_id>')
@login_required
def delete_subscription(subscription_id):
    subscription = db.session.get(Subscription, subscription_id)
    if not subscription:
        flash('الاشتراك غير موجود', 'error')
        return redirect(url_for('subscriptions'))

    # التحقق من الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        flash('ليس لديك صلاحية لحذف هذا الاشتراك', 'error')
        return redirect(url_for('subscriptions'))

    subscription_name = subscription.name
    db.session.delete(subscription)
    db.session.commit()

    flash(f'تم حذف الاشتراك {subscription_name} بنجاح!', 'success')
    return redirect(url_for('subscriptions'))

# تحديث الشريط الجانبي في لوحة التحكم
@app.route('/subscription_charts')
@login_required
def subscription_charts():
    # إحصائيات الاشتراكات
    if current_user.is_admin():
        total_subscriptions = Subscription.query.count()
        active_subscriptions = Subscription.query.filter_by(status='active').count()
        expired_subscriptions = Subscription.query.filter_by(status='expired').count()
        suspended_subscriptions = Subscription.query.filter_by(status='suspended').count()

        # توزيع حسب المزودين
        providers_data = db.session.query(
            CloudProvider.name,
            db.func.count(Subscription.id).label('count')
        ).join(Subscription).group_by(CloudProvider.name).all()

        # توزيع حسب نوع الاشتراك
        subscription_types = db.session.query(
            Subscription.subscription_type,
            db.func.count(Subscription.id).label('count')
        ).group_by(Subscription.subscription_type).all()

    else:
        total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
        active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
        expired_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='expired').count()
        suspended_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='suspended').count()

        providers_data = db.session.query(
            CloudProvider.name,
            db.func.count(Subscription.id).label('count')
        ).join(Subscription).filter(Subscription.user_id == current_user.id).group_by(CloudProvider.name).all()

        subscription_types = db.session.query(
            Subscription.subscription_type,
            db.func.count(Subscription.id).label('count')
        ).filter_by(user_id=current_user.id).group_by(Subscription.subscription_type).all()

    charts_data = {
        'status_distribution': {
            'active': active_subscriptions,
            'expired': expired_subscriptions,
            'suspended': suspended_subscriptions
        },
        'providers_distribution': [{'name': p.name, 'count': p.count} for p in providers_data],
        'types_distribution': [{'type': t.subscription_type, 'count': t.count} for t in subscription_types]
    }

    return render_template_string(SUBSCRIPTION_CHARTS_TEMPLATE, charts_data=charts_data)

@app.route('/communication')
@login_required
def communication():
    return render_template_string('''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>مركز التواصل - نظام إدارة الاشتراكات</title>

        <!-- Bootstrap RTL CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

        <!-- Font Awesome -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

        <!-- Google Fonts - Cairo -->
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

        <style>
            body {
                font-family: 'Cairo', sans-serif;
                background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
                color: white;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }

            .coming-soon-container {
                text-align: center;
                max-width: 600px;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 25px;
                padding: 60px 40px;
                position: relative;
                overflow: hidden;
            }

            .coming-soon-container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
            }

            .coming-soon-icon {
                font-size: 5rem;
                color: #00f5ff;
                text-shadow: 0 0 30px #00f5ff;
                margin-bottom: 30px;
                animation: iconGlow 2s ease-in-out infinite alternate;
            }

            @keyframes iconGlow {
                from {
                    text-shadow: 0 0 20px #00f5ff, 0 0 30px #00f5ff, 0 0 40px #00f5ff;
                }
                to {
                    text-shadow: 0 0 10px #00f5ff, 0 0 20px #00f5ff, 0 0 30px #00f5ff;
                }
            }

            .coming-soon-title {
                font-size: 2.5rem;
                font-weight: 700;
                color: #00f5ff;
                margin-bottom: 20px;
                text-shadow: 0 0 20px #00f5ff;
            }

            .coming-soon-subtitle {
                font-size: 1.2rem;
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 30px;
                line-height: 1.6;
            }

            .features-preview {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 15px;
                padding: 25px;
                margin: 30px 0;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            .features-title {
                color: #bf00ff;
                font-size: 1.3rem;
                font-weight: 600;
                margin-bottom: 20px;
                text-shadow: 0 0 10px #bf00ff;
            }

            .feature-list {
                list-style: none;
                padding: 0;
            }

            .feature-item {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
                color: rgba(255, 255, 255, 0.7);
            }

            .feature-item i {
                color: #00f5ff;
                margin-left: 15px;
                font-size: 1.2rem;
            }

            .back-btn {
                display: inline-block;
                padding: 15px 30px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                text-decoration: none;
                border-radius: 50px;
                font-weight: 600;
                transition: all 0.3s ease;
                margin-top: 20px;
            }

            .back-btn:hover {
                transform: translateY(-3px);
                box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
                color: white;
            }

            .progress-bar {
                width: 100%;
                height: 8px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                margin: 20px 0;
                overflow: hidden;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #00f5ff, #bf00ff);
                border-radius: 10px;
                width: 75%;
                animation: progressAnimation 2s ease-in-out;
            }

            @keyframes progressAnimation {
                from { width: 0%; }
                to { width: 75%; }
            }

            .eta {
                color: rgba(255, 255, 255, 0.6);
                font-size: 0.9rem;
                margin-top: 10px;
            }
        </style>
    </head>
    <body>
        <div class="coming-soon-container">
            <div class="coming-soon-icon">
                <i class="fas fa-envelope"></i>
            </div>

            <h1 class="coming-soon-title">مركز التواصل</h1>

            <p class="coming-soon-subtitle">
                نعمل حالياً على تطوير مركز التواصل المتطور لإرسال الرسائل والإشعارات التلقائية لعملائك
            </p>

            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <div class="eta">مكتمل بنسبة 75% - متوقع الإطلاق قريباً</div>

            <div class="features-preview">
                <h3 class="features-title">
                    <i class="fas fa-star me-2"></i>
                    الميزات القادمة
                </h3>

                <ul class="feature-list">
                    <li class="feature-item">
                        <i class="fas fa-paper-plane"></i>
                        إرسال رسائل بريد إلكتروني تلقائية
                    </li>
                    <li class="feature-item">
                        <i class="fas fa-sms"></i>
                        إرسال رسائل SMS للعملاء
                    </li>
                    <li class="feature-item">
                        <i class="fas fa-bell"></i>
                        تنبيهات انتهاء الاشتراكات
                    </li>
                    <li class="feature-item">
                        <i class="fas fa-file-invoice"></i>
                        إرسال الفواتير تلقائياً
                    </li>
                    <li class="feature-item">
                        <i class="fas fa-template"></i>
                        قوالب رسائل جاهزة ومخصصة
                    </li>
                    <li class="feature-item">
                        <i class="fas fa-chart-bar"></i>
                        تقارير معدلات الفتح والاستجابة
                    </li>
                </ul>
            </div>

            <a href="{{ url_for('dashboard') }}" class="back-btn">
                <i class="fas fa-arrow-right me-2"></i>
                العودة إلى لوحة التحكم
            </a>
        </div>

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الظهور التدريجي
            const container = document.querySelector('.coming-soon-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(50px) scale(0.9)';

            setTimeout(() => {
                container.style.transition = 'all 0.8s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0) scale(1)';
            }, 200);

            // تأثير الظهور التدريجي للميزات
            const features = document.querySelectorAll('.feature-item');
            features.forEach((feature, index) => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateX(30px)';

                setTimeout(() => {
                    feature.style.transition = 'all 0.5s ease';
                    feature.style.opacity = '1';
                    feature.style.transform = 'translateX(0)';
                }, 1000 + (index * 100));
            });
        });
        </script>
    </body>
    </html>
    ''')

# إنشاء قاعدة البيانات والبيانات الأولية
def initialize_database():
    """إنشاء قاعدة البيانات والبيانات الأولية"""
    with app.app_context():
        # إنشاء الجداول
        db.create_all()

        # إنشاء المستخدم الافتراضي
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام - شركة محمد الجبوري',
                role='admin',
                company='شركة محمد الجبوري - AdenLink',
                phone='+964-XXX-XXXX',
                email_verified=True
            )
            admin_user.set_password('123456')
            db.session.add(admin_user)
            print("✅ تم إنشاء المستخدم الافتراضي")

        # إنشاء مزودي الخدمة السحابية
        providers_data = [
            {
                'name': 'AdenLink - شركة محمد الجبوري',
                'slug': 'adenlink',
                'website': 'https://adenlink.com',
                'description': 'شركة محمد الجبوري المتخصصة في الخدمات السحابية والتقنية المتطورة'
            },
            {
                'name': 'Amazon Web Services (AWS)',
                'slug': 'aws',
                'website': 'https://aws.amazon.com',
                'description': 'منصة الخدمات السحابية الرائدة من أمازون'
            },
            {
                'name': 'Google Cloud Platform (GCP)',
                'slug': 'gcp',
                'website': 'https://cloud.google.com',
                'description': 'منصة الخدمات السحابية من جوجل'
            },
            {
                'name': 'Microsoft Azure',
                'slug': 'azure',
                'website': 'https://azure.microsoft.com',
                'description': 'منصة الخدمات السحابية من مايكروسوفت'
            },
            {
                'name': 'DigitalOcean',
                'slug': 'digitalocean',
                'website': 'https://digitalocean.com',
                'description': 'منصة الخدمات السحابية المبسطة للمطورين'
            },
            {
                'name': 'Vultr',
                'slug': 'vultr',
                'website': 'https://vultr.com',
                'description': 'خدمات الخوادم السحابية عالية الأداء'
            },
            {
                'name': 'Linode',
                'slug': 'linode',
                'website': 'https://linode.com',
                'description': 'خدمات الخوادم السحابية والاستضافة'
            }
        ]

        for provider_data in providers_data:
            existing_provider = CloudProvider.query.filter_by(slug=provider_data['slug']).first()
            if not existing_provider:
                provider = CloudProvider(**provider_data)
                db.session.add(provider)

        # إنشاء طرق الدفع
        payment_methods_data = [
            {
                'name': 'بطاقة ائتمان/خصم',
                'type': 'credit_card',
                'processing_fee': 2.9,
                'description': 'دفع بالبطاقات الائتمانية (Visa, MasterCard, American Express)'
            },
            {
                'name': 'PayPal',
                'type': 'paypal',
                'processing_fee': 3.4,
                'description': 'دفع عبر PayPal'
            },
            {
                'name': 'تحويل بنكي',
                'type': 'bank_transfer',
                'processing_fee': 0.0,
                'description': 'تحويل بنكي مباشر'
            },
            {
                'name': 'العملات المشفرة',
                'type': 'crypto',
                'processing_fee': 1.0,
                'description': 'دفع بالعملات المشفرة (Bitcoin, Ethereum)'
            },
            {
                'name': 'Apple Pay',
                'type': 'apple_pay',
                'processing_fee': 2.9,
                'description': 'دفع عبر Apple Pay'
            },
            {
                'name': 'Google Pay',
                'type': 'google_pay',
                'processing_fee': 2.9,
                'description': 'دفع عبر Google Pay'
            }
        ]

        for method_data in payment_methods_data:
            existing_method = PaymentMethod.query.filter_by(name=method_data['name']).first()
            if not existing_method:
                method = PaymentMethod(**method_data)
                db.session.add(method)

        # حفظ التغييرات
        db.session.commit()

        # إحصائيات سريعة
        total_users = User.query.count()
        total_providers = CloudProvider.query.count()
        total_payment_methods = PaymentMethod.query.count()

        print(f"📊 إحصائيات النظام:")
        print(f"   👥 عدد المستخدمين: {total_users}")
        print(f"   ☁️  عدد مزودي الخدمة: {total_providers}")
        print(f"   💳 عدد طرق الدفع: {total_payment_methods}")
        print()

        return True

def create_sample_data():
    """إنشاء بيانات تجريبية للنظام"""
    try:
        # إنشاء مستخدمين تجريبيين
        if User.query.count() <= 1:  # إذا كان هناك مستخدم واحد فقط (المدير)
            sample_users = [
                {
                    'username': 'ahmed_ali',
                    'email': '<EMAIL>',
                    'full_name': 'أحمد علي محمد',
                    'role': 'user',
                    'company': 'شركة التقنية المتقدمة',
                    'phone': '+***********-4567',
                    'password': '123456'
                },
                {
                    'username': 'sara_hassan',
                    'email': '<EMAIL>',
                    'full_name': 'سارة حسن أحمد',
                    'role': 'user',
                    'company': 'مؤسسة الابتكار الرقمي',
                    'phone': '+***********-5678',
                    'password': '123456'
                },
                {
                    'username': 'omar_khalil',
                    'email': '<EMAIL>',
                    'full_name': 'عمر خليل إبراهيم',
                    'role': 'admin',
                    'company': 'AdenLink',
                    'phone': '+***********-6789',
                    'password': '123456'
                }
            ]

            for user_data in sample_users:
                existing_user = User.query.filter(
                    (User.username == user_data['username']) |
                    (User.email == user_data['email'])
                ).first()

                if not existing_user:
                    new_user = User(
                        username=user_data['username'],
                        email=user_data['email'],
                        full_name=user_data['full_name'],
                        role=user_data['role'],
                        company=user_data['company'],
                        phone=user_data['phone'],
                        is_active=True,
                        email_verified=True
                    )
                    new_user.set_password(user_data['password'])
                    db.session.add(new_user)

            db.session.commit()

        # إنشاء اشتراكات تجريبية
        if Subscription.query.count() == 0:
            admin_user = User.query.filter_by(role='admin').first()
            regular_users = User.query.filter_by(role='user').all()
            providers = CloudProvider.query.all()

            if admin_user and providers:
                sample_subscriptions = [
                    {
                        'name': 'خادم ويب رئيسي - AdenLink',
                        'description': 'خادم ويب رئيسي لموقع الشركة مع قاعدة بيانات',
                        'provider_id': providers[0].id,  # AdenLink
                        'user_id': admin_user.id,
                        'service_type': 'web_hosting',
                        'subscription_type': 'annual',
                        'price': 1200.00,
                        'currency': 'USD',
                        'server_ip': '*************',
                        'port': 443,
                        'username': 'adenlink_admin',
                        'password': 'SecurePass2024!',
                        'region': 'Middle East',
                        'priority': 'critical',
                        'status': 'active',
                        'start_date': date.today() - timedelta(days=90),
                        'end_date': date.today() + timedelta(days=275),
                        'notes': 'خادم رئيسي للشركة - أولوية قصوى'
                    },
                    {
                        'name': 'خادم التطبيقات - AWS',
                        'description': 'خادم تطبيقات للمشاريع الداخلية',
                        'provider_id': next((p.id for p in providers if p.slug == 'aws'), providers[1].id),
                        'user_id': admin_user.id,
                        'service_type': 'cloud_server',
                        'subscription_type': 'monthly',
                        'price': 150.00,
                        'currency': 'USD',
                        'server_ip': '************',
                        'port': 22,
                        'username': 'ec2-user',
                        'api_key': 'AKIA1234567890ABCDEF',
                        'region': 'us-east-1',
                        'priority': 'high',
                        'status': 'active',
                        'start_date': date.today() - timedelta(days=30),
                        'end_date': date.today() + timedelta(days=30),
                        'notes': 'خادم تطبيقات للمشاريع الجديدة'
                    },
                    {
                        'name': 'قاعدة بيانات - Azure',
                        'description': 'قاعدة بيانات SQL للعملاء',
                        'provider_id': next((p.id for p in providers if p.slug == 'azure'), providers[2].id),
                        'user_id': regular_users[0].id if regular_users else admin_user.id,
                        'service_type': 'database',
                        'subscription_type': 'semi_annual',
                        'price': 800.00,
                        'currency': 'USD',
                        'server_ip': 'adenlink-db.database.windows.net',
                        'port': 1433,
                        'username': 'dbadmin',
                        'password': 'DbPass2024!',
                        'region': 'West Europe',
                        'priority': 'high',
                        'status': 'active',
                        'start_date': date.today() - timedelta(days=60),
                        'end_date': date.today() + timedelta(days=120),
                        'notes': 'قاعدة بيانات العملاء الرئيسية'
                    },
                    {
                        'name': 'خدمة التخزين السحابي',
                        'description': 'تخزين الملفات والنسخ الاحتياطية',
                        'provider_id': next((p.id for p in providers if p.slug == 'google-cloud'), providers[3].id),
                        'user_id': regular_users[1].id if len(regular_users) > 1 else admin_user.id,
                        'service_type': 'storage',
                        'subscription_type': 'monthly',
                        'price': 75.00,
                        'currency': 'USD',
                        'api_key': 'AIzaSyB1234567890abcdefghijk',
                        'region': 'europe-west1',
                        'priority': 'medium',
                        'status': 'active',
                        'start_date': date.today() - timedelta(days=15),
                        'end_date': date.today() + timedelta(days=15),
                        'notes': 'تخزين النسخ الاحتياطية'
                    },
                    {
                        'name': 'خادم تطوير منتهي',
                        'description': 'خادم تطوير قديم - منتهي الصلاحية',
                        'provider_id': providers[4].id if len(providers) > 4 else providers[0].id,
                        'user_id': admin_user.id,
                        'service_type': 'development',
                        'subscription_type': 'monthly',
                        'price': 50.00,
                        'currency': 'USD',
                        'server_ip': '*************',
                        'port': 22,
                        'username': 'dev_user',
                        'priority': 'low',
                        'status': 'expired',
                        'start_date': date.today() - timedelta(days=120),
                        'end_date': date.today() - timedelta(days=30),
                        'notes': 'خادم تطوير قديم - يحتاج تجديد'
                    }
                ]

                for sub_data in sample_subscriptions:
                    new_subscription = Subscription(**sub_data)
                    db.session.add(new_subscription)

                db.session.commit()

        # إنشاء فواتير تجريبية
        if Invoice.query.count() == 0:
            subscriptions = Subscription.query.all()
            payment_methods = PaymentMethod.query.all()

            if subscriptions and payment_methods:
                sample_invoices = [
                    {
                        'subscription_id': subscriptions[0].id,
                        'user_id': subscriptions[0].user_id,
                        'payment_method_id': payment_methods[0].id,
                        'subtotal': 1200.00,
                        'tax_rate': 0.0,
                        'discount_amount': 0.0,
                        'currency': 'USD',
                        'issue_date': date.today() - timedelta(days=30),
                        'due_date': date.today() + timedelta(days=30),
                        'payment_status': 'paid',
                        'paid_date': date.today() - timedelta(days=25),
                        'status': 'paid',
                        'notes': 'فاتورة الاشتراك السنوي - مدفوعة'
                    },
                    {
                        'subscription_id': subscriptions[1].id,
                        'user_id': subscriptions[1].user_id,
                        'payment_method_id': payment_methods[1].id,
                        'subtotal': 150.00,
                        'tax_rate': 5.0,
                        'discount_amount': 10.0,
                        'currency': 'USD',
                        'issue_date': date.today() - timedelta(days=15),
                        'due_date': date.today() + timedelta(days=15),
                        'payment_status': 'pending',
                        'status': 'pending',
                        'notes': 'فاتورة شهرية - في انتظار الدفع'
                    },
                    {
                        'subscription_id': subscriptions[2].id,
                        'user_id': subscriptions[2].user_id,
                        'payment_method_id': payment_methods[2].id,
                        'subtotal': 800.00,
                        'tax_rate': 0.0,
                        'discount_amount': 50.0,
                        'currency': 'USD',
                        'issue_date': date.today() - timedelta(days=45),
                        'due_date': date.today() - timedelta(days=15),
                        'payment_status': 'paid',
                        'paid_date': date.today() - timedelta(days=40),
                        'status': 'paid',
                        'notes': 'فاتورة نصف سنوية مع خصم'
                    },
                    {
                        'subscription_id': subscriptions[3].id,
                        'user_id': subscriptions[3].user_id,
                        'payment_method_id': payment_methods[0].id,
                        'subtotal': 75.00,
                        'tax_rate': 10.0,
                        'discount_amount': 0.0,
                        'currency': 'USD',
                        'issue_date': date.today() - timedelta(days=5),
                        'due_date': date.today() + timedelta(days=25),
                        'payment_status': 'pending',
                        'status': 'pending',
                        'notes': 'فاتورة شهرية للتخزين السحابي'
                    }
                ]

                for invoice_data in sample_invoices:
                    new_invoice = Invoice(**invoice_data)
                    new_invoice.calculate_total()
                    db.session.add(new_invoice)

                db.session.commit()

        print("✅ تم إنشاء البيانات التجريبية بنجاح!")
        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {str(e)}")
        db.session.rollback()
        return False

if __name__ == '__main__':
    print("🚀 نظام إدارة الاشتراكات المتطور")
    print("مطور بواسطة: المهندس محمد ياسر الجبوري ❤️")
    print("=" * 60)
    
    try:
        # تهيئة قاعدة البيانات
        print("📦 تهيئة قاعدة البيانات...")
        initialize_database()

        # إنشاء البيانات التجريبية
        with app.app_context():
            create_sample_data()

            # عرض الإحصائيات النهائية
            print("📊 إحصائيات النظام النهائية:")
            print(f"   👥 عدد المستخدمين: {User.query.count()}")
            print(f"   ☁️  عدد مزودي الخدمة: {CloudProvider.query.count()}")
            print(f"   💳 عدد طرق الدفع: {PaymentMethod.query.count()}")
            print(f"   📋 عدد الاشتراكات: {Subscription.query.count()}")
            print(f"   🧾 عدد الفواتير: {Invoice.query.count()}")

        print("\n🌐 معلومات الوصول:")
        print("   🔗 الرابط: http://localhost:5000")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: 123456")
        print("=" * 60)
        print("🚀 جاري تشغيل الخادم...")
        print("⚠️  للإيقاف: اضغط Ctrl+C")
        print("=" * 60)
        
        # تشغيل التطبيق
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        print("\n🔧 تأكد من:")
        print("   - تثبيت Flask: pip install flask flask-sqlalchemy flask-login")
        print("   - عدم استخدام المنفذ 5000 من تطبيق آخر")
        input("\nاضغط Enter للخروج...")
