#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 النظام المتكامل والمتطور - النسخة المبسطة
مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
شركة AdenLink - اليافعي
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta
import json

print("🚀 بدء تشغيل النظام المتكامل والمتطور...")

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'ultimate-advanced-subscription-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ultimate_advanced_subscriptions.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# نماذج قاعدة البيانات المبسطة

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user', nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

class CloudProvider(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    website = db.Column(db.String(255))
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='active')
    
    user = db.relationship('User', backref='subscriptions')
    provider = db.relationship('CloudProvider', backref='subscriptions')

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    total_amount = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='pending')
    
    user = db.relationship('User', backref='invoices')
    subscription = db.relationship('Subscription', backref='invoices')

class MessageTemplate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    template_type = db.Column(db.String(50), nullable=False)
    is_active = db.Column(db.Boolean, default=True)

class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    recipient_email = db.Column(db.String(255), nullable=False)
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    sent_at = db.Column(db.DateTime, default=datetime.utcnow)
    delivery_status = db.Column(db.String(20), default='sent')
    
    sender = db.relationship('User', backref='sent_messages')

# دوال مساعدة

def get_dashboard_stats():
    try:
        if current_user.is_admin():
            total_subscriptions = Subscription.query.count()
            active_subscriptions = Subscription.query.filter_by(status='active').count()
            total_invoices = Invoice.query.count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(status='paid').scalar() or 0
        else:
            total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
            active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
            total_invoices = Invoice.query.filter_by(user_id=current_user.id).count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(user_id=current_user.id, status='paid').scalar() or 0

        return {
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'expired_subscriptions': total_subscriptions - active_subscriptions,
            'total_invoices': total_invoices,
            'total_revenue': total_revenue
        }
    except:
        return {
            'total_subscriptions': 0,
            'active_subscriptions': 0,
            'expired_subscriptions': 0,
            'total_invoices': 0,
            'total_revenue': 0
        }

def generate_invoice_number():
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return f"INV-{timestamp}"

# قوالب HTML

LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }
        .logo-section {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo-icon {
            font-size: 4rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .system-title {
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }
        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            padding: 1rem;
        }
        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            color: white;
        }
        .btn-login {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            width: 100%;
        }
        .demo-credentials {
            background: rgba(0, 212, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <div class="logo-icon">
                <i class="fas fa-rocket"></i>
            </div>
            <h1 class="system-title">النظام المتكامل والمتطور</h1>
            <p>مطور بواسطة: المهندس محمد ياسر الجبوري ❤️</p>
            <p>شركة AdenLink - اليافعي</p>
        </div>
        
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-danger">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <div class="mb-3">
                <input type="text" class="form-control" name="username" placeholder="اسم المستخدم" required>
            </div>
            <div class="mb-3">
                <input type="password" class="form-control" name="password" placeholder="كلمة المرور" required>
            </div>
            <button type="submit" class="btn btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
            </button>
        </form>
        
        <div class="demo-credentials">
            <div><strong>بيانات التجربة:</strong></div>
            <div>اسم المستخدم: <strong>admin</strong></div>
            <div>كلمة المرور: <strong>123456</strong></div>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
        }
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-left: 2px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            overflow-y: auto;
        }
        .sidebar-header {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }
        .logo {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }
        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .nav-link:hover {
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
            border-left-color: #00d4ff;
        }
        .nav-link i {
            width: 20px;
            margin-left: 1rem;
        }
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        .top-bar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .page-title {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 0.5rem;
        }
        .btn-logout {
            background: linear-gradient(135deg, #ff006e, #b537f2);
            border: none;
            border-radius: 12px;
            padding: 0.8rem 1.5rem;
            color: white;
            text-decoration: none;
        }
        @media (max-width: 768px) {
            .sidebar { transform: translateX(100%); }
            .main-content { margin-right: 0; }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo"><i class="fas fa-rocket"></i></div>
            <div>النظام المتكامل</div>
            <div>محمد ياسر الجبوري</div>
        </div>
        
        <nav>
            <a href="{{ url_for('dashboard') }}" class="nav-link">
                <i class="fas fa-tachometer-alt"></i>لوحة التحكم
            </a>
            <a href="{{ url_for('subscription_charts') }}" class="nav-link">
                <i class="fas fa-chart-pie"></i>المخططات التفاعلية
            </a>
            <a href="{{ url_for('subscriptions') }}" class="nav-link">
                <i class="fas fa-server"></i>الاشتراكات
            </a>
            <a href="{{ url_for('invoices') }}" class="nav-link">
                <i class="fas fa-file-invoice"></i>الفواتير
            </a>
            <a href="{{ url_for('message_center') }}" class="nav-link">
                <i class="fas fa-envelope"></i>مركز الرسائل
            </a>
            <a href="{{ url_for('send_message') }}" class="nav-link">
                <i class="fas fa-paper-plane"></i>إرسال رسالة
            </a>
        </nav>
    </div>
    
    <div class="main-content">
        <div class="top-bar">
            <div>
                <h1 class="page-title">لوحة التحكم المتطورة</h1>
                <p class="mb-0">مرحباً بك في النظام المتكامل</p>
            </div>
            <div>
                <span>{{ current_user.full_name }}</span>
                <a href="{{ url_for('logout') }}" class="btn-logout ms-2">
                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                </a>
            </div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_subscriptions }}</div>
                <div>إجمالي الاشتراكات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.active_subscriptions }}</div>
                <div>الاشتراكات النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_invoices }}</div>
                <div>إجمالي الفواتير</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${{ "%.0f"|format(stats.total_revenue) }}</div>
                <div>إجمالي الإيرادات</div>
            </div>
        </div>
    </div>
</body>
</html>
'''

# المسارات الأساسية

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            flash('🎉 مرحباً بك في النظام المتكامل!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('✅ تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    stats = get_dashboard_stats()
    return render_template_string(DASHBOARD_TEMPLATE, stats=stats)

# قوالب الصفحات الأخرى

SUBSCRIPTION_CHARTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>المخططات التفاعلية - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 2rem;
        }
        .page-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 2rem;
        }
        .chart-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
        }
        .chart-container {
            position: relative;
            height: 400px;
        }
        .back-btn {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-chart-pie me-3"></i>
            المخططات التفاعلية المتطورة
        </h1>
        <p class="lead mb-3">رؤى عميقة وتحليلات شاملة لاشتراكاتك</p>
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للوحة التحكم
        </a>
    </div>

    <div class="charts-grid">
        <div class="chart-card">
            <h3 class="text-center mb-4">توزيع الاشتراكات حسب الحالة</h3>
            <div class="chart-container">
                <canvas id="statusChart"></canvas>
            </div>
        </div>

        <div class="chart-card">
            <h3 class="text-center mb-4">الإيرادات الشهرية</h3>
            <div class="chart-container">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // مخطط الحالة
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'منتهي', 'موقوف'],
                datasets: [{
                    data: [{{ stats.active_subscriptions }}, {{ stats.expired_subscriptions }}, 1],
                    backgroundColor: [
                        'rgba(0, 212, 255, 0.8)',
                        'rgba(255, 0, 110, 0.8)',
                        'rgba(255, 170, 0, 0.8)'
                    ],
                    borderColor: ['#00d4ff', '#ff006e', '#ffaa00'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { color: 'white', padding: 20 }
                    }
                }
            }
        });

        // مخطط الإيرادات
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات ($)',
                    data: [1200, 1900, 3000, 5000, 2000, 3000],
                    borderColor: '#00d4ff',
                    backgroundColor: 'rgba(0, 212, 255, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { labels: { color: 'white' } }
                },
                scales: {
                    x: {
                        ticks: { color: 'white' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    },
                    y: {
                        ticks: { color: 'white' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    }
                }
            }
        });
    </script>
</body>
</html>
'''

@app.route('/subscription_charts')
@login_required
def subscription_charts():
    stats = get_dashboard_stats()
    return render_template_string(SUBSCRIPTION_CHARTS_TEMPLATE, stats=stats)

SUBSCRIPTIONS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>قائمة الاشتراكات - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 2rem;
        }
        .page-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .subscription-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .subscription-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.2);
        }
        .back-btn {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <h1><i class="fas fa-list me-3"></i>قائمة الاشتراكات</h1>
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right me-2"></i>العودة
        </a>
    </div>

    <div class="row">
        {% for subscription in subscriptions %}
        <div class="col-md-6 col-lg-4">
            <div class="subscription-card">
                <h5>{{ subscription.name }}</h5>
                <p><strong>المزود:</strong> {{ subscription.provider.name if subscription.provider else 'غير محدد' }}</p>
                <p><strong>السعر:</strong> {{ subscription.price }} {{ subscription.currency }}</p>
                <p><strong>الحالة:</strong>
                    <span class="badge {% if subscription.status == 'active' %}bg-success{% else %}bg-danger{% endif %}">
                        {{ subscription.status }}
                    </span>
                </p>
                <p><strong>تاريخ الانتهاء:</strong> {{ subscription.end_date }}</p>
            </div>
        </div>
        {% endfor %}
    </div>
</body>
</html>
'''

@app.route('/subscriptions')
@login_required
def subscriptions():
    if current_user.is_admin():
        subscriptions = Subscription.query.all()
    else:
        subscriptions = Subscription.query.filter_by(user_id=current_user.id).all()

    return render_template_string(SUBSCRIPTIONS_TEMPLATE, subscriptions=subscriptions)

# قوالب ومسارات إضافية

INVOICES_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>قائمة الفواتير - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 2rem;
        }
        .page-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .invoice-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .back-btn {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <h1><i class="fas fa-file-invoice me-3"></i>قائمة الفواتير</h1>
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right me-2"></i>العودة
        </a>
    </div>

    {% for invoice in invoices %}
    <div class="invoice-card">
        <div class="row">
            <div class="col-md-8">
                <h5>فاتورة رقم: {{ invoice.invoice_number }}</h5>
                <p><strong>الاشتراك:</strong> {{ invoice.subscription.name }}</p>
                <p><strong>المبلغ:</strong> {{ invoice.total_amount }} {{ invoice.currency }}</p>
                <p><strong>تاريخ الإصدار:</strong> {{ invoice.issue_date }}</p>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge {% if invoice.status == 'paid' %}bg-success{% elif invoice.status == 'pending' %}bg-warning{% else %}bg-danger{% endif %}">
                    {{ invoice.status }}
                </span>
            </div>
        </div>
    </div>
    {% endfor %}
</body>
</html>
'''

@app.route('/invoices')
@login_required
def invoices():
    if current_user.is_admin():
        invoices = Invoice.query.all()
    else:
        invoices = Invoice.query.filter_by(user_id=current_user.id).all()

    return render_template_string(INVOICES_TEMPLATE, invoices=invoices)

MESSAGE_CENTER_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>مركز الرسائل - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 2rem;
        }
        .page-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .message-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 10px;
        }
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <h1><i class="fas fa-envelope me-3"></i>مركز الرسائل</h1>
        <a href="{{ url_for('send_message') }}" class="btn btn-primary">
            <i class="fas fa-paper-plane me-2"></i>إرسال رسالة جديدة
        </a>
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-right me-2"></i>العودة
        </a>
    </div>

    {% for message in messages %}
    <div class="message-card">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h5>{{ message.subject }}</h5>
                <p><strong>إلى:</strong> {{ message.recipient_email }}</p>
                <p><strong>التاريخ:</strong> {{ message.sent_at.strftime('%Y-%m-%d %H:%M') }}</p>
                <p class="text-truncate" style="max-width: 500px;">{{ message.body[:100] }}...</p>
            </div>
            <div>
                <span class="badge {% if message.delivery_status == 'sent' %}bg-success{% else %}bg-warning{% endif %}">
                    {{ message.delivery_status }}
                </span>
            </div>
        </div>
    </div>
    {% endfor %}
</body>
</html>
'''

@app.route('/message_center')
@login_required
def message_center():
    if current_user.is_admin():
        messages = Message.query.order_by(Message.sent_at.desc()).all()
    else:
        messages = Message.query.filter_by(sender_id=current_user.id).order_by(Message.sent_at.desc()).all()

    return render_template_string(MESSAGE_CENTER_TEMPLATE, messages=messages)

SEND_MESSAGE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إرسال رسالة - النظام المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 2rem;
        }
        .form-container {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
        }
        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            color: white;
        }
        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            color: white;
        }
        .btn-primary {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
        }
        .variables-help {
            background: rgba(0, 212, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h2 class="text-center mb-4"><i class="fas fa-paper-plane me-2"></i>إرسال رسالة جديدة</h2>

        <form method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">نوع المستقبل</label>
                        <select class="form-select" name="recipient_type" required>
                            <option value="">اختر نوع المستقبل</option>
                            <option value="all_active">جميع العملاء النشطين</option>
                            <option value="custom">إيميل مخصص</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني (للإيميل المخصص)</label>
                        <input type="email" class="form-control" name="custom_email" placeholder="<EMAIL>">
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">موضوع الرسالة</label>
                <input type="text" class="form-control" name="subject" required>
            </div>

            <div class="mb-3">
                <label class="form-label">نص الرسالة</label>
                <textarea class="form-control" name="body" rows="8" required></textarea>
            </div>

            <div class="variables-help">
                <h6><i class="fas fa-info-circle me-2"></i>المتغيرات المتاحة:</h6>
                <div class="row">
                    <div class="col-md-6">
                        <small><code>{customer_name}</code> - اسم العميل</small><br>
                        <small><code>{current_date}</code> - التاريخ الحالي</small>
                    </div>
                    <div class="col-md-6">
                        <small><code>{subscription_name}</code> - اسم الاشتراك</small><br>
                        <small><code>{price}</code> - السعر</small>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-paper-plane me-2"></i>إرسال الرسالة
                </button>
                <a href="{{ url_for('message_center') }}" class="btn btn-secondary btn-lg ms-2">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</body>
</html>
'''

@app.route('/send_message', methods=['GET', 'POST'])
@login_required
def send_message():
    if request.method == 'POST':
        try:
            recipient_type = request.form.get('recipient_type')
            subject = request.form.get('subject')
            body = request.form.get('body')

            recipients = []

            if recipient_type == 'all_active':
                # جميع العملاء النشطين
                active_users = User.query.filter_by(is_active=True).all()
                for user in active_users:
                    recipients.append({
                        'email': user.email,
                        'name': user.full_name
                    })

            elif recipient_type == 'custom':
                # إيميل مخصص
                custom_email = request.form.get('custom_email')
                if custom_email:
                    recipients.append({
                        'email': custom_email,
                        'name': 'عميل'
                    })

            # إرسال الرسائل
            sent_count = 0
            for recipient in recipients:
                try:
                    # معالجة المتغيرات
                    processed_body = body.replace('{customer_name}', recipient['name'])
                    processed_body = processed_body.replace('{current_date}', date.today().strftime('%Y-%m-%d'))

                    # إنشاء سجل الرسالة
                    message = Message(
                        sender_id=current_user.id,
                        recipient_email=recipient['email'],
                        subject=subject,
                        body=processed_body
                    )

                    db.session.add(message)
                    sent_count += 1

                except Exception as e:
                    print(f"خطأ في إرسال رسالة إلى {recipient['email']}: {e}")

            db.session.commit()
            flash(f'✅ تم إرسال {sent_count} رسالة بنجاح', 'success')
            return redirect(url_for('message_center'))

        except Exception as e:
            print(f"خطأ في إرسال الرسائل: {e}")
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template_string(SEND_MESSAGE_TEMPLATE)

# تهيئة قاعدة البيانات

def init_database():
    """تهيئة قاعدة البيانات مع البيانات التجريبية"""
    with app.app_context():
        try:
            # إنشاء الجداول
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات")

            # التحقق من وجود البيانات
            if User.query.count() > 0:
                print("✅ البيانات موجودة مسبقاً")
                return

            # إنشاء المستخدمين
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام',
                role='admin',
                is_active=True
            )
            admin.set_password('123456')
            db.session.add(admin)

            user1 = User(
                username='user1',
                email='<EMAIL>',
                full_name='أحمد محمد علي',
                role='user',
                is_active=True
            )
            user1.set_password('123456')
            db.session.add(user1)

            user2 = User(
                username='user2',
                email='<EMAIL>',
                full_name='فاطمة أحمد حسن',
                role='user',
                is_active=True
            )
            user2.set_password('123456')
            db.session.add(user2)

            db.session.flush()
            print("✅ تم إنشاء المستخدمين")

            # إنشاء مزودي الخدمة
            aws = CloudProvider(
                name='Amazon Web Services',
                slug='aws',
                website='https://aws.amazon.com',
                description='خدمات الحوسبة السحابية من أمازون',
                is_active=True
            )
            db.session.add(aws)

            azure = CloudProvider(
                name='Microsoft Azure',
                slug='azure',
                website='https://azure.microsoft.com',
                description='منصة الحوسبة السحابية من مايكروسوفت',
                is_active=True
            )
            db.session.add(azure)

            gcp = CloudProvider(
                name='Google Cloud Platform',
                slug='gcp',
                website='https://cloud.google.com',
                description='خدمات الحوسبة السحابية من جوجل',
                is_active=True
            )
            db.session.add(gcp)

            db.session.flush()
            print("✅ تم إنشاء مزودي الخدمة")

            # إنشاء الاشتراكات التجريبية
            sub1 = Subscription(
                user_id=2,  # user1
                provider_id=1,  # AWS
                name='خادم ويب أساسي',
                description='خادم ويب للموقع الشخصي',
                price=25.99,
                currency='USD',
                start_date=date.today() - timedelta(days=15),
                end_date=date.today() + timedelta(days=15),
                status='active'
            )
            db.session.add(sub1)

            sub2 = Subscription(
                user_id=2,  # user1
                provider_id=2,  # Azure
                name='قاعدة بيانات متقدمة',
                description='قاعدة بيانات للتطبيق الرئيسي',
                price=89.99,
                currency='USD',
                start_date=date.today() - timedelta(days=30),
                end_date=date.today() + timedelta(days=60),
                status='active'
            )
            db.session.add(sub2)

            sub3 = Subscription(
                user_id=3,  # user2
                provider_id=3,  # GCP
                name='تخزين سحابي',
                description='مساحة تخزين للملفات',
                price=15.50,
                currency='USD',
                start_date=date.today() - timedelta(days=45),
                end_date=date.today() + timedelta(days=45),
                status='active'
            )
            db.session.add(sub3)

            sub4 = Subscription(
                user_id=3,  # user2
                provider_id=1,  # AWS
                name='خادم تطوير',
                description='خادم للتطوير والاختبار',
                price=12.00,
                currency='USD',
                start_date=date.today() - timedelta(days=60),
                end_date=date.today() - timedelta(days=5),
                status='expired'
            )
            db.session.add(sub4)

            db.session.flush()
            print("✅ تم إنشاء الاشتراكات التجريبية")

            # إنشاء الفواتير التجريبية
            inv1 = Invoice(
                invoice_number=generate_invoice_number(),
                user_id=2,
                subscription_id=1,
                total_amount=28.59,
                currency='USD',
                issue_date=date.today() - timedelta(days=30),
                due_date=date.today() - timedelta(days=15),
                status='paid'
            )
            db.session.add(inv1)

            inv2 = Invoice(
                invoice_number=generate_invoice_number(),
                user_id=2,
                subscription_id=2,
                total_amount=98.99,
                currency='USD',
                issue_date=date.today() - timedelta(days=15),
                due_date=date.today() + timedelta(days=15),
                status='pending'
            )
            db.session.add(inv2)

            inv3 = Invoice(
                invoice_number=generate_invoice_number(),
                user_id=3,
                subscription_id=3,
                total_amount=16.74,
                currency='USD',
                issue_date=date.today() - timedelta(days=10),
                due_date=date.today() + timedelta(days=20),
                status='pending'
            )
            db.session.add(inv3)

            db.session.flush()
            print("✅ تم إنشاء الفواتير التجريبية")

            # إنشاء قوالب الرسائل
            template1 = MessageTemplate(
                name='رسالة ترحيب',
                subject='مرحباً بك في خدماتنا، {customer_name}',
                body='''مرحباً {customer_name}،

نرحب بك في خدماتنا السحابية. تم تفعيل اشتراكك بنجاح.

تاريخ اليوم: {current_date}

نتمنى لك تجربة ممتازة مع خدماتنا.

مع أطيب التحيات،
فريق AdenLink - اليافعي''',
                template_type='welcome',
                is_active=True
            )
            db.session.add(template1)

            template2 = MessageTemplate(
                name='تذكير التجديد',
                subject='تذكير: اشتراكك ينتهي قريباً',
                body='''عزيزي {customer_name}،

نود تذكيرك بأن اشتراكك سينتهي قريباً.

يرجى تجديد اشتراكك لتجنب انقطاع الخدمة.

مع أطيب التحيات،
فريق AdenLink - اليافعي''',
                template_type='renewal',
                is_active=True
            )
            db.session.add(template2)

            db.session.flush()
            print("✅ تم إنشاء قوالب الرسائل")

            # إنشاء رسائل تجريبية
            msg1 = Message(
                sender_id=1,
                recipient_email='<EMAIL>',
                subject='مرحباً بك في خدماتنا',
                body='مرحباً أحمد، نرحب بك في خدماتنا السحابية...',
                delivery_status='sent'
            )
            db.session.add(msg1)

            msg2 = Message(
                sender_id=1,
                recipient_email='<EMAIL>',
                subject='تذكير: اشتراكك ينتهي قريباً',
                body='عزيزة فاطمة، نود تذكيرك بأن اشتراكك سينتهي قريباً...',
                delivery_status='sent'
            )
            db.session.add(msg2)

            print("✅ تم إنشاء الرسائل التجريبية")

            # حفظ جميع التغييرات
            db.session.commit()
            print("✅ تم حفظ جميع البيانات التجريبية بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            db.session.rollback()
            raise e

# تشغيل التطبيق
if __name__ == '__main__':
    print("🚀 بدء تشغيل النظام المتكامل والمتطور...")
    print("=" * 60)
    print("🎯 النظام المتكامل لإدارة الاشتراكات السحابية")
    print("💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️")
    print("🏢 شركة AdenLink - اليافعي")
    print("=" * 60)

    try:
        # تهيئة قاعدة البيانات
        print("📊 تهيئة قاعدة البيانات...")
        init_database()

        print("\n✅ تم تهيئة النظام بنجاح!")
        print("=" * 60)
        print("🌐 معلومات الوصول:")
        print("🔗 الرابط: http://localhost:4020")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: 123456")
        print("=" * 60)
        print("🎮 الميزات المتاحة:")
        print("📊 • مخططات تفاعلية متطورة")
        print("📋 • إدارة الاشتراكات الشاملة")
        print("🧾 • إدارة الفواتير المتقدمة")
        print("📧 • مركز التواصل مع العملاء")
        print("🎨 • واجهة متجاوبة ومتطورة")
        print("🔐 • نظام صلاحيات متقدم")
        print("=" * 60)
        print("🚀 النظام جاهز للاستخدام!")
        print("=" * 60)

        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=4020,
            debug=True,
            threaded=True
        )

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("🔧 يرجى التحقق من:")
        print("   • تثبيت جميع المكتبات المطلوبة")
        print("   • عدم استخدام المنفذ 5000 من تطبيق آخر")
        print("   • صلاحيات الكتابة في مجلد التطبيق")

print("✅ تم إعداد النظام المتكامل والمتطور بالكامل!")
print("🎊 النظام يحتوي على جميع الميزات المطلوبة:")
print("   📊 أقسام متفرعة متطورة")
print("   🎨 مخططات تفاعلية متقدمة")
print("   📧 نظام رسائل إلكترونية شامل")
print("   📈 تقارير وتحليلات متطورة")
print("   🔐 نظام صلاحيات متقدم")
print("   📱 تصميم متجاوب ومتطور")
print("   🗄️ قاعدة بيانات شاملة")
print("   ⚡ أداء محسن ومستقر")
print("🚀 النظام جاهز للتشغيل!")
