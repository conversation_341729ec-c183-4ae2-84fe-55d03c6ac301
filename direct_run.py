#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import os

def run_system():
    """تشغيل النظام مباشرة"""
    try:
        # تغيير المجلد
        os.chdir(r'C:\Users\<USER>\Desktop\ammaradenlink')
        print(f"📂 المجلد الحالي: {os.getcwd()}")
        
        # تشغيل النظام المبسط
        print("🚀 تشغيل النظام المبسط...")
        result = subprocess.run([sys.executable, 'minimal_system.py'], 
                              capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✅ تم تشغيل النظام بنجاح")
        else:
            print(f"❌ خطأ في تشغيل النظام: {result.returncode}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == '__main__':
    run_system()
