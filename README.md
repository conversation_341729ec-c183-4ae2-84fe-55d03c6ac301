# نظام إدارة الاشتراكات المتطور

## مطور بواسطة: المهندس محمد ياسر الجبوري ❤️

---

## 🚀 نظرة عامة

نظام شامل ومتطور لإدارة الاشتراكات والفواتير مع واجهة مستخدم عربية عصرية وميزات متقدمة. يوفر النظام حلولاً متكاملة لتتبع الاشتراكات، إدارة الفواتير، والتواصل مع العملاء.

## ✨ الميزات الرئيسية

### 🎯 إدارة الاشتراكات
- ✅ إضافة وتعديل وحذف الاشتراكات
- ✅ تتبع تفاصيل السيرفرات (IP, Port, API Keys)
- ✅ تصفية متقدمة حسب الفئة والحالة والمزود
- ✅ تتبع تواريخ الانتهاء والتجديد التلقائي
- ✅ نظام أولويات وعلامات للتنظيم

### 💰 نظام الفواتير
- ✅ إنشاء فواتير تلقائية مرتبطة بالاشتراكات
- ✅ تتبع حالات الدفع (مدفوع، معلق، متأخر)
- ✅ حساب الضرائب والخصومات
- ✅ تصدير الفواتير بصيغة PDF

### 📊 التحليلات والتقارير
- ✅ لوحة تحكم تفاعلية مع إحصائيات مباشرة
- ✅ رسوم بيانية بـ Chart.js
- ✅ تحليل الإنفاق الشهري والسنوي
- ✅ توزيع الاشتراكات حسب الفئات والمزودين

### 📧 مركز التواصل
- ✅ إرسال رسائل إلكترونية للعملاء
- ✅ قوالب جاهزة (تذكير تجديد، استحقاق دفع، ترحيب)
- ✅ متغيرات ديناميكية في الرسائل
- ✅ معاينة الرسائل قبل الإرسال

### 🔒 الأمان والحماية
- ✅ نظام مصادقة آمن مع Flask-Login
- ✅ تشفير كلمات المرور
- ✅ حماية من هجمات CSRF
- ✅ تسجيل أنشطة المستخدمين

### 🎨 التصميم العصري
- ✅ تأثيرات Glassmorphism و Neon Glow
- ✅ تأثيرات حركية (Hologram, Crystal, Matrix)
- ✅ تصميم متجاوب مع جميع الأجهزة
- ✅ دعم كامل للغة العربية

## 🛠️ التقنيات المستخدمة

### Backend
- **Python 3.13+**
- **Flask 2.3.3** - إطار العمل الرئيسي
- **SQLAlchemy 2.0.35** - قاعدة البيانات ORM
- **Flask-Login** - إدارة الجلسات والمصادقة
- **Flask-WTF** - نماذج آمنة
- **SQLite** - قاعدة البيانات

### Frontend
- **HTML5 & CSS3**
- **Bootstrap 5.3 RTL** - التصميم المتجاوب
- **JavaScript ES6+** - التفاعلات
- **Chart.js** - الرسوم البيانية
- **Font Awesome 6** - الأيقونات
- **Google Fonts (Cairo)** - الخط العربي

### المكتبات الإضافية
- **ReportLab** - تصدير PDF
- **OpenPyXL** - تصدير Excel
- **Flask-Mail** - إرسال الرسائل
- **Email-Validator** - التحقق من الإيميلات

## 📦 التثبيت والتشغيل

### 1. متطلبات النظام
```bash
Python 3.13+
pip (مدير الحزم)
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python run.py
```

### 4. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## 🔑 بيانات تسجيل الدخول

**المستخدم الافتراضي:**
- اسم المستخدم: `admin`
- كلمة المرور: `123456`

## 📁 هيكل المشروع

```
ammaradenlink/
├── app.py                          # التطبيق الرئيسي
├── run.py                          # ملف التشغيل
├── config.py                       # إعدادات التطبيق
├── requirements.txt                # المتطلبات
├── README.md                       # التوثيق
├── static/                         # الملفات الثابتة
│   ├── css/
│   │   └── style.css              # التصميم الرئيسي
│   ├── js/
│   │   └── main.js                # JavaScript الرئيسي
│   └── images/                    # الصور
├── templates/                      # قوالب HTML
│   ├── base.html                  # القالب الأساسي
│   ├── welcome.html               # صفحة الترحيب
│   ├── dashboard.html             # لوحة التحكم
│   ├── auth/
│   │   └── login.html             # تسجيل الدخول
│   ├── subscriptions/             # صفحات الاشتراكات
│   │   ├── list.html              # قائمة الاشتراكات
│   │   ├── add.html               # إضافة اشتراك
│   │   ├── edit.html              # تعديل اشتراك
│   │   └── view.html              # عرض تفاصيل
│   ├── analytics/                 # صفحات التحليلات
│   │   └── subscription_analytics.html
│   └── communication/             # صفحات التواصل
│       └── send_email.html
└── subscription_manager.db        # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🎯 الصفحات والوظائف

### الصفحات الرئيسية
- `/` - الصفحة الرئيسية (إعادة توجيه)
- `/welcome` - صفحة الترحيب
- `/login` - تسجيل الدخول
- `/dashboard` - لوحة التحكم

### إدارة الاشتراكات
- `/subscriptions` - قائمة الاشتراكات
- `/subscriptions/add` - إضافة اشتراك جديد
- `/subscriptions/<id>` - عرض تفاصيل الاشتراك
- `/subscriptions/<id>/edit` - تعديل الاشتراك
- `/subscriptions/<id>/delete` - حذف الاشتراك

### التحليلات والتقارير
- `/subscription_analytics` - تحليلات الاشتراكات

### التواصل
- `/send_email` - مركز إرسال الرسائل

## 🔧 التخصيص والتطوير

### إضافة ميزات جديدة
1. أضف الـ routes الجديدة في `app.py`
2. أنشئ القوالب المطلوبة في `templates/`
3. أضف التصميم في `static/css/style.css`
4. أضف JavaScript في `static/js/main.js`

### تخصيص التصميم
- عدّل المتغيرات في `:root` في `style.css`
- أضف تأثيرات جديدة في `main.js`
- استخدم الكلاسات الجاهزة: `glass-card`, `crystal-btn`, `hologram-card`

## 🚀 الميزات المستقبلية

- [ ] نظام إدارة المستخدمين المتقدم
- [ ] تصدير التقارير بصيغ متعددة
- [ ] تكامل مع بوابات الدفع
- [ ] تطبيق جوال
- [ ] API للتكامل مع أنظمة أخرى
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] إشعارات الهاتف المحمول

## 📞 الدعم والتواصل

**المطور:** المهندس محمد ياسر الجبوري  
**الإصدار:** 1.0.0  
**تاريخ الإصدار:** 2025-07-23  

---

## 📄 الترخيص

هذا المشروع مطور بحب وإتقان لخدمة المجتمع التقني العربي.

---

**صُنع بـ ❤️ في العراق**
