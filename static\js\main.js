/**
 * نظام إدارة الاشتراكات - JavaScript الرئيسي
 * مطور بواسطة: المهندس محمد ياسر الجبوري
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التأثيرات البصرية
    initVisualEffects();
    
    // تهيئة التحديث التلقائي
    initAutoRefresh();
    
    // تهيئة الإشعارات
    initNotifications();
    
    // تهيئة الرسوم البيانية
    initCharts();
    
    // تهيئة التفاعلات
    initInteractions();
});

/**
 * تهيئة التأثيرات البصرية
 */
function initVisualEffects() {
    // Matrix Background Effect
    createMatrixEffect();
    
    // Ripple Effect للأزرار
    addRippleEffect();
    
    // Hologram Effect للبطاقات
    addHologramEffect();
    
    // Crystal Effect للأزرار
    addCrystalEffect();
}

/**
 * إنشاء تأثير Matrix في الخلفية
 */
function createMatrixEffect() {
    const matrixBg = document.getElementById('matrix-bg');
    if (!matrixBg) return;
    
    // إنشاء عناصر Matrix
    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.className = 'matrix-particle';
        particle.style.cssText = `
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00f5ff;
            border-radius: 50%;
            opacity: ${Math.random() * 0.5 + 0.2};
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: matrixFloat ${Math.random() * 10 + 5}s linear infinite;
        `;
        matrixBg.appendChild(particle);
    }
    
    // إضافة CSS للحركة
    const style = document.createElement('style');
    style.textContent = `
        @keyframes matrixFloat {
            0% { transform: translateY(0) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
}

/**
 * إضافة تأثير Ripple للأزرار
 */
function addRippleEffect() {
    const buttons = document.querySelectorAll('.btn, .ripple');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: rippleEffect 0.6s linear;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // إضافة CSS للتأثير
    const style = document.createElement('style');
    style.textContent = `
        @keyframes rippleEffect {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

/**
 * إضافة تأثير Hologram للبطاقات
 */
function addHologramEffect() {
    const cards = document.querySelectorAll('.glass-card, .stats-card');
    
    cards.forEach(card => {
        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;
            
            this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
        });
    });
}

/**
 * إضافة تأثير Crystal للأزرار
 */
function addCrystalEffect() {
    const crystalBtns = document.querySelectorAll('.crystal-btn');
    
    crystalBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 0 20px rgba(0, 245, 255, 0.5)';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
        });
    });
}

/**
 * تهيئة التحديث التلقائي
 */
function initAutoRefresh() {
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(() => {
        if (window.location.pathname === '/dashboard') {
            refreshDashboardStats();
        }
    }, 30000);
}

/**
 * تحديث إحصائيات لوحة التحكم
 */
function refreshDashboardStats() {
    fetch('/api/dashboard/stats')
        .then(response => response.json())
        .then(data => {
            updateStatsCards(data);
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

/**
 * تحديث بطاقات الإحصائيات
 */
function updateStatsCards(data) {
    const statsElements = {
        'total-subscriptions': data.total_subscriptions,
        'active-subscriptions': data.active_subscriptions,
        'total-invoices': data.total_invoices,
        'pending-invoices': data.pending_invoices,
        'total-revenue': data.total_revenue,
        'upcoming-expiry': data.upcoming_expiry
    };
    
    Object.entries(statsElements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            animateNumber(element, value);
        }
    });
}

/**
 * تحريك الأرقام
 */
function animateNumber(element, targetValue) {
    const currentValue = parseInt(element.textContent) || 0;
    const increment = (targetValue - currentValue) / 20;
    let current = currentValue;
    
    const timer = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= targetValue) || (increment < 0 && current <= targetValue)) {
            current = targetValue;
            clearInterval(timer);
        }
        element.textContent = Math.round(current);
    }, 50);
}

/**
 * تهيئة الإشعارات
 */
function initNotifications() {
    // التحقق من الإشعارات الجديدة كل دقيقة
    setInterval(checkNotifications, 60000);
    
    // إضافة مستمعي الأحداث للإشعارات
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('notification-item')) {
            markNotificationAsRead(e.target.dataset.notificationId);
        }
    });
}

/**
 * التحقق من الإشعارات الجديدة
 */
function checkNotifications() {
    fetch('/api/notifications/unread')
        .then(response => response.json())
        .then(data => {
            updateNotificationBadge(data.count);
            if (data.notifications && data.notifications.length > 0) {
                showNewNotifications(data.notifications);
            }
        })
        .catch(error => {
            console.error('خطأ في جلب الإشعارات:', error);
        });
}

/**
 * تحديث شارة الإشعارات
 */
function updateNotificationBadge(count) {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'block' : 'none';
    }
}

/**
 * عرض الإشعارات الجديدة
 */
function showNewNotifications(notifications) {
    notifications.forEach(notification => {
        showToast(notification.title, notification.message, notification.type);
    });
}

/**
 * عرض رسالة Toast
 */
function showToast(title, message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <strong>${title}</strong><br>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // إزالة Toast بعد 5 ثوان
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

/**
 * إنشاء حاوية Toast
 */
function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

/**
 * تحديد الإشعار كمقروء
 */
function markNotificationAsRead(notificationId) {
    fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (notificationElement) {
                notificationElement.classList.add('read');
            }
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث الإشعار:', error);
    });
}

/**
 * الحصول على CSRF Token
 */
function getCSRFToken() {
    const token = document.querySelector('meta[name=csrf-token]');
    return token ? token.getAttribute('content') : '';
}

/**
 * تهيئة الرسوم البيانية
 */
function initCharts() {
    // سيتم تطوير هذه الوظيفة لاحقاً مع Chart.js
}

/**
 * تهيئة التفاعلات
 */
function initInteractions() {
    // تأكيد الحذف
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('delete-btn')) {
            e.preventDefault();
            const message = e.target.dataset.message || 'هل أنت متأكد من الحذف؟';
            if (confirm(message)) {
                window.location.href = e.target.href;
            }
        }
    });
    
    // تبديل كلمة المرور
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('toggle-password')) {
            const input = document.querySelector(e.target.dataset.target);
            const icon = e.target.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
    });
}

/**
 * وظائف مساعدة
 */

// تنسيق الأرقام
function formatNumber(num) {
    return new Intl.NumberFormat('ar-EG').format(num);
}

// تنسيق العملة
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

// تنسيق التاريخ
function formatDate(date) {
    return new Intl.DateTimeFormat('ar-EG').format(new Date(date));
}

// إظهار Loading
function showLoading(element) {
    element.innerHTML = '<div class="loading"></div>';
}

// إخفاء Loading
function hideLoading(element, originalContent) {
    element.innerHTML = originalContent;
}
