#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تشغيل النظام
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <html>
    <head>
        <title>نظام إدارة الاشتراكات</title>
        <meta charset="UTF-8">
    </head>
    <body style="font-family: Arial; text-align: center; padding: 50px;">
        <h1>🎉 نظام إدارة الاشتراكات يعمل بنجاح!</h1>
        <p>النظام جاهز للاستخدام</p>
        <a href="/full_system" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            تشغيل النظام الكامل
        </a>
    </body>
    </html>
    '''

@app.route('/full_system')
def full_system():
    try:
        # تحميل النظام الكامل
        exec(open('advanced_subscription_system.py').read())
        return "تم تحميل النظام الكامل بنجاح!"
    except Exception as e:
        return f"خطأ في تحميل النظام: {e}"

if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام الاختبار...")
    app.run(host='0.0.0.0', port=5090, debug=True)
