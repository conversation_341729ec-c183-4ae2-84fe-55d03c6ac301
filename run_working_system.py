#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

# تغيير المجلد الحالي
os.chdir(r'C:\Users\<USER>\Desktop\ammaradenlink')

print("🚀 تشغيل نظام إدارة الاشتراكات بالتصميم المحدث...")
print(f"📂 المجلد الحالي: {os.getcwd()}")

try:
    # تشغيل النظام
    exec(open('working_system.py', 'r', encoding='utf-8').read())
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
    input("اضغط Enter للخروج...")
