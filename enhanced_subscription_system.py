#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات المحسن والمتميز
مطور بواسطة: المهندس محمد ياسر الجبوري
شركة AdenLink - العراق
الإصدار المحسن مع ميزات متقدمة
"""

from flask import Flask, render_template, render_template_string, request, redirect, url_for, flash, jsonify, session, send_file, abort
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, date, timedelta
import secrets
import os
import json
import uuid
import smtplib
import io
import base64
import calendar
from collections import defaultdict
import random

# إعداد التطبيق المحسن
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///enhanced_subscriptions.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# إنشاء مجلد الرفع إذا لم يكن موجوداً
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول لهذه الصفحة'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# نماذج قاعدة البيانات المحسنة
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(200), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, user, manager
    company = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    city = db.Column(db.String(100))
    country = db.Column(db.String(100), default='العراق')
    avatar = db.Column(db.String(200))  # مسار الصورة الشخصية
    is_active = db.Column(db.Boolean, default=True)
    email_verified = db.Column(db.Boolean, default=False)
    last_login = db.Column(db.DateTime)
    login_count = db.Column(db.Integer, default=0)
    preferences = db.Column(db.Text)  # JSON للإعدادات الشخصية
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    subscriptions = db.relationship('Subscription', backref='user', lazy=True)
    invoices = db.relationship('Invoice', backref='user', lazy=True)
    sent_messages = db.relationship('Message', backref='sender', lazy=True)
    notifications = db.relationship('Notification', backref='user', lazy=True)
    reviews = db.relationship('Review', backref='user', lazy=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        return self.role in ['admin', 'manager']
    
    def get_preferences(self):
        if self.preferences:
            return json.loads(self.preferences)
        return {}
    
    def set_preferences(self, prefs):
        self.preferences = json.dumps(prefs)

class CloudProvider(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    website = db.Column(db.String(200))
    description = db.Column(db.Text)
    logo_url = db.Column(db.String(200))
    contact_email = db.Column(db.String(120))
    contact_phone = db.Column(db.String(20))
    support_url = db.Column(db.String(200))
    rating = db.Column(db.Float, default=0.0)  # تقييم المزود
    is_active = db.Column(db.Boolean, default=True)
    is_featured = db.Column(db.Boolean, default=False)  # مزود مميز
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    subscriptions = db.relationship('Subscription', backref='provider', lazy=True)
    reviews = db.relationship('Review', backref='provider', lazy=True)
    
    def get_average_rating(self):
        reviews = Review.query.filter_by(provider_id=self.id).all()
        if reviews:
            return sum(r.rating for r in reviews) / len(reviews)
        return 0.0

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    service_type = db.Column(db.String(50), nullable=False)
    subscription_type = db.Column(db.String(20), nullable=False)
    price = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='active')
    
    # معلومات الاتصال المحسنة
    server_ip = db.Column(db.String(45))
    port = db.Column(db.Integer)
    username = db.Column(db.String(100))
    password = db.Column(db.String(200))
    api_key = db.Column(db.String(500))
    region = db.Column(db.String(100))
    datacenter = db.Column(db.String(100))  # مركز البيانات
    
    # معلومات إضافية محسنة
    priority = db.Column(db.String(20), default='medium')
    notes = db.Column(db.Text)
    auto_renew = db.Column(db.Boolean, default=False)
    backup_enabled = db.Column(db.Boolean, default=False)  # النسخ الاحتياطي
    monitoring_enabled = db.Column(db.Boolean, default=False)  # المراقبة
    ssl_enabled = db.Column(db.Boolean, default=False)  # شهادة SSL
    
    # إحصائيات الاستخدام
    cpu_usage = db.Column(db.Float, default=0.0)  # استخدام المعالج
    memory_usage = db.Column(db.Float, default=0.0)  # استخدام الذاكرة
    disk_usage = db.Column(db.Float, default=0.0)  # استخدام القرص
    bandwidth_usage = db.Column(db.Float, default=0.0)  # استخدام النطاق
    
    # التواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_backup = db.Column(db.DateTime)  # آخر نسخة احتياطية
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='subscription', lazy=True)
    files = db.relationship('SubscriptionFile', backref='subscription', lazy=True)
    
    def days_until_expiry(self):
        if self.end_date:
            delta = self.end_date - date.today()
            return delta.days
        return 0
    
    def is_expiring_soon(self, days=7):
        return 0 <= self.days_until_expiry() <= days
    
    def is_expired(self):
        return self.end_date < date.today()
    
    def get_health_status(self):
        """حالة صحة الخادم بناءً على الاستخدام"""
        if self.cpu_usage > 90 or self.memory_usage > 90 or self.disk_usage > 90:
            return 'critical'
        elif self.cpu_usage > 70 or self.memory_usage > 70 or self.disk_usage > 70:
            return 'warning'
        else:
            return 'healthy'

class PaymentMethod(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    payment_type = db.Column(db.String(50), nullable=False)
    processing_fee = db.Column(db.Float, default=0.0)
    supported_currency = db.Column(db.String(3), default='USD')
    is_active = db.Column(db.Boolean, default=True)
    icon = db.Column(db.String(200))  # أيقونة طريقة الدفع
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='payment_method', lazy=True)

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
    payment_method_id = db.Column(db.Integer, db.ForeignKey('payment_method.id'), nullable=True)
    
    # المبالغ
    subtotal = db.Column(db.Float, nullable=False)
    tax_rate = db.Column(db.Float, default=0.0)
    tax_amount = db.Column(db.Float, default=0.0)
    discount_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')
    
    # التواريخ
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.Date)
    
    # الحالة
    status = db.Column(db.String(20), default='pending')
    payment_status = db.Column(db.String(20), default='pending')
    
    # معلومات إضافية
    notes = db.Column(db.Text)
    pdf_path = db.Column(db.String(200))  # مسار ملف PDF
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, **kwargs):
        super(Invoice, self).__init__(**kwargs)
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
    
    def generate_invoice_number(self):
        today = date.today()
        prefix = f"INV-{today.year}{today.month:02d}"
        last_invoice = Invoice.query.filter(
            Invoice.invoice_number.like(f"{prefix}%")
        ).order_by(Invoice.id.desc()).first()
        
        if last_invoice:
            try:
                last_number = int(last_invoice.invoice_number.split('-')[-1])
                new_number = last_number + 1
            except:
                new_number = 1
        else:
            new_number = 1
        
        return f"{prefix}-{new_number:04d}"
    
    def calculate_total(self):
        self.tax_amount = (self.subtotal * self.tax_rate) / 100
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
        return self.total_amount

class MessageTemplate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    subject = db.Column(db.String(200), nullable=False)
    body = db.Column(db.Text, nullable=False)
    template_type = db.Column(db.String(50), nullable=False)
    category = db.Column(db.String(50), default='general')  # تصنيف القالب
    language = db.Column(db.String(10), default='ar')  # لغة القالب
    is_active = db.Column(db.Boolean, default=True)
    is_default = db.Column(db.Boolean, default=False)  # قالب افتراضي
    usage_count = db.Column(db.Integer, default=0)  # عدد مرات الاستخدام
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    recipient_email = db.Column(db.String(120), nullable=False)
    recipient_name = db.Column(db.String(200))
    subject = db.Column(db.String(200), nullable=False)
    body = db.Column(db.Text, nullable=False)
    message_type = db.Column(db.String(50), default='custom')
    template_id = db.Column(db.Integer, db.ForeignKey('message_template.id'), nullable=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
    
    # حالة الإرسال
    status = db.Column(db.String(20), default='pending')
    sent_at = db.Column(db.DateTime)
    scheduled_at = db.Column(db.DateTime)
    error_message = db.Column(db.Text)
    
    # إعدادات الإرسال
    send_copy_to_sender = db.Column(db.Boolean, default=False)
    attach_pdf = db.Column(db.Boolean, default=False)
    priority = db.Column(db.String(20), default='normal')  # high, normal, low
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    template = db.relationship('MessageTemplate', backref='messages', lazy=True)
    related_subscription = db.relationship('Subscription', backref='messages', lazy=True)

# نماذج جديدة للميزات المتقدمة
class Notification(db.Model):
    """نظام الإشعارات المتقدم"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)  # info, warning, error, success
    category = db.Column(db.String(50), default='general')  # subscription, payment, system
    is_read = db.Column(db.Boolean, default=False)
    is_important = db.Column(db.Boolean, default=False)
    action_url = db.Column(db.String(200))  # رابط الإجراء
    action_text = db.Column(db.String(100))  # نص الإجراء
    expires_at = db.Column(db.DateTime)  # تاريخ انتهاء الإشعار
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Review(db.Model):
    """نظام التقييمات والمراجعات"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
    rating = db.Column(db.Integer, nullable=False)  # 1-5 نجوم
    title = db.Column(db.String(200))
    comment = db.Column(db.Text)
    pros = db.Column(db.Text)  # الإيجابيات
    cons = db.Column(db.Text)  # السلبيات
    is_verified = db.Column(db.Boolean, default=False)  # مراجعة موثقة
    is_featured = db.Column(db.Boolean, default=False)  # مراجعة مميزة
    helpful_count = db.Column(db.Integer, default=0)  # عدد الإعجابات
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class SubscriptionFile(db.Model):
    """نظام الملفات والمرفقات"""
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    filename = db.Column(db.String(200), nullable=False)
    original_filename = db.Column(db.String(200), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)  # حجم الملف بالبايت
    file_type = db.Column(db.String(50))  # نوع الملف
    category = db.Column(db.String(50), default='general')  # config, backup, certificate, etc.
    description = db.Column(db.Text)
    is_encrypted = db.Column(db.Boolean, default=False)  # ملف مشفر
    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    uploader = db.relationship('User', backref='uploaded_files', lazy=True)

class SystemSettings(db.Model):
    """إعدادات النظام"""
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    category = db.Column(db.String(50), default='general')
    is_public = db.Column(db.Boolean, default=False)  # إعداد عام أم خاص
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ActivityLog(db.Model):
    """سجل الأنشطة"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    action = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    resource_type = db.Column(db.String(50))  # subscription, invoice, user, etc.
    resource_id = db.Column(db.Integer)
    old_values = db.Column(db.Text)  # القيم القديمة (JSON)
    new_values = db.Column(db.Text)  # القيم الجديدة (JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    user = db.relationship('User', backref='activities', lazy=True)

# إنشاء الجداول
def create_tables():
    """إنشاء جداول قاعدة البيانات المحسنة"""
    with app.app_context():
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات المحسنة")

# وظائف مساعدة محسنة
def log_activity(user_id, action, description, resource_type=None, resource_id=None, old_values=None, new_values=None):
    """تسجيل نشاط في السجل"""
    try:
        activity = ActivityLog(
            user_id=user_id,
            action=action,
            description=description,
            ip_address=request.remote_addr if request else None,
            user_agent=request.user_agent.string if request else None,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=json.dumps(old_values) if old_values else None,
            new_values=json.dumps(new_values) if new_values else None
        )
        db.session.add(activity)
        db.session.commit()
    except Exception as e:
        print(f"خطأ في تسجيل النشاط: {e}")

def create_notification(user_id, title, message, notification_type='info', category='general', action_url=None, action_text=None, is_important=False):
    """إنشاء إشعار جديد"""
    try:
        notification = Notification(
            user_id=user_id,
            title=title,
            message=message,
            notification_type=notification_type,
            category=category,
            action_url=action_url,
            action_text=action_text,
            is_important=is_important
        )
        db.session.add(notification)
        db.session.commit()
        return notification
    except Exception as e:
        print(f"خطأ في إنشاء الإشعار: {e}")
        return None

def get_system_stats():
    """إحصائيات النظام المحسنة"""
    stats = {}

    # إحصائيات أساسية
    stats['total_users'] = User.query.count()
    stats['active_users'] = User.query.filter_by(is_active=True).count()
    stats['total_subscriptions'] = Subscription.query.count()
    stats['active_subscriptions'] = Subscription.query.filter_by(status='active').count()
    stats['total_providers'] = CloudProvider.query.count()
    stats['featured_providers'] = CloudProvider.query.filter_by(is_featured=True).count()

    # إحصائيات مالية
    total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(payment_status='paid').scalar() or 0
    pending_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(payment_status='pending').scalar() or 0
    stats['total_revenue'] = total_revenue
    stats['pending_revenue'] = pending_revenue

    # إحصائيات الاشتراكات
    expiring_soon = Subscription.query.filter(
        Subscription.end_date <= date.today() + timedelta(days=7),
        Subscription.end_date >= date.today(),
        Subscription.status == 'active'
    ).count()
    stats['expiring_soon'] = expiring_soon

    # إحصائيات الصحة
    critical_subscriptions = Subscription.query.filter(
        db.or_(
            Subscription.cpu_usage > 90,
            Subscription.memory_usage > 90,
            Subscription.disk_usage > 90
        )
    ).count()
    stats['critical_subscriptions'] = critical_subscriptions

    # إحصائيات الإشعارات
    unread_notifications = Notification.query.filter_by(is_read=False).count()
    stats['unread_notifications'] = unread_notifications

    # إحصائيات الملفات
    total_files = SubscriptionFile.query.count()
    total_file_size = db.session.query(db.func.sum(SubscriptionFile.file_size)).scalar() or 0
    stats['total_files'] = total_files
    stats['total_file_size'] = total_file_size / (1024 * 1024)  # MB

    return stats

def generate_ai_insights():
    """توليد رؤى ذكية بالذكاء الاصطناعي (محاكاة)"""
    insights = []

    # تحليل الاشتراكات
    active_subs = Subscription.query.filter_by(status='active').count()
    expired_subs = Subscription.query.filter_by(status='expired').count()

    if expired_subs > active_subs * 0.2:
        insights.append({
            'type': 'warning',
            'title': 'معدل انتهاء مرتفع',
            'message': f'لديك {expired_subs} اشتراك منتهي مقابل {active_subs} نشط. يُنصح بمراجعة استراتيجية التجديد.',
            'action': 'مراجعة الاشتراكات المنتهية',
            'priority': 'high'
        })

    # تحليل الإيرادات
    current_month_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter(
        Invoice.payment_status == 'paid',
        db.extract('month', Invoice.paid_date) == datetime.now().month,
        db.extract('year', Invoice.paid_date) == datetime.now().year
    ).scalar() or 0

    if current_month_revenue > 0:
        insights.append({
            'type': 'success',
            'title': 'أداء مالي جيد',
            'message': f'تم تحقيق إيرادات بقيمة ${current_month_revenue:.2f} هذا الشهر.',
            'action': 'عرض التقرير المالي',
            'priority': 'normal'
        })

    # تحليل صحة الخوادم
    critical_servers = Subscription.query.filter(
        db.or_(
            Subscription.cpu_usage > 85,
            Subscription.memory_usage > 85,
            Subscription.disk_usage > 85
        )
    ).count()

    if critical_servers > 0:
        insights.append({
            'type': 'error',
            'title': 'تحذير: خوادم تحتاج انتباه',
            'message': f'{critical_servers} خادم يعاني من استخدام مرتفع للموارد.',
            'action': 'فحص الخوادم',
            'priority': 'high'
        })

    # اقتراحات التحسين
    insights.append({
        'type': 'info',
        'title': 'اقتراح تحسين',
        'message': 'يمكنك تفعيل النسخ الاحتياطي التلقائي لحماية أفضل لبياناتك.',
        'action': 'تفعيل النسخ الاحتياطي',
        'priority': 'normal'
    })

    return insights

def get_calendar_events():
    """أحداث التقويم للاشتراكات"""
    events = []

    # الاشتراكات المنتهية قريباً
    expiring_subs = Subscription.query.filter(
        Subscription.end_date >= date.today(),
        Subscription.end_date <= date.today() + timedelta(days=30),
        Subscription.status == 'active'
    ).all()

    for sub in expiring_subs:
        events.append({
            'title': f'انتهاء: {sub.name}',
            'date': sub.end_date.isoformat(),
            'type': 'expiry',
            'color': '#ff6b6b' if sub.days_until_expiry() <= 7 else '#ffa500',
            'subscription_id': sub.id
        })

    # الفواتير المستحقة
    due_invoices = Invoice.query.filter(
        Invoice.due_date >= date.today(),
        Invoice.due_date <= date.today() + timedelta(days=30),
        Invoice.payment_status == 'pending'
    ).all()

    for invoice in due_invoices:
        events.append({
            'title': f'استحقاق فاتورة: {invoice.invoice_number}',
            'date': invoice.due_date.isoformat(),
            'type': 'payment',
            'color': '#3b82f6',
            'invoice_id': invoice.id
        })

    # النسخ الاحتياطية المجدولة
    backup_subs = Subscription.query.filter_by(backup_enabled=True, status='active').all()
    for sub in backup_subs:
        # محاكاة جدولة النسخ الاحتياطي (كل أسبوع)
        next_backup = date.today() + timedelta(days=7)
        events.append({
            'title': f'نسخة احتياطية: {sub.name}',
            'date': next_backup.isoformat(),
            'type': 'backup',
            'color': '#10b981',
            'subscription_id': sub.id
        })

    return events

# المسارات الأساسية المحسنة
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('enhanced_dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            login_user(user)

            # تحديث معلومات تسجيل الدخول
            user.last_login = datetime.utcnow()
            user.login_count += 1
            db.session.commit()

            # تسجيل النشاط
            log_activity(user.id, 'login', f'تسجيل دخول من {request.remote_addr}')

            # إنشاء إشعار ترحيب
            create_notification(
                user.id,
                'مرحباً بك!',
                f'تم تسجيل دخولك بنجاح في {datetime.now().strftime("%Y-%m-%d %H:%M")}',
                'success',
                'system'
            )

            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('enhanced_dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string(ENHANCED_LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    # تسجيل النشاط
    log_activity(current_user.id, 'logout', 'تسجيل خروج')

    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def enhanced_dashboard():
    # إحصائيات النظام
    stats = get_system_stats()

    # رؤى الذكاء الاصطناعي
    ai_insights = generate_ai_insights()

    # أحداث التقويم
    calendar_events = get_calendar_events()

    # الإشعارات الحديثة
    recent_notifications = Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).order_by(Notification.created_at.desc()).limit(5).all()

    # الأنشطة الحديثة
    recent_activities = ActivityLog.query.filter_by(
        user_id=current_user.id
    ).order_by(ActivityLog.created_at.desc()).limit(10).all()

    # الاشتراكات الحرجة
    critical_subscriptions = Subscription.query.filter(
        db.or_(
            Subscription.cpu_usage > 85,
            Subscription.memory_usage > 85,
            Subscription.disk_usage > 85
        )
    ).all()

    if not current_user.is_admin():
        critical_subscriptions = [s for s in critical_subscriptions if s.user_id == current_user.id]

    return render_template_string(
        ENHANCED_DASHBOARD_TEMPLATE,
        stats=stats,
        ai_insights=ai_insights,
        calendar_events=calendar_events,
        recent_notifications=recent_notifications,
        recent_activities=recent_activities,
        critical_subscriptions=critical_subscriptions
    )

# مسارات الميزات الجديدة المتقدمة

@app.route('/notifications')
@login_required
def notifications():
    """صفحة الإشعارات"""
    page = request.args.get('page', 1, type=int)
    notifications = Notification.query.filter_by(user_id=current_user.id).order_by(
        Notification.is_important.desc(),
        Notification.created_at.desc()
    ).paginate(page=page, per_page=20, error_out=False)

    return render_template_string(NOTIFICATIONS_TEMPLATE, notifications=notifications)

@app.route('/mark_notification_read/<int:notification_id>')
@login_required
def mark_notification_read(notification_id):
    """تحديد إشعار كمقروء"""
    notification = Notification.query.get_or_404(notification_id)
    if notification.user_id == current_user.id:
        notification.is_read = True
        db.session.commit()
    return redirect(url_for('notifications'))

@app.route('/calendar')
@login_required
def calendar_view():
    """تقويم الاشتراكات التفاعلي"""
    events = get_calendar_events()

    # فلترة الأحداث حسب صلاحية المستخدم
    if not current_user.is_admin():
        user_subscription_ids = [s.id for s in current_user.subscriptions]
        events = [e for e in events if e.get('subscription_id') in user_subscription_ids or 'subscription_id' not in e]

    return render_template_string(CALENDAR_TEMPLATE, events=events)

@app.route('/ai_insights')
@login_required
def ai_insights():
    """صفحة الرؤى الذكية"""
    insights = generate_ai_insights()

    # إحصائيات متقدمة للذكاء الاصطناعي
    advanced_stats = {
        'growth_rate': random.uniform(5, 25),  # محاكاة معدل النمو
        'efficiency_score': random.uniform(75, 95),  # درجة الكفاءة
        'cost_optimization': random.uniform(10, 30),  # توفير التكاليف المحتمل
        'uptime_percentage': random.uniform(98, 99.9),  # نسبة التشغيل
    }

    return render_template_string(AI_INSIGHTS_TEMPLATE, insights=insights, advanced_stats=advanced_stats)

@app.route('/reviews')
@login_required
def reviews():
    """صفحة المراجعات والتقييمات"""
    if current_user.is_admin():
        reviews = Review.query.order_by(Review.created_at.desc()).all()
    else:
        reviews = Review.query.filter_by(user_id=current_user.id).order_by(Review.created_at.desc()).all()

    providers = CloudProvider.query.filter_by(is_active=True).all()

    return render_template_string(REVIEWS_TEMPLATE, reviews=reviews, providers=providers)

@app.route('/add_review', methods=['POST'])
@login_required
def add_review():
    """إضافة مراجعة جديدة"""
    try:
        provider_id = request.form.get('provider_id')
        subscription_id = request.form.get('subscription_id')
        rating = int(request.form.get('rating'))
        title = request.form.get('title')
        comment = request.form.get('comment')
        pros = request.form.get('pros')
        cons = request.form.get('cons')

        review = Review(
            user_id=current_user.id,
            provider_id=provider_id,
            subscription_id=subscription_id if subscription_id else None,
            rating=rating,
            title=title,
            comment=comment,
            pros=pros,
            cons=cons
        )

        db.session.add(review)
        db.session.commit()

        # تسجيل النشاط
        log_activity(current_user.id, 'add_review', f'إضافة مراجعة لمزود الخدمة {provider_id}')

        flash('تم إضافة المراجعة بنجاح!', 'success')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')

    return redirect(url_for('reviews'))

@app.route('/files/<int:subscription_id>')
@login_required
def subscription_files(subscription_id):
    """ملفات الاشتراك"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # فحص الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        abort(403)

    files = SubscriptionFile.query.filter_by(subscription_id=subscription_id).order_by(
        SubscriptionFile.created_at.desc()
    ).all()

    return render_template_string(FILES_TEMPLATE, subscription=subscription, files=files)

@app.route('/upload_file/<int:subscription_id>', methods=['POST'])
@login_required
def upload_file(subscription_id):
    """رفع ملف للاشتراك"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # فحص الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        abort(403)

    if 'file' not in request.files:
        flash('لم يتم اختيار ملف', 'error')
        return redirect(url_for('subscription_files', subscription_id=subscription_id))

    file = request.files['file']
    if file.filename == '':
        flash('لم يتم اختيار ملف', 'error')
        return redirect(url_for('subscription_files', subscription_id=subscription_id))

    if file:
        try:
            # إنشاء اسم ملف آمن
            filename = secure_filename(file.filename)
            unique_filename = f"{uuid.uuid4()}_{filename}"

            # إنشاء مجلد للاشتراك
            subscription_folder = os.path.join(app.config['UPLOAD_FOLDER'], f'subscription_{subscription_id}')
            os.makedirs(subscription_folder, exist_ok=True)

            file_path = os.path.join(subscription_folder, unique_filename)
            file.save(file_path)

            # حفظ معلومات الملف في قاعدة البيانات
            file_record = SubscriptionFile(
                subscription_id=subscription_id,
                filename=unique_filename,
                original_filename=filename,
                file_path=file_path,
                file_size=os.path.getsize(file_path),
                file_type=filename.split('.')[-1].lower() if '.' in filename else 'unknown',
                category=request.form.get('category', 'general'),
                description=request.form.get('description', ''),
                uploaded_by=current_user.id
            )

            db.session.add(file_record)
            db.session.commit()

            # تسجيل النشاط
            log_activity(current_user.id, 'upload_file', f'رفع ملف {filename} للاشتراك {subscription.name}')

            flash('تم رفع الملف بنجاح!', 'success')
        except Exception as e:
            flash(f'حدث خطأ في رفع الملف: {str(e)}', 'error')

    return redirect(url_for('subscription_files', subscription_id=subscription_id))

@app.route('/download_file/<int:file_id>')
@login_required
def download_file(file_id):
    """تحميل ملف"""
    file_record = SubscriptionFile.query.get_or_404(file_id)
    subscription = file_record.subscription

    # فحص الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        abort(403)

    try:
        return send_file(file_record.file_path, as_attachment=True, download_name=file_record.original_filename)
    except Exception as e:
        flash(f'حدث خطأ في تحميل الملف: {str(e)}', 'error')
        return redirect(url_for('subscription_files', subscription_id=subscription.id))

@app.route('/system_health')
@login_required
def system_health():
    """صفحة صحة النظام"""
    if not current_user.is_admin():
        abort(403)

    # إحصائيات صحة النظام
    health_stats = {
        'total_subscriptions': Subscription.query.count(),
        'healthy_subscriptions': Subscription.query.filter(
            Subscription.cpu_usage <= 70,
            Subscription.memory_usage <= 70,
            Subscription.disk_usage <= 70
        ).count(),
        'warning_subscriptions': Subscription.query.filter(
            db.or_(
                db.and_(Subscription.cpu_usage > 70, Subscription.cpu_usage <= 90),
                db.and_(Subscription.memory_usage > 70, Subscription.memory_usage <= 90),
                db.and_(Subscription.disk_usage > 70, Subscription.disk_usage <= 90)
            )
        ).count(),
        'critical_subscriptions': Subscription.query.filter(
            db.or_(
                Subscription.cpu_usage > 90,
                Subscription.memory_usage > 90,
                Subscription.disk_usage > 90
            )
        ).count()
    }

    # الخوادم الحرجة
    critical_servers = Subscription.query.filter(
        db.or_(
            Subscription.cpu_usage > 85,
            Subscription.memory_usage > 85,
            Subscription.disk_usage > 85
        )
    ).all()

    return render_template_string(SYSTEM_HEALTH_TEMPLATE, health_stats=health_stats, critical_servers=critical_servers)

@app.route('/backup_management')
@login_required
def backup_management():
    """إدارة النسخ الاحتياطية"""
    if current_user.is_admin():
        subscriptions = Subscription.query.filter_by(status='active').all()
    else:
        subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').all()

    return render_template_string(BACKUP_MANAGEMENT_TEMPLATE, subscriptions=subscriptions)

@app.route('/toggle_backup/<int:subscription_id>')
@login_required
def toggle_backup(subscription_id):
    """تفعيل/تعطيل النسخ الاحتياطي"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # فحص الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        abort(403)

    subscription.backup_enabled = not subscription.backup_enabled
    if subscription.backup_enabled:
        subscription.last_backup = datetime.utcnow()

    db.session.commit()

    # تسجيل النشاط
    action = 'تفعيل' if subscription.backup_enabled else 'تعطيل'
    log_activity(current_user.id, 'toggle_backup', f'{action} النسخ الاحتياطي للاشتراك {subscription.name}')

    # إنشاء إشعار
    create_notification(
        current_user.id,
        f'{action} النسخ الاحتياطي',
        f'تم {action} النسخ الاحتياطي للاشتراك {subscription.name}',
        'success' if subscription.backup_enabled else 'info',
        'backup'
    )

    flash(f'تم {action} النسخ الاحتياطي بنجاح!', 'success')
    return redirect(url_for('backup_management'))

@app.route('/api/subscription_usage/<int:subscription_id>')
@login_required
def api_subscription_usage(subscription_id):
    """API لبيانات استخدام الاشتراك"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # فحص الصلاحية
    if not current_user.is_admin() and subscription.user_id != current_user.id:
        abort(403)

    # محاكاة بيانات الاستخدام (في التطبيق الحقيقي ستأتي من API المزود)
    usage_data = {
        'cpu_usage': subscription.cpu_usage,
        'memory_usage': subscription.memory_usage,
        'disk_usage': subscription.disk_usage,
        'bandwidth_usage': subscription.bandwidth_usage,
        'timestamp': datetime.utcnow().isoformat()
    }

    return jsonify(usage_data)

@app.route('/update_subscription_usage/<int:subscription_id>', methods=['POST'])
@login_required
def update_subscription_usage(subscription_id):
    """تحديث بيانات استخدام الاشتراك (محاكاة)"""
    if not current_user.is_admin():
        abort(403)

    subscription = Subscription.query.get_or_404(subscription_id)

    # محاكاة تحديث البيانات
    subscription.cpu_usage = random.uniform(10, 95)
    subscription.memory_usage = random.uniform(20, 90)
    subscription.disk_usage = random.uniform(15, 85)
    subscription.bandwidth_usage = random.uniform(5, 80)

    db.session.commit()

    # إنشاء إشعار إذا كان الاستخدام مرتفع
    if subscription.cpu_usage > 85 or subscription.memory_usage > 85 or subscription.disk_usage > 85:
        create_notification(
            subscription.user_id,
            'تحذير: استخدام مرتفع',
            f'الخادم {subscription.name} يعاني من استخدام مرتفع للموارد',
            'warning',
            'monitoring',
            url_for('subscription_files', subscription_id=subscription.id),
            'عرض التفاصيل',
            True
        )

    return jsonify({'status': 'success', 'message': 'تم تحديث البيانات'})

# تهيئة البيانات المحسنة
def initialize_enhanced_data():
    """تهيئة البيانات المحسنة للنظام"""
    with app.app_context():
        # إنشاء المستخدم المدير
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام المتقدم',
                role='admin',
                company='AdenLink',
                phone='+***********-0000',
                address='بغداد، العراق',
                city='بغداد',
                country='العراق',
                is_active=True,
                email_verified=True
            )
            admin.set_password('123456')
            db.session.add(admin)

        # إنشاء مستخدمين إضافيين
        users_data = [
            {
                'username': 'ahmed_manager',
                'email': '<EMAIL>',
                'full_name': 'أحمد علي المدير',
                'role': 'manager',
                'company': 'شركة التقنية المتقدمة',
                'phone': '+***********-1111',
                'city': 'البصرة'
            },
            {
                'username': 'sara_user',
                'email': '<EMAIL>',
                'full_name': 'سارة حسن أحمد',
                'role': 'user',
                'company': 'مؤسسة الابتكار',
                'phone': '+***********-2222',
                'city': 'أربيل'
            },
            {
                'username': 'omar_user',
                'email': '<EMAIL>',
                'full_name': 'عمر خليل إبراهيم',
                'role': 'user',
                'company': 'شركة المستقبل',
                'phone': '+***********-3333',
                'city': 'الموصل'
            }
        ]

        for user_data in users_data:
            existing = User.query.filter_by(username=user_data['username']).first()
            if not existing:
                user = User(**user_data)
                user.set_password('123456')
                user.is_active = True
                user.email_verified = True
                user.country = 'العراق'
                db.session.add(user)

        # إنشاء مزودي الخدمة المحسنين
        providers_data = [
            {
                'name': 'AdenLink Cloud',
                'slug': 'adenlink',
                'website': 'https://adenlink.com',
                'description': 'شركة AdenLink للحلول السحابية المتقدمة',
                'contact_email': '<EMAIL>',
                'contact_phone': '+***********-0000',
                'support_url': 'https://support.adenlink.com',
                'is_featured': True,
                'rating': 4.8
            },
            {
                'name': 'Amazon Web Services',
                'slug': 'aws',
                'website': 'https://aws.amazon.com',
                'description': 'خدمات أمازون السحابية الرائدة عالمياً',
                'contact_email': '<EMAIL>',
                'support_url': 'https://aws.amazon.com/support',
                'is_featured': True,
                'rating': 4.7
            },
            {
                'name': 'Microsoft Azure',
                'slug': 'azure',
                'website': 'https://azure.microsoft.com',
                'description': 'منصة مايكروسوفت السحابية المتكاملة',
                'contact_email': '<EMAIL>',
                'support_url': 'https://azure.microsoft.com/support',
                'is_featured': True,
                'rating': 4.6
            },
            {
                'name': 'Google Cloud Platform',
                'slug': 'google-cloud',
                'website': 'https://cloud.google.com',
                'description': 'منصة جوجل السحابية المبتكرة',
                'contact_email': '<EMAIL>',
                'support_url': 'https://cloud.google.com/support',
                'is_featured': False,
                'rating': 4.5
            },
            {
                'name': 'DigitalOcean',
                'slug': 'digitalocean',
                'website': 'https://digitalocean.com',
                'description': 'خدمات DigitalOcean السحابية البسيطة',
                'contact_email': '<EMAIL>',
                'support_url': 'https://digitalocean.com/support',
                'is_featured': False,
                'rating': 4.4
            }
        ]

        for provider_data in providers_data:
            existing = CloudProvider.query.filter_by(slug=provider_data['slug']).first()
            if not existing:
                provider = CloudProvider(**provider_data)
                db.session.add(provider)

        # إنشاء طرق الدفع المحسنة
        payment_methods_data = [
            {
                'name': 'بطاقة ائتمان',
                'payment_type': 'credit_card',
                'processing_fee': 2.9,
                'supported_currency': 'USD',
                'icon': 'fas fa-credit-card',
                'description': 'دفع آمن بالبطاقة الائتمانية'
            },
            {
                'name': 'PayPal',
                'payment_type': 'paypal',
                'processing_fee': 3.4,
                'supported_currency': 'USD',
                'icon': 'fab fa-paypal',
                'description': 'دفع سريع وآمن عبر PayPal'
            },
            {
                'name': 'تحويل بنكي',
                'payment_type': 'bank_transfer',
                'processing_fee': 0.0,
                'supported_currency': 'USD',
                'icon': 'fas fa-university',
                'description': 'تحويل مباشر من البنك'
            },
            {
                'name': 'العملات المشفرة',
                'payment_type': 'cryptocurrency',
                'processing_fee': 1.0,
                'supported_currency': 'USD',
                'icon': 'fab fa-bitcoin',
                'description': 'دفع بالعملات المشفرة'
            }
        ]

        for method_data in payment_methods_data:
            existing = PaymentMethod.query.filter_by(name=method_data['name']).first()
            if not existing:
                method = PaymentMethod(**method_data)
                db.session.add(method)

        db.session.commit()

        # إنشاء اشتراكات محسنة مع بيانات الاستخدام
        subscriptions_data = [
            {
                'name': 'خادم ويب رئيسي - AdenLink Pro',
                'description': 'خادم ويب مخصص عالي الأداء للموقع الرئيسي',
                'user_id': 1,  # admin
                'provider_id': 1,  # AdenLink
                'service_type': 'web_hosting',
                'subscription_type': 'annual',
                'price': 1500.00,
                'currency': 'USD',
                'start_date': date(2024, 1, 1),
                'end_date': date(2024, 12, 31),
                'status': 'active',
                'server_ip': '*************',
                'port': 443,
                'username': 'admin',
                'password': 'secure_password_123',
                'region': 'Middle East',
                'datacenter': 'Baghdad DC1',
                'priority': 'critical',
                'auto_renew': True,
                'backup_enabled': True,
                'monitoring_enabled': True,
                'ssl_enabled': True,
                'cpu_usage': 45.2,
                'memory_usage': 62.8,
                'disk_usage': 38.5,
                'bandwidth_usage': 25.3
            },
            {
                'name': 'خادم التطبيقات - AWS EC2',
                'description': 'خادم سحابي مرن لتطبيقات الويب',
                'user_id': 2,  # ahmed_manager
                'provider_id': 2,  # AWS
                'service_type': 'cloud_server',
                'subscription_type': 'monthly',
                'price': 200.00,
                'currency': 'USD',
                'start_date': date(2024, 6, 1),
                'end_date': date(2024, 8, 1),
                'status': 'active',
                'server_ip': '************',
                'port': 443,
                'username': 'ec2-user',
                'api_key': 'AKIA1234567890ABCDEF',
                'region': 'us-east-1',
                'datacenter': 'Virginia',
                'priority': 'high',
                'auto_renew': False,
                'backup_enabled': True,
                'monitoring_enabled': True,
                'ssl_enabled': True,
                'cpu_usage': 78.9,
                'memory_usage': 85.2,
                'disk_usage': 56.7,
                'bandwidth_usage': 42.1
            },
            {
                'name': 'قاعدة بيانات - Azure SQL',
                'description': 'قاعدة بيانات SQL مُدارة عالية الأداء',
                'user_id': 3,  # sara_user
                'provider_id': 3,  # Azure
                'service_type': 'database',
                'subscription_type': 'semi_annual',
                'price': 800.00,
                'currency': 'USD',
                'start_date': date(2024, 3, 1),
                'end_date': date(2024, 9, 1),
                'status': 'active',
                'server_ip': 'mydb.database.windows.net',
                'port': 1433,
                'username': 'dbadmin',
                'password': 'ComplexPassword123!',
                'region': 'East US',
                'datacenter': 'East US 2',
                'priority': 'high',
                'auto_renew': True,
                'backup_enabled': True,
                'monitoring_enabled': True,
                'ssl_enabled': True,
                'cpu_usage': 32.4,
                'memory_usage': 48.6,
                'disk_usage': 72.3,
                'bandwidth_usage': 18.9
            },
            {
                'name': 'خدمة التخزين - Google Cloud Storage',
                'description': 'تخزين سحابي آمن ومرن للملفات والنسخ الاحتياطية',
                'user_id': 4,  # omar_user
                'provider_id': 4,  # Google Cloud
                'service_type': 'storage',
                'subscription_type': 'monthly',
                'price': 120.00,
                'currency': 'USD',
                'start_date': date(2024, 5, 1),
                'end_date': date(2024, 8, 15),
                'status': 'active',
                'api_key': 'AIzaSyDXXXXXXXXXXXXXXXXXXXXXXXXX',
                'region': 'us-central1',
                'datacenter': 'Iowa',
                'priority': 'medium',
                'auto_renew': True,
                'backup_enabled': False,
                'monitoring_enabled': True,
                'ssl_enabled': True,
                'cpu_usage': 15.7,
                'memory_usage': 28.3,
                'disk_usage': 89.2,
                'bandwidth_usage': 67.4
            },
            {
                'name': 'خادم تطوير - DigitalOcean Droplet',
                'description': 'خادم تطوير للاختبارات والتجارب',
                'user_id': 2,  # ahmed_manager
                'provider_id': 5,  # DigitalOcean
                'service_type': 'development',
                'subscription_type': 'monthly',
                'price': 80.00,
                'currency': 'USD',
                'start_date': date(2024, 1, 1),
                'end_date': date(2024, 6, 15),
                'status': 'expired',
                'server_ip': '*************',
                'port': 22,
                'username': 'root',
                'password': 'dev_password_456',
                'region': 'nyc3',
                'datacenter': 'New York 3',
                'priority': 'low',
                'auto_renew': False,
                'backup_enabled': False,
                'monitoring_enabled': False,
                'ssl_enabled': False,
                'cpu_usage': 92.1,
                'memory_usage': 95.8,
                'disk_usage': 78.4,
                'bandwidth_usage': 12.6
            }
        ]

        for sub_data in subscriptions_data:
            existing = Subscription.query.filter_by(name=sub_data['name']).first()
            if not existing:
                subscription = Subscription(**sub_data)
                db.session.add(subscription)

        db.session.commit()
        print("✅ تم تهيئة البيانات المحسنة والمتقدمة")

# القوالب المحسنة والمتميزة
ENHANCED_LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* خلفية متحركة */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            border-radius: 25px;
            padding: 50px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 450px;
            width: 100%;
            position: relative;
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo-container {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(0, 245, 255, 0.3);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .logo-container i {
            font-size: 3rem;
            color: white;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .login-title {
            color: white;
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .login-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            font-weight: 400;
        }

        .form-floating {
            margin-bottom: 25px;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            height: 60px;
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: #00f5ff;
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.4);
            color: white;
            outline: none;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-floating label {
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border: none;
            border-radius: 15px;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0, 245, 255, 0.4);
        }

        .features-list {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
        }

        .features-title {
            color: #00f5ff;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
        }

        .feature-item i {
            color: #00f5ff;
            margin-left: 10px;
            width: 20px;
        }

        .system-info {
            text-align: center;
            margin-top: 25px;
            padding-top: 25px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .system-info p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .version-badge {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-top: 10px;
        }

        /* تأثيرات الجسيمات */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: particleFloat 15s linear infinite;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
                padding: 30px;
            }

            .login-title {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- تأثير الجسيمات -->
    <div class="particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 40%; animation-delay: 6s;"></div>
        <div class="particle" style="left: 50%; animation-delay: 8s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 10s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 12s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 14s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 16s;"></div>
    </div>

    <div class="login-container">
        <div class="login-header">
            <div class="logo-container">
                <i class="fas fa-rocket"></i>
            </div>
            <h1 class="login-title">نظام إدارة الاشتراكات</h1>
            <p class="login-subtitle">الإصدار المتقدم والمحسن</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show" style="background: rgba({{ '0, 255, 0' if category == 'success' else '255, 0, 0' }}, 0.2); border: 1px solid rgba({{ '0, 255, 0' if category == 'success' else '255, 0, 0' }}, 0.3); color: white;">
                        <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" style="filter: invert(1);"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST">
            <div class="form-floating">
                <input type="text" name="username" class="form-control" id="username" placeholder="اسم المستخدم" required>
                <label for="username"><i class="fas fa-user me-2"></i>اسم المستخدم</label>
            </div>

            <div class="form-floating">
                <input type="password" name="password" class="form-control" id="password" placeholder="كلمة المرور" required>
                <label for="password"><i class="fas fa-lock me-2"></i>كلمة المرور</label>
            </div>

            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt me-2"></i>
                تسجيل الدخول
            </button>
        </form>

        <div class="features-list">
            <div class="features-title">
                <i class="fas fa-star me-2"></i>
                الميزات الجديدة
            </div>
            <div class="feature-item">
                <i class="fas fa-brain"></i>
                رؤى ذكية بالذكاء الاصطناعي
            </div>
            <div class="feature-item">
                <i class="fas fa-bell"></i>
                نظام إشعارات متقدم
            </div>
            <div class="feature-item">
                <i class="fas fa-calendar-alt"></i>
                تقويم تفاعلي للاشتراكات
            </div>
            <div class="feature-item">
                <i class="fas fa-shield-alt"></i>
                نسخ احتياطي تلقائي
            </div>
            <div class="feature-item">
                <i class="fas fa-chart-line"></i>
                مراقبة الأداء المباشر
            </div>
        </div>

        <div class="system-info">
            <p><strong>النظام المتقدم والمحسن</strong></p>
            <p>مطور بواسطة: المهندس محمد ياسر الجبوري</p>
            <p>شركة AdenLink - العراق 🇮🇶</p>
            <div class="version-badge">
                الإصدار 2.0 - المتقدم
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تأثيرات تفاعلية
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير التركيز على الحقول
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.3s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // تأثير الكتابة على العنوان
        const title = document.querySelector('.login-title');
        const text = title.textContent;
        title.textContent = '';

        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                title.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        };

        setTimeout(typeWriter, 500);
    });
    </script>
</body>
</html>
'''

# لوحة التحكم المحسنة
ENHANCED_DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المتقدمة - نظام إدارة الاشتراكات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow-x: hidden;
        }

        /* الشريط العلوي المحسن */
        .top-navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 30px;
            position: sticky;
            top: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            color: #00f5ff;
            font-size: 1.5rem;
            font-weight: 700;
            text-decoration: none;
        }

        .navbar-brand i {
            margin-left: 10px;
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notification-bell {
            position: relative;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .notification-bell:hover {
            color: #00f5ff;
            transform: scale(1.1);
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .user-profile:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #00f5ff;
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* المحتوى الرئيسي */
        .main-content {
            padding: 30px;
        }

        .page-header {
            margin-bottom: 40px;
            text-align: center;
        }

        .page-title {
            color: #00f5ff;
            font-size: 3rem;
            font-weight: 700;
            text-shadow: 0 0 30px #00f5ff;
            margin-bottom: 10px;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px #00f5ff; }
            to { text-shadow: 0 0 40px #00f5ff, 0 0 60px #00f5ff; }
        }

        .page-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .quick-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        /* بطاقات الإحصائيات المحسنة */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #00f5ff, #bf00ff, #ff0080);
        }

        .stat-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 25px 50px rgba(0, 245, 255, 0.3);
        }

        .stat-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #00f5ff;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 10px;
            counter-reset: number;
            animation: countUp 2s ease-out;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            font-weight: 600;
        }

        .stat-change {
            margin-top: 10px;
            font-size: 0.9rem;
            padding: 5px 10px;
            border-radius: 15px;
            display: inline-block;
        }

        .stat-change.positive {
            background: rgba(0, 255, 0, 0.2);
            color: #4ade80;
        }

        .stat-change.negative {
            background: rgba(255, 0, 0, 0.2);
            color: #ff6b6b;
        }

        /* قسم الرؤى الذكية */
        .ai-insights-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-title {
            color: #bf00ff;
            font-size: 1.8rem;
            font-weight: 600;
            text-shadow: 0 0 15px #bf00ff;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .ai-insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }

        .insight-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .insight-card.success { border-left-color: #4ade80; }
        .insight-card.warning { border-left-color: #ffa500; }
        .insight-card.error { border-left-color: #ff6b6b; }
        .insight-card.info { border-left-color: #3b82f6; }

        .insight-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(-5px);
        }

        .insight-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .insight-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }

        .insight-title {
            font-weight: 600;
            color: white;
        }

        .insight-message {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .insight-action {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .insight-action:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #00f5ff;
        }

        /* التقويم المصغر */
        .mini-calendar-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 40px;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
            margin-top: 20px;
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .calendar-day.today {
            background: linear-gradient(135deg, #00f5ff, #bf00ff);
            color: white;
            font-weight: 600;
        }

        .calendar-day.has-event {
            background: rgba(255, 165, 0, 0.3);
            color: #ffa500;
        }

        .calendar-day:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.1);
        }

        /* الأنشطة الحديثة */
        .recent-activities {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 40px;
        }

        .activity-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .activity-content {
            flex: 1;
        }

        .activity-description {
            color: white;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .activity-time {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }

        /* الخوادم الحرجة */
        .critical-servers {
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid rgba(255, 107, 107, 0.3);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 40px;
        }

        .server-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #ff6b6b;
        }

        .server-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .server-name {
            font-weight: 600;
            color: white;
        }

        .server-status {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .usage-bars {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
        }

        .usage-bar {
            text-align: center;
        }

        .usage-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .usage-progress {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .usage-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ade80, #ffa500, #ff6b6b);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .usage-value {
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        /* تجاوب الموبايل */
        @media (max-width: 768px) {
            .main-content {
                padding: 15px;
            }

            .page-title {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .ai-insights-grid {
                grid-template-columns: 1fr;
            }

            .navbar-actions {
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="top-navbar">
        <a href="{{ url_for('enhanced_dashboard') }}" class="navbar-brand">
            <i class="fas fa-rocket"></i>
            نظام إدارة الاشتراكات المتقدم
        </a>

        <div class="navbar-actions">
            <div class="notification-bell" onclick="showNotifications()">
                <i class="fas fa-bell"></i>
                {% if recent_notifications %}
                <span class="notification-badge">{{ recent_notifications|length }}</span>
                {% endif %}
            </div>

            <a href="#" class="user-profile">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <span>{{ current_user.full_name }}</span>
            </a>

            <a href="{{ url_for('logout') }}" class="quick-action-btn" style="background: linear-gradient(135deg, #ff6b6b, #ff4757);">
                <i class="fas fa-sign-out-alt"></i>
                خروج
            </a>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-tachometer-alt me-3"></i>
                لوحة التحكم المتقدمة
            </h1>
            <p class="page-subtitle">مرحباً {{ current_user.full_name }}، إليك نظرة شاملة على نظامك</p>

            <div class="quick-actions">
                <a href="{{ url_for('notifications') }}" class="quick-action-btn">
                    <i class="fas fa-bell"></i>
                    الإشعارات
                </a>
                <a href="{{ url_for('calendar_view') }}" class="quick-action-btn">
                    <i class="fas fa-calendar-alt"></i>
                    التقويم
                </a>
                <a href="{{ url_for('ai_insights') }}" class="quick-action-btn">
                    <i class="fas fa-brain"></i>
                    الرؤى الذكية
                </a>
                <a href="{{ url_for('system_health') }}" class="quick-action-btn">
                    <i class="fas fa-heartbeat"></i>
                    صحة النظام
                </a>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-subscription"></i>
                </div>
                <div class="stat-number">{{ stats.total_subscriptions }}</div>
                <div class="stat-label">إجمالي الاشتراكات</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> +12%
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number">{{ stats.active_subscriptions }}</div>
                <div class="stat-label">اشتراكات نشطة</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> +8%
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-number">${{ "%.0f"|format(stats.total_revenue) }}</div>
                <div class="stat-label">إجمالي الإيرادات</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> +25%
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number">{{ stats.expiring_soon }}</div>
                <div class="stat-label">تنتهي قريباً</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i> -5%
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-server"></i>
                </div>
                <div class="stat-number">{{ stats.critical_subscriptions }}</div>
                <div class="stat-label">خوادم حرجة</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-up"></i> +2
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-file"></i>
                </div>
                <div class="stat-number">{{ stats.total_files }}</div>
                <div class="stat-label">الملفات المرفوعة</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> +{{ "%.1f"|format(stats.total_file_size) }}MB
                </div>
            </div>
        </div>

        <!-- الرؤى الذكية -->
        {% if ai_insights %}
        <div class="ai-insights-section">
            <h2 class="section-title">
                <i class="fas fa-brain"></i>
                الرؤى الذكية بالذكاء الاصطناعي
            </h2>

            <div class="ai-insights-grid">
                {% for insight in ai_insights %}
                <div class="insight-card {{ insight.type }}">
                    <div class="insight-header">
                        <div class="insight-icon" style="background:
                            {% if insight.type == 'success' %}rgba(74, 222, 128, 0.2){% endif %}
                            {% if insight.type == 'warning' %}rgba(255, 165, 0, 0.2){% endif %}
                            {% if insight.type == 'error' %}rgba(255, 107, 107, 0.2){% endif %}
                            {% if insight.type == 'info' %}rgba(59, 130, 246, 0.2){% endif %}
                        ;">
                            <i class="fas fa-
                                {% if insight.type == 'success' %}check{% endif %}
                                {% if insight.type == 'warning' %}exclamation-triangle{% endif %}
                                {% if insight.type == 'error' %}times{% endif %}
                                {% if insight.type == 'info' %}info{% endif %}
                            "></i>
                        </div>
                        <div class="insight-title">{{ insight.title }}</div>
                    </div>
                    <div class="insight-message">{{ insight.message }}</div>
                    <a href="#" class="insight-action">{{ insight.action }}</a>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- الخوادم الحرجة -->
        {% if critical_subscriptions %}
        <div class="critical-servers">
            <h2 class="section-title" style="color: #ff6b6b;">
                <i class="fas fa-exclamation-triangle"></i>
                خوادم تحتاج انتباه فوري
            </h2>

            {% for server in critical_subscriptions %}
            <div class="server-item">
                <div class="server-header">
                    <div class="server-name">{{ server.name }}</div>
                    <div class="server-status">حرج</div>
                </div>

                <div class="usage-bars">
                    <div class="usage-bar">
                        <div class="usage-label">المعالج</div>
                        <div class="usage-progress">
                            <div class="usage-fill" style="width: {{ server.cpu_usage }}%"></div>
                        </div>
                        <div class="usage-value">{{ "%.1f"|format(server.cpu_usage) }}%</div>
                    </div>

                    <div class="usage-bar">
                        <div class="usage-label">الذاكرة</div>
                        <div class="usage-progress">
                            <div class="usage-fill" style="width: {{ server.memory_usage }}%"></div>
                        </div>
                        <div class="usage-value">{{ "%.1f"|format(server.memory_usage) }}%</div>
                    </div>

                    <div class="usage-bar">
                        <div class="usage-label">القرص</div>
                        <div class="usage-progress">
                            <div class="usage-fill" style="width: {{ server.disk_usage }}%"></div>
                        </div>
                        <div class="usage-value">{{ "%.1f"|format(server.disk_usage) }}%</div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- الأنشطة الحديثة -->
        <div class="recent-activities">
            <h2 class="section-title">
                <i class="fas fa-history"></i>
                الأنشطة الحديثة
            </h2>

            <div class="activity-list">
                {% for activity in recent_activities %}
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-
                            {% if 'login' in activity.action %}sign-in-alt{% endif %}
                            {% if 'add' in activity.action %}plus{% endif %}
                            {% if 'edit' in activity.action %}edit{% endif %}
                            {% if 'delete' in activity.action %}trash{% endif %}
                            {% if 'backup' in activity.action %}shield-alt{% endif %}
                        "></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-description">{{ activity.description }}</div>
                        <div class="activity-time">{{ activity.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تأثيرات تفاعلية متقدمة
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير العد التصاعدي للأرقام
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach(number => {
            const target = parseInt(number.textContent.replace(/[^0-9]/g, ''));
            let current = 0;
            const increment = target / 50;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                if (number.textContent.includes('$')) {
                    number.textContent = '$' + Math.floor(current);
                } else {
                    number.textContent = Math.floor(current);
                }
            }, 40);
        });

        // تأثير الظهور التدريجي للبطاقات
        const cards = document.querySelectorAll('.stat-card, .insight-card, .server-item');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // تحديث بيانات الاستخدام كل 30 ثانية
        setInterval(updateUsageData, 30000);
    });

    function showNotifications() {
        window.location.href = '{{ url_for("notifications") }}';
    }

    function updateUsageData() {
        // محاكاة تحديث البيانات
        const usageFills = document.querySelectorAll('.usage-fill');
        usageFills.forEach(fill => {
            const newWidth = Math.random() * 100;
            fill.style.width = newWidth + '%';
        });
    }
    </script>
</body>
</html>
'''

# قوالب الميزات الجديدة
NOTIFICATIONS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>الإشعارات - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 20px;
        }
        .notification-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        .notification-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(-5px);
        }
        .notification-card.info { border-left-color: #3b82f6; }
        .notification-card.success { border-left-color: #4ade80; }
        .notification-card.warning { border-left-color: #ffa500; }
        .notification-card.error { border-left-color: #ff6b6b; }
        .notification-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .notification-title { font-weight: 600; color: white; }
        .notification-time { color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; }
        .notification-message { color: rgba(255, 255, 255, 0.8); line-height: 1.5; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">
        <i class="fas fa-bell me-2"></i>الإشعارات
    </h1>

    {% for notification in notifications.items %}
    <div class="notification-card {{ notification.notification_type }}">
        <div class="notification-header">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-time">{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
        </div>
        <div class="notification-message">{{ notification.message }}</div>
        {% if notification.action_url %}
        <a href="{{ notification.action_url }}" class="btn btn-sm btn-outline-light mt-2">{{ notification.action_text or 'عرض' }}</a>
        {% endif %}
    </div>
    {% endfor %}

    <div class="text-center mt-4">
        <a href="{{ url_for('enhanced_dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

CALENDAR_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>التقويم التفاعلي - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 20px;
        }
        .calendar-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 30px;
            backdrop-filter: blur(20px);
        }
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;
        }
        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .calendar-day:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.05);
        }
        .calendar-day.has-event {
            background: rgba(0, 245, 255, 0.3);
            border: 2px solid #00f5ff;
        }
        .event-list {
            margin-top: 30px;
        }
        .event-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid;
        }
        .event-item.expiry { border-left-color: #ff6b6b; }
        .event-item.payment { border-left-color: #3b82f6; }
        .event-item.backup { border-left-color: #4ade80; }
    </style>
</head>
<body>
    <div class="calendar-container">
        <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">
            <i class="fas fa-calendar-alt me-2"></i>التقويم التفاعلي
        </h1>

        <div class="calendar-header">
            <button class="btn btn-outline-light" onclick="previousMonth()">
                <i class="fas fa-chevron-right"></i>
            </button>
            <h3 id="currentMonth" style="color: #bf00ff;"></h3>
            <button class="btn btn-outline-light" onclick="nextMonth()">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>

        <div class="calendar-grid" id="calendarGrid">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>

        <div class="event-list">
            <h4 style="color: #bf00ff; margin-bottom: 20px;">الأحداث القادمة</h4>
            {% for event in events %}
            <div class="event-item {{ event.type }}">
                <strong>{{ event.title }}</strong><br>
                <small>{{ event.date }}</small>
            </div>
            {% endfor %}
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="{{ url_for('enhanced_dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>

    <script>
    const events = {{ events|tojson }};
    let currentDate = new Date();

    function renderCalendar() {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();

        document.getElementById('currentMonth').textContent =
            new Date(year, month).toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });

        const firstDay = new Date(year, month, 1).getDay();
        const daysInMonth = new Date(year, month + 1, 0).getDate();

        const grid = document.getElementById('calendarGrid');
        grid.innerHTML = '';

        // أيام الأسبوع
        const weekDays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        weekDays.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.textContent = day;
            dayHeader.style.fontWeight = '600';
            dayHeader.style.color = '#00f5ff';
            dayHeader.style.textAlign = 'center';
            dayHeader.style.padding = '10px';
            grid.appendChild(dayHeader);
        });

        // أيام فارغة في بداية الشهر
        for (let i = 0; i < firstDay; i++) {
            const emptyDay = document.createElement('div');
            emptyDay.className = 'calendar-day';
            grid.appendChild(emptyDay);
        }

        // أيام الشهر
        for (let day = 1; day <= daysInMonth; day++) {
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = day;

            const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            const hasEvent = events.some(event => event.date === dateStr);

            if (hasEvent) {
                dayElement.classList.add('has-event');
            }

            if (day === new Date().getDate() && month === new Date().getMonth() && year === new Date().getFullYear()) {
                dayElement.style.background = 'linear-gradient(135deg, #00f5ff, #bf00ff)';
                dayElement.style.color = 'white';
                dayElement.style.fontWeight = '600';
            }

            grid.appendChild(dayElement);
        }
    }

    function previousMonth() {
        currentDate.setMonth(currentDate.getMonth() - 1);
        renderCalendar();
    }

    function nextMonth() {
        currentDate.setMonth(currentDate.getMonth() + 1);
        renderCalendar();
    }

    renderCalendar();
    </script>
</body>
</html>
'''

AI_INSIGHTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>الرؤى الذكية - نظام إدارة الاشتراكات المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 20px;
        }
        .ai-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 30px;
            backdrop-filter: blur(20px);
            margin-bottom: 30px;
        }
        .ai-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .ai-title {
            color: #00f5ff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 0 20px #00f5ff;
            margin-bottom: 10px;
        }
        .ai-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #00f5ff;
            margin-bottom: 10px;
        }
        .stat-label {
            color: rgba(255, 255, 255, 0.8);
        }
        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        .insight-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid;
        }
        .insight-card.success { border-left-color: #4ade80; }
        .insight-card.warning { border-left-color: #ffa500; }
        .insight-card.error { border-left-color: #ff6b6b; }
        .insight-card.info { border-left-color: #3b82f6; }
    </style>
</head>
<body>
    <div class="ai-container">
        <div class="ai-header">
            <h1 class="ai-title">
                <i class="fas fa-brain me-3"></i>
                الرؤى الذكية بالذكاء الاصطناعي
            </h1>
            <p class="ai-subtitle">تحليلات متقدمة وتوصيات ذكية لتحسين أداء نظامك</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ "%.1f"|format(advanced_stats.growth_rate) }}%</div>
                <div class="stat-label">معدل النمو</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ "%.1f"|format(advanced_stats.efficiency_score) }}%</div>
                <div class="stat-label">درجة الكفاءة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ "%.1f"|format(advanced_stats.cost_optimization) }}%</div>
                <div class="stat-label">توفير التكاليف</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ "%.2f"|format(advanced_stats.uptime_percentage) }}%</div>
                <div class="stat-label">نسبة التشغيل</div>
            </div>
        </div>

        <div class="insights-grid">
            {% for insight in insights %}
            <div class="insight-card {{ insight.type }}">
                <h5 style="color: white; margin-bottom: 15px;">
                    <i class="fas fa-lightbulb me-2"></i>{{ insight.title }}
                </h5>
                <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 15px;">{{ insight.message }}</p>
                <button class="btn btn-sm btn-outline-light">{{ insight.action }}</button>
            </div>
            {% endfor %}
        </div>
    </div>

    <div class="text-center">
        <a href="{{ url_for('enhanced_dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

# قوالب أخرى مبسطة
REVIEWS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>المراجعات والتقييمات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .review-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px; }
        .rating { color: #ffa500; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">المراجعات والتقييمات</h1>

    <div class="row">
        <div class="col-md-8">
            {% for review in reviews %}
            <div class="review-card">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5>{{ review.title or 'مراجعة' }}</h5>
                    <div class="rating">
                        {% for i in range(review.rating) %}★{% endfor %}
                        {% for i in range(5 - review.rating) %}☆{% endfor %}
                    </div>
                </div>
                <p>{{ review.comment }}</p>
                <small class="text-muted">{{ review.user.full_name }} - {{ review.created_at.strftime('%Y-%m-%d') }}</small>
            </div>
            {% endfor %}
        </div>

        <div class="col-md-4">
            <div class="review-card">
                <h5>إضافة مراجعة</h5>
                <form method="POST" action="{{ url_for('add_review') }}">
                    <div class="mb-3">
                        <select name="provider_id" class="form-select" required>
                            <option value="">اختر المزود</option>
                            {% for provider in providers %}
                            <option value="{{ provider.id }}">{{ provider.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <select name="rating" class="form-select" required>
                            <option value="">التقييم</option>
                            <option value="5">5 نجوم</option>
                            <option value="4">4 نجوم</option>
                            <option value="3">3 نجوم</option>
                            <option value="2">نجمتان</option>
                            <option value="1">نجمة واحدة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <input type="text" name="title" class="form-control" placeholder="عنوان المراجعة">
                    </div>
                    <div class="mb-3">
                        <textarea name="comment" class="form-control" rows="3" placeholder="تعليقك"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة مراجعة</button>
                </form>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="{{ url_for('enhanced_dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

FILES_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>ملفات الاشتراك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .file-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 15px; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">
        ملفات الاشتراك: {{ subscription.name }}
    </h1>

    <div class="row">
        <div class="col-md-8">
            {% for file in files %}
            <div class="file-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6>{{ file.original_filename }}</h6>
                        <small>{{ file.description or 'لا يوجد وصف' }}</small><br>
                        <small class="text-muted">{{ "%.2f"|format(file.file_size / 1024) }} KB - {{ file.created_at.strftime('%Y-%m-%d') }}</small>
                    </div>
                    <a href="{{ url_for('download_file', file_id=file.id) }}" class="btn btn-sm btn-outline-light">
                        <i class="fas fa-download"></i> تحميل
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="col-md-4">
            <div class="file-card">
                <h5>رفع ملف جديد</h5>
                <form method="POST" action="{{ url_for('upload_file', subscription_id=subscription.id) }}" enctype="multipart/form-data">
                    <div class="mb-3">
                        <input type="file" name="file" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <select name="category" class="form-select">
                            <option value="general">عام</option>
                            <option value="config">إعدادات</option>
                            <option value="backup">نسخة احتياطية</option>
                            <option value="certificate">شهادة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <textarea name="description" class="form-control" rows="2" placeholder="وصف الملف"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">رفع الملف</button>
                </form>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="{{ url_for('enhanced_dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

SYSTEM_HEALTH_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>صحة النظام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .health-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 20px; text-align: center; }
        .health-number { font-size: 2rem; font-weight: 700; margin-bottom: 10px; }
        .healthy { color: #4ade80; }
        .warning { color: #ffa500; }
        .critical { color: #ff6b6b; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">صحة النظام</h1>

    <div class="row">
        <div class="col-md-3">
            <div class="health-card">
                <div class="health-number healthy">{{ health_stats.healthy_subscriptions }}</div>
                <div>خوادم صحية</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="health-card">
                <div class="health-number warning">{{ health_stats.warning_subscriptions }}</div>
                <div>تحذيرات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="health-card">
                <div class="health-number critical">{{ health_stats.critical_subscriptions }}</div>
                <div>حالات حرجة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="health-card">
                <div class="health-number">{{ health_stats.total_subscriptions }}</div>
                <div>إجمالي الخوادم</div>
            </div>
        </div>
    </div>

    {% if critical_servers %}
    <h3 style="color: #ff6b6b; margin: 30px 0;">خوادم تحتاج انتباه فوري</h3>
    {% for server in critical_servers %}
    <div class="health-card" style="text-align: right;">
        <h5>{{ server.name }}</h5>
        <div class="row">
            <div class="col-md-4">المعالج: {{ "%.1f"|format(server.cpu_usage) }}%</div>
            <div class="col-md-4">الذاكرة: {{ "%.1f"|format(server.memory_usage) }}%</div>
            <div class="col-md-4">القرص: {{ "%.1f"|format(server.disk_usage) }}%</div>
        </div>
    </div>
    {% endfor %}
    {% endif %}

    <div class="text-center mt-4">
        <a href="{{ url_for('enhanced_dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

BACKUP_MANAGEMENT_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة النسخ الاحتياطية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; padding: 20px; }
        .backup-card { background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; margin-bottom: 15px; }
        .backup-enabled { border-left: 4px solid #4ade80; }
        .backup-disabled { border-left: 4px solid #ff6b6b; }
    </style>
</head>
<body>
    <h1 style="color: #00f5ff; text-align: center; margin-bottom: 30px;">إدارة النسخ الاحتياطية</h1>

    {% for subscription in subscriptions %}
    <div class="backup-card {{ 'backup-enabled' if subscription.backup_enabled else 'backup-disabled' }}">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5>{{ subscription.name }}</h5>
                <small>{{ subscription.provider.name }}</small><br>
                {% if subscription.backup_enabled and subscription.last_backup %}
                <small class="text-success">آخر نسخة احتياطية: {{ subscription.last_backup.strftime('%Y-%m-%d %H:%M') }}</small>
                {% elif subscription.backup_enabled %}
                <small class="text-warning">لم يتم إنشاء نسخة احتياطية بعد</small>
                {% else %}
                <small class="text-danger">النسخ الاحتياطي معطل</small>
                {% endif %}
            </div>
            <a href="{{ url_for('toggle_backup', subscription_id=subscription.id) }}"
               class="btn btn-sm {{ 'btn-outline-danger' if subscription.backup_enabled else 'btn-outline-success' }}">
                {{ 'تعطيل' if subscription.backup_enabled else 'تفعيل' }}
            </a>
        </div>
    </div>
    {% endfor %}

    <div class="text-center mt-4">
        <a href="{{ url_for('enhanced_dashboard') }}" class="btn btn-primary">العودة للوحة التحكم</a>
    </div>
</body>
</html>
'''

if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام إدارة الاشتراكات المحسن والمتميز...")
    print("=" * 70)
    print("🎯 الميزات الجديدة المضافة:")
    print("   🧠 رؤى ذكية بالذكاء الاصطناعي")
    print("   🔔 نظام إشعارات متقدم")
    print("   📅 تقويم تفاعلي للاشتراكات")
    print("   🛡️ نظام النسخ الاحتياطي التلقائي")
    print("   ⭐ نظام التقييمات والمراجعات")
    print("   📁 نظام الملفات والمرفقات")
    print("   💊 مراقبة صحة النظام")
    print("   📊 تحليلات متقدمة ومحسنة")
    print("=" * 70)

    # إنشاء الجداول وتهيئة البيانات
    create_tables()
    initialize_enhanced_data()

    print("\n🌐 معلومات الوصول:")
    print("   🔗 الرابط: http://localhost:5090")
    print("   👤 اسم المستخدم: admin")
    print("   🔑 كلمة المرور: 123456")
    print("\n🎨 الميزات المتاحة:")
    print("   📊 لوحة تحكم ذكية مع AI")
    print("   🔔 إشعارات فورية ومتقدمة")
    print("   📅 تقويم تفاعلي للأحداث")
    print("   ⭐ تقييمات ومراجعات المزودين")
    print("   📁 إدارة الملفات والمرفقات")
    print("   🛡️ نسخ احتياطي تلقائي")
    print("   💊 مراقبة صحة الخوادم")
    print("   🎯 رؤى ذكية وتوصيات")
    print("=" * 70)

    # تشغيل التطبيق المحسن
    app.run(debug=True, host='0.0.0.0', port=5090)
