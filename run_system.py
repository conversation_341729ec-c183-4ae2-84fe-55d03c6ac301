#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة الاشتراكات
"""

import sys
import os
import subprocess

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# تشغيل النظام
if __name__ == '__main__':
    try:
        print("🚀 بدء تشغيل نظام إدارة الاشتراكات...")
        print("📂 المجلد الحالي:", os.getcwd())

        # تشغيل النظام مباشرة
        subprocess.run([sys.executable, 'advanced_subscription_system.py'], check=True)

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")
