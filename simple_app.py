#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق مبسط لاختبار النظام
"""

from flask import Flask, render_template, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager, UserMixin, login_user, logout_user, login_required, current_user
from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, PasswordField, BooleanField
from wtforms.validators import DataRequired
from werkzeug.security import generate_password_hash, check_password_hash

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///simple_subscription.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# نموذج المستخدم
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# نموذج تسجيل الدخول
class LoginForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired()])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])
    remember_me = BooleanField('تذكرني')

# الصفحة الرئيسية
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('welcome'))

@app.route('/welcome')
def welcome():
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>مرحباً - نظام إدارة الاشتراكات</title>
        <style>
            body { 
                background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e); 
                color: white; 
                font-family: Arial, sans-serif; 
                text-align: center; 
                padding: 50px; 
                min-height: 100vh;
                margin: 0;
            }
            .container { 
                max-width: 600px; 
                margin: 0 auto; 
                background: rgba(255,255,255,0.1); 
                padding: 40px; 
                border-radius: 20px; 
                backdrop-filter: blur(20px);
            }
            h1 { 
                color: #00f5ff; 
                font-size: 3rem; 
                margin-bottom: 20px; 
                text-shadow: 0 0 20px #00f5ff;
            }
            .btn { 
                background: linear-gradient(135deg, #667eea, #764ba2); 
                color: white; 
                padding: 15px 30px; 
                border: none; 
                border-radius: 10px; 
                text-decoration: none; 
                font-size: 1.1rem; 
                margin: 10px;
                display: inline-block;
            }
            .btn:hover { 
                transform: translateY(-2px); 
                color: white; 
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 نظام إدارة الاشتراكات</h1>
            <p style="font-size: 1.2rem; margin-bottom: 30px;">مرحباً بك في النظام المتطور لإدارة الاشتراكات</p>
            <a href="/login" class="btn">تسجيل الدخول</a>
            <div style="margin-top: 30px; padding: 20px; background: rgba(0,245,255,0.1); border-radius: 10px;">
                <strong>بيانات تجريبية:</strong><br>
                المستخدم: admin<br>
                كلمة المرور: 123456
            </div>
            <div style="margin-top: 30px; color: #bf00ff;">
                مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
            </div>
        </div>
    </body>
    </html>
    '''

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            login_user(user, remember=form.remember_me.data)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة.', 'error')
    
    return render_template_string('''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>تسجيل الدخول</title>
        <style>
            body { 
                background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e); 
                color: white; 
                font-family: Arial, sans-serif; 
                display: flex; 
                align-items: center; 
                justify-content: center; 
                min-height: 100vh; 
                margin: 0;
            }
            .login-container { 
                background: rgba(255,255,255,0.1); 
                padding: 40px; 
                border-radius: 20px; 
                backdrop-filter: blur(20px); 
                max-width: 400px; 
                width: 100%;
            }
            h1 { 
                color: #00f5ff; 
                text-align: center; 
                margin-bottom: 30px; 
                text-shadow: 0 0 20px #00f5ff;
            }
            .form-control { 
                background: rgba(255,255,255,0.1); 
                border: 2px solid rgba(255,255,255,0.2); 
                border-radius: 10px; 
                color: white; 
                padding: 12px 15px; 
                width: 100%; 
                margin-bottom: 15px; 
                box-sizing: border-box;
            }
            .form-control:focus { 
                border-color: #00f5ff; 
                outline: none; 
                box-shadow: 0 0 15px rgba(0,245,255,0.3);
            }
            .btn-login { 
                background: linear-gradient(135deg, #667eea, #764ba2); 
                border: none; 
                border-radius: 10px; 
                color: white; 
                padding: 12px; 
                width: 100%; 
                font-weight: 600; 
                margin-top: 20px;
            }
            .btn-login:hover { 
                transform: translateY(-2px);
            }
            .alert { 
                background: rgba(255,0,0,0.1); 
                border: 1px solid rgba(255,0,0,0.3); 
                color: #ff6b6b; 
                padding: 10px; 
                border-radius: 10px; 
                margin-bottom: 15px;
            }
            .demo-info { 
                background: rgba(0,245,255,0.1); 
                border: 1px solid rgba(0,245,255,0.3); 
                border-radius: 10px; 
                padding: 15px; 
                margin-top: 20px; 
                text-align: center; 
                font-size: 0.9rem;
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <h1>🔐 تسجيل الدخول</h1>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST">
                {{ form.hidden_tag() }}
                
                <input type="text" name="username" class="form-control" placeholder="اسم المستخدم" required>
                <input type="password" name="password" class="form-control" placeholder="كلمة المرور" required>
                
                <label style="display: flex; align-items: center; margin: 15px 0;">
                    <input type="checkbox" name="remember_me" style="margin-left: 10px;">
                    تذكرني
                </label>
                
                <button type="submit" class="btn-login">تسجيل الدخول</button>
            </form>
            
            <div class="demo-info">
                <strong>بيانات تجريبية:</strong><br>
                المستخدم: admin<br>
                كلمة المرور: 123456
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <a href="/" style="color: #00f5ff; text-decoration: none;">العودة للصفحة الرئيسية</a>
            </div>
        </div>
    </body>
    </html>
    ''', form=form)

# لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    return f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>لوحة التحكم</title>
        <style>
            body {{ 
                background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e); 
                color: white; 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 20px;
            }}
            .navbar {{ 
                background: rgba(255,255,255,0.1); 
                padding: 15px 20px; 
                border-radius: 15px; 
                margin-bottom: 30px; 
                display: flex; 
                justify-content: space-between; 
                align-items: center;
            }}
            .navbar-brand {{ 
                color: #00f5ff; 
                font-size: 1.5rem; 
                font-weight: 700; 
                text-shadow: 0 0 20px #00f5ff;
            }}
            .nav-link {{ 
                color: white; 
                text-decoration: none; 
                margin: 0 10px;
            }}
            .nav-link:hover {{ 
                color: #00f5ff; 
                text-decoration: none;
            }}
            .welcome-card {{ 
                background: rgba(255,255,255,0.1); 
                padding: 40px; 
                border-radius: 20px; 
                text-align: center; 
                margin-bottom: 30px;
            }}
            .welcome-title {{ 
                color: #00f5ff; 
                font-size: 2.5rem; 
                margin-bottom: 20px; 
                text-shadow: 0 0 20px #00f5ff;
            }}
            .stats-grid {{ 
                display: grid; 
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
                gap: 20px; 
                margin-bottom: 30px;
            }}
            .stat-card {{ 
                background: rgba(255,255,255,0.1); 
                padding: 25px; 
                border-radius: 15px; 
                text-align: center;
            }}
            .stat-number {{ 
                font-size: 2rem; 
                font-weight: 700; 
                color: #00f5ff; 
                margin-bottom: 10px;
            }}
            .action-buttons {{ 
                display: grid; 
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
                gap: 15px; 
                margin-top: 30px;
            }}
            .action-btn {{ 
                background: linear-gradient(135deg, #667eea, #764ba2); 
                color: white; 
                padding: 15px 20px; 
                border-radius: 15px; 
                text-decoration: none; 
                text-align: center; 
                font-weight: 600;
            }}
            .action-btn:hover {{ 
                transform: translateY(-3px); 
                color: white; 
                text-decoration: none;
            }}
            .developer-info {{ 
                background: rgba(255,255,255,0.05); 
                padding: 20px; 
                border-radius: 15px; 
                text-align: center; 
                margin-top: 30px;
            }}
        </style>
    </head>
    <body>
        <nav class="navbar">
            <div class="navbar-brand">🚀 نظام إدارة الاشتراكات</div>
            <div>
                <span class="nav-link">مرحباً {current_user.full_name}</span>
                <a href="/logout" class="nav-link">تسجيل الخروج</a>
            </div>
        </nav>
        
        <div class="welcome-card">
            <h1 class="welcome-title">📊 لوحة التحكم</h1>
            <p style="font-size: 1.2rem;">مرحباً بك {current_user.full_name}، إليك نظرة عامة على نشاطك</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div>إجمالي الاشتراكات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div>الاشتراكات النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div>إجمالي الفواتير</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">$0.00</div>
                <div>إجمالي الإيرادات</div>
            </div>
        </div>
        
        <div class="action-buttons">
            <a href="#" class="action-btn">📋 إدارة الاشتراكات</a>
            <a href="#" class="action-btn">➕ إضافة اشتراك جديد</a>
            <a href="#" class="action-btn">📊 التحليلات والتقارير</a>
            <a href="#" class="action-btn">📧 مركز التواصل</a>
        </div>
        
        <div class="developer-info">
            <p>مطور بواسطة: <span style="color: #bf00ff; font-weight: 600;">المهندس محمد ياسر الجبوري</span> ❤️</p>
            <p>صُنع بحب وإتقان لخدمة المجتمع التقني العربي</p>
        </div>
    </body>
    </html>
    '''

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('welcome'))

# إنشاء قاعدة البيانات والمستخدم الافتراضي
def setup_database():
    with app.app_context():
        db.create_all()
        
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام',
                role='admin'
            )
            admin_user.set_password('123456')
            db.session.add(admin_user)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي: admin / 123456")

if __name__ == '__main__':
    print("🚀 تشغيل نظام إدارة الاشتراكات المبسط")
    print("="*50)
    
    setup_database()
    
    print("🌐 معلومات الوصول:")
    print("   الرابط: http://localhost:5000")
    print("   المستخدم: admin")
    print("   كلمة المرور: 123456")
    print("="*50)
    print("🚀 جاري تشغيل الخادم...")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
