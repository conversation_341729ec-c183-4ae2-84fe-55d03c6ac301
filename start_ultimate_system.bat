@echo off
chcp 65001 >nul
cd /d "C:\Users\<USER>\Desktop\ammaradenlink"

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
echo ║                                    🚀 نظام إدارة الاشتراكات المتطور والشامل                                    ║
echo ║                                              AdenLink - العراق                                                ║
echo ║                                        النسخة النهائية مع جميع الميزات المتقدمة                                ║
echo ╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🌟 تشغيل النظام المتطور والشامل...
echo 📂 المجلد الحالي: %CD%
echo.
echo 🔍 فحص متطلبات النظام المتطور...
echo 🐍 إصدار Python:
python --version
echo.
echo 📦 فحص المكتبات المطلوبة...
python -c "import flask; print('✅ Flask متاح - الإصدار:', flask.__version__)" 2>nul || echo "❌ Flask غير متاح - يرجى التثبيت"
python -c "import flask_sqlalchemy; print('✅ Flask-SQLAlchemy متاح')" 2>nul || echo "❌ Flask-SQLAlchemy غير متاح"
python -c "import flask_login; print('✅ Flask-Login متاح')" 2>nul || echo "❌ Flask-Login غير متاح"
python -c "import werkzeug; print('✅ Werkzeug متاح')" 2>nul || echo "❌ Werkzeug غير متاح"
echo.
echo ⚡ تشغيل النظام المتطور والشامل...
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╗
echo ║                                          ✨ النظام المتطور والشامل ✨                                          ║
echo ║                                                                                                                  ║
echo ║  🌐 الرابط: http://localhost:5090                                                                              ║
echo ║  👤 اسم المستخدم: admin                                                                                        ║
echo ║  🔑 كلمة المرور: 123456                                                                                        ║
echo ║                                                                                                                  ║
echo ║  🏢 إدارة الاشتراكات المتطورة:                                                                                  ║
echo ║  ✅ 7 مزودي خدمة مدمجين (AdenLink، AWS، GCP، Azure، DigitalOcean، Vultr، Linode)                            ║
echo ║  ✅ 3 أنواع اشتراكات (شهري، نصف سنوي، سنوي)                                                                 ║
echo ║  ✅ معلومات شاملة لكل اشتراك (IP، منفذ، API، أولوية)                                                          ║
echo ║  ✅ صفحة إدارة متطورة مع بطاقات تفاعلية                                                                      ║
echo ║  ✅ صفحة إضافة اشتراك شاملة ومنظمة                                                                           ║
echo ║                                                                                                                  ║
echo ║  💰 إدارة الفواتير والمدفوعات:                                                                                ║
echo ║  ✅ 6 طرق دفع مختلفة مع رسوم معالجة                                                                          ║
echo ║  ✅ فواتير تلقائية مع أرقام فريدة                                                                             ║
echo ║  ✅ حسابات مالية دقيقة (ضرائب، خصومات)                                                                      ║
echo ║  ✅ 5 عملات مدعومة (USD، EUR، IQD، SAR، AED)                                                                 ║
echo ║  ✅ تتبع حالات الدفع المختلفة                                                                                 ║
echo ║                                                                                                                  ║
echo ║  🖥️ واجهة المستخدم المتطورة:                                                                                   ║
echo ║  ✅ لوحة تحكم تفاعلية مع إحصائيات حقيقية                                                                     ║
echo ║  ✅ تصميم Glassmorphism مع تأثيرات نيون                                                                       ║
echo ║  ✅ تأثيرات حركية متطورة وتفاعلية                                                                            ║
echo ║  ✅ تصميم متجاوب مع جميع الأجهزة                                                                             ║
echo ║  ✅ خط Cairo العربي الجميل                                                                                   ║
echo ║                                                                                                                  ║
echo ║  👥 إدارة المستخدمين:                                                                                          ║
echo ║  ✅ نظام صلاحيات متقدم (مدير/مستخدم عادي)                                                                   ║
echo ║  ✅ معلومات مستخدم شاملة (شركة، هاتف، صورة)                                                                  ║
echo ║  ✅ تسجيل دخول آمن مع تشفير                                                                                  ║
echo ║  ✅ إدارة الجلسات المتطورة                                                                                    ║
echo ║                                                                                                                  ║
echo ║  🔔 نظام الإشعارات:                                                                                            ║
echo ║  ✅ تنبيهات انتهاء الاشتراكات (30 يوم مقدماً)                                                               ║
echo ║  ✅ 4 أنواع إشعارات (معلومات، تحذير، خطأ، نجاح)                                                             ║
echo ║  ✅ تتبع حالة القراءة والتواريخ                                                                               ║
echo ║  ✅ ربط بالإجراءات المطلوبة                                                                                   ║
echo ║                                                                                                                  ║
echo ║  🔒 الأمان المتقدم:                                                                                            ║
echo ║  ✅ تشفير كلمات المرور Werkzeug Security                                                                      ║
echo ║  ✅ جلسات آمنة Flask-Login                                                                                    ║
echo ║  ✅ حماية المسارات login_required                                                                             ║
echo ║  ✅ حماية من الهجمات CSRF/XSS                                                                                ║
echo ║                                                                                                                  ║
echo ║  📊 الإحصائيات الحالية:                                                                                        ║
echo ║  🎯 البيانات المدمجة:                                                                                          ║
echo ║     👥 المستخدمين: 1 (المدير العام)                                                                          ║
echo ║     ☁️ مزودي الخدمة: 7 (شامل AdenLink)                                                                       ║
echo ║     💳 طرق الدفع: 6 (متنوعة)                                                                                 ║
echo ║     💰 العملات: 5 (عالمية ومحلية)                                                                            ║
echo ║                                                                                                                  ║
echo ║  📈 الإحصائيات التفاعلية:                                                                                      ║
echo ║     • إجمالي الاشتراكات (حسب المستخدم)                                                                       ║
echo ║     • الاشتراكات النشطة                                                                                       ║
echo ║     • إجمالي الفواتير                                                                                         ║
echo ║     • الفواتير المعلقة                                                                                        ║
echo ║     • إجمالي الإيرادات                                                                                        ║
echo ║     • الاشتراكات التي تنتهي قريباً                                                                            ║
echo ║                                                                                                                  ║
echo ╚══════════════════════════════════════════════════════════════════════════════════════════════════════════════════╝
echo.

python ultimate_subscription_system.py

echo.
echo ⏹️ تم إيقاف النظام المتطور
echo.
echo 📝 ملاحظات مهمة:
echo • النظام يحتوي على جميع الميزات المتقدمة المطلوبة
echo • تصميم Glassmorphism مع تأثيرات نيون متطورة
echo • 7 مزودي خدمة مدمجين بالكامل
echo • 6 طرق دفع مع رسوم معالجة
echo • 5 عملات مدعومة (محلية وعالمية)
echo • نظام إشعارات متقدم مع 4 أنواع
echo • أمان متقدم مع تشفير Werkzeug
echo • إحصائيات تفاعلية حقيقية
echo.
echo 💡 للحصول على الدعم:
echo • راجع ملف ULTIMATE_README.md
echo • تحقق من رسائل الخطأ في الطرفية
echo • تأكد من تثبيت جميع المكتبات المطلوبة
echo • تأكد من أن المنفذ 5090 متاح
echo.
echo 🔄 اضغط أي مفتاح للخروج...
pause >nul
