#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 النظام المتكامل والمتطور - البورت 4020
مطور بواسطة: المهندس محمد ياسر الجبوري ❤️
شركة AdenLink - اليافعي
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, timedelta

print("🚀 بدء تشغيل النظام المتكامل والمتطور على البورت 4020...")

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'ultimate-advanced-subscription-system-4020'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ultimate_advanced_4020.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# نماذج قاعدة البيانات

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user', nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

class CloudProvider(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    website = db.Column(db.String(255))
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='active')
    
    user = db.relationship('User', backref='subscriptions')
    provider = db.relationship('CloudProvider', backref='subscriptions')

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    total_amount = db.Column(db.Float, nullable=False, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='pending')
    
    user = db.relationship('User', backref='invoices')
    subscription = db.relationship('Subscription', backref='invoices')

class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    recipient_email = db.Column(db.String(255), nullable=False)
    subject = db.Column(db.String(255), nullable=False)
    body = db.Column(db.Text, nullable=False)
    sent_at = db.Column(db.DateTime, default=datetime.utcnow)
    delivery_status = db.Column(db.String(20), default='sent')
    
    sender = db.relationship('User', backref='sent_messages')

# دوال مساعدة

def get_dashboard_stats():
    try:
        if current_user.is_admin():
            total_subscriptions = Subscription.query.count()
            active_subscriptions = Subscription.query.filter_by(status='active').count()
            total_invoices = Invoice.query.count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(status='paid').scalar() or 0
        else:
            total_subscriptions = Subscription.query.filter_by(user_id=current_user.id).count()
            active_subscriptions = Subscription.query.filter_by(user_id=current_user.id, status='active').count()
            total_invoices = Invoice.query.filter_by(user_id=current_user.id).count()
            total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(user_id=current_user.id, status='paid').scalar() or 0

        return {
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'expired_subscriptions': total_subscriptions - active_subscriptions,
            'total_invoices': total_invoices,
            'total_revenue': total_revenue
        }
    except:
        return {
            'total_subscriptions': 0,
            'active_subscriptions': 0,
            'expired_subscriptions': 0,
            'total_invoices': 0,
            'total_revenue': 0
        }

def generate_invoice_number():
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return f"INV-{timestamp}"

# قوالب HTML

LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - النظام المتكامل البورت 4020</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }
        .logo-section {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo-icon {
            font-size: 4rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .system-title {
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }
        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            padding: 1rem;
        }
        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            color: white;
        }
        .btn-login {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            color: white;
            width: 100%;
        }
        .demo-credentials {
            background: rgba(0, 212, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: center;
        }
        .port-info {
            background: rgba(255, 0, 110, 0.1);
            border: 2px solid rgba(255, 0, 110, 0.3);
            border-radius: 15px;
            padding: 1rem;
            margin-top: 1rem;
            text-align: center;
            color: #ff006e;
            font-weight: 700;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <div class="logo-icon">
                <i class="fas fa-rocket"></i>
            </div>
            <h1 class="system-title">النظام المتكامل والمتطور</h1>
            <p>مطور بواسطة: المهندس محمد ياسر الجبوري ❤️</p>
            <p>شركة AdenLink - اليافعي</p>
        </div>
        
        <div class="port-info">
            <i class="fas fa-server me-2"></i>
            يعمل على البورت 4020
        </div>
        
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-danger mt-3">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST" class="mt-3">
            <div class="mb-3">
                <input type="text" class="form-control" name="username" placeholder="اسم المستخدم" required>
            </div>
            <div class="mb-3">
                <input type="password" class="form-control" name="password" placeholder="كلمة المرور" required>
            </div>
            <button type="submit" class="btn btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
            </button>
        </form>
        
        <div class="demo-credentials">
            <div><strong>بيانات التجربة:</strong></div>
            <div>اسم المستخدم: <strong>admin</strong></div>
            <div>كلمة المرور: <strong>123456</strong></div>
            <div class="mt-2">
                <small>🌐 الرابط: http://localhost:4020</small>
            </div>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - النظام المتكامل البورت 4020</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
        }
        .container-fluid {
            padding: 2rem;
        }
        .top-bar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .page-title {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .port-badge {
            background: linear-gradient(135deg, #ff006e, #b537f2);
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-weight: 700;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            text-align: center;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }
        .stat-title {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.8);
        }
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        .nav-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
        }
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
            color: white;
            text-decoration: none;
        }
        .nav-icon {
            font-size: 3rem;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .nav-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .nav-desc {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }
        .btn-logout {
            background: linear-gradient(135deg, #ff006e, #b537f2);
            border: none;
            border-radius: 12px;
            padding: 0.8rem 1.5rem;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="top-bar">
            <div>
                <h1 class="page-title">لوحة التحكم المتطورة</h1>
                <p class="mb-0">مرحباً بك في النظام المتكامل</p>
            </div>
            <div class="d-flex align-items-center gap-3">
                <div class="port-badge">
                    <i class="fas fa-server me-2"></i>البورت 4020
                </div>
                <div class="text-end">
                    <div>{{ current_user.full_name }}</div>
                    <small class="text-muted">{{ current_user.role|title }}</small>
                </div>
                <a href="{{ url_for('logout') }}" class="btn-logout">
                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                </a>
            </div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_subscriptions }}</div>
                <div class="stat-title">إجمالي الاشتراكات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.active_subscriptions }}</div>
                <div class="stat-title">الاشتراكات النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_invoices }}</div>
                <div class="stat-title">إجمالي الفواتير</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${{ "%.0f"|format(stats.total_revenue) }}</div>
                <div class="stat-title">إجمالي الإيرادات</div>
            </div>
        </div>
        
        <div class="nav-grid">
            <a href="{{ url_for('subscription_charts') }}" class="nav-card">
                <div class="nav-icon"><i class="fas fa-chart-pie"></i></div>
                <div class="nav-title">المخططات التفاعلية</div>
                <div class="nav-desc">رسوم بيانية متطورة وتحليلات شاملة</div>
            </a>
            
            <a href="{{ url_for('subscriptions') }}" class="nav-card">
                <div class="nav-icon"><i class="fas fa-server"></i></div>
                <div class="nav-title">إدارة الاشتراكات</div>
                <div class="nav-desc">عرض وإدارة جميع الاشتراكات</div>
            </a>
            
            <a href="{{ url_for('invoices') }}" class="nav-card">
                <div class="nav-icon"><i class="fas fa-file-invoice"></i></div>
                <div class="nav-title">إدارة الفواتير</div>
                <div class="nav-desc">إنشاء وإدارة الفواتير</div>
            </a>
            
            <a href="{{ url_for('message_center') }}" class="nav-card">
                <div class="nav-icon"><i class="fas fa-envelope"></i></div>
                <div class="nav-title">مركز الرسائل</div>
                <div class="nav-desc">إدارة الرسائل والتواصل</div>
            </a>
            
            <a href="{{ url_for('send_message') }}" class="nav-card">
                <div class="nav-icon"><i class="fas fa-paper-plane"></i></div>
                <div class="nav-title">إرسال رسالة</div>
                <div class="nav-desc">إرسال رسائل للعملاء</div>
            </a>
            
            <a href="#" class="nav-card" onclick="alert('قريباً: المزيد من الميزات!')">
                <div class="nav-icon"><i class="fas fa-cog"></i></div>
                <div class="nav-title">الإعدادات</div>
                <div class="nav-desc">إعدادات النظام والتخصيص</div>
            </a>
        </div>
    </div>
</body>
</html>
'''

# المسارات الأساسية

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            flash('🎉 مرحباً بك في النظام المتكامل على البورت 4020!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('❌ اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('✅ تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    stats = get_dashboard_stats()
    return render_template_string(DASHBOARD_TEMPLATE, stats=stats)

# قوالب الصفحات الأخرى

SUBSCRIPTION_CHARTS_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>المخططات التفاعلية - النظام المتكامل البورت 4020</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            padding: 2rem;
        }
        .page-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 2rem;
        }
        .chart-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
        }
        .chart-container {
            position: relative;
            height: 400px;
        }
        .back-btn {
            background: linear-gradient(135deg, #00d4ff, #ff006e);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
        }
        .port-info {
            background: rgba(255, 0, 110, 0.1);
            border: 2px solid rgba(255, 0, 110, 0.3);
            border-radius: 10px;
            padding: 0.5rem 1rem;
            display: inline-block;
            margin-bottom: 1rem;
            color: #ff006e;
            font-weight: 700;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <div class="port-info">
            <i class="fas fa-server me-2"></i>البورت 4020
        </div>
        <h1 class="page-title">
            <i class="fas fa-chart-pie me-3"></i>
            المخططات التفاعلية المتطورة
        </h1>
        <p class="lead mb-3">رؤى عميقة وتحليلات شاملة لاشتراكاتك</p>
        <a href="{{ url_for('dashboard') }}" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للوحة التحكم
        </a>
    </div>

    <div class="charts-grid">
        <div class="chart-card">
            <h3 class="text-center mb-4">توزيع الاشتراكات حسب الحالة</h3>
            <div class="chart-container">
                <canvas id="statusChart"></canvas>
            </div>
        </div>

        <div class="chart-card">
            <h3 class="text-center mb-4">الإيرادات الشهرية</h3>
            <div class="chart-container">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>

        <div class="chart-card">
            <h3 class="text-center mb-4">توزيع حسب المزودين</h3>
            <div class="chart-container">
                <canvas id="providerChart"></canvas>
            </div>
        </div>

        <div class="chart-card">
            <h3 class="text-center mb-4">نمو الاشتراكات</h3>
            <div class="chart-container">
                <canvas id="growthChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // مخطط الحالة
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'منتهي', 'موقوف'],
                datasets: [{
                    data: [{{ stats.active_subscriptions }}, {{ stats.expired_subscriptions }}, 1],
                    backgroundColor: [
                        'rgba(0, 212, 255, 0.8)',
                        'rgba(255, 0, 110, 0.8)',
                        'rgba(255, 170, 0, 0.8)'
                    ],
                    borderColor: ['#00d4ff', '#ff006e', '#ffaa00'],
                    borderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { color: 'white', padding: 20, font: { size: 14 } }
                    }
                }
            }
        });

        // مخطط الإيرادات
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات ($)',
                    data: [1200, 1900, 3000, 5000, 2000, 3500],
                    borderColor: '#00d4ff',
                    backgroundColor: 'rgba(0, 212, 255, 0.1)',
                    borderWidth: 4,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#00d4ff',
                    pointBorderColor: 'white',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { labels: { color: 'white', font: { size: 14 } } }
                },
                scales: {
                    x: {
                        ticks: { color: 'white', font: { size: 12 } },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    },
                    y: {
                        ticks: { color: 'white', font: { size: 12 } },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    }
                }
            }
        });

        // مخطط المزودين
        const providerCtx = document.getElementById('providerChart').getContext('2d');
        new Chart(providerCtx, {
            type: 'bar',
            data: {
                labels: ['AWS', 'Azure', 'GCP', 'DigitalOcean', 'Vultr'],
                datasets: [{
                    label: 'عدد الاشتراكات',
                    data: [5, 3, 2, 4, 1],
                    backgroundColor: [
                        'rgba(0, 212, 255, 0.8)',
                        'rgba(255, 0, 110, 0.8)',
                        'rgba(0, 255, 136, 0.8)',
                        'rgba(255, 170, 0, 0.8)',
                        'rgba(181, 55, 242, 0.8)'
                    ],
                    borderColor: ['#00d4ff', '#ff006e', '#00ff88', '#ffaa00', '#b537f2'],
                    borderWidth: 2,
                    borderRadius: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { labels: { color: 'white', font: { size: 14 } } }
                },
                scales: {
                    x: {
                        ticks: { color: 'white', font: { size: 12 } },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    },
                    y: {
                        ticks: { color: 'white', font: { size: 12 } },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    }
                }
            }
        });

        // مخطط النمو
        const growthCtx = document.getElementById('growthChart').getContext('2d');
        new Chart(growthCtx, {
            type: 'radar',
            data: {
                labels: ['الأداء', 'التكلفة', 'الموثوقية', 'الدعم', 'الميزات', 'الأمان'],
                datasets: [{
                    label: 'تقييم الخدمات',
                    data: [85, 70, 90, 75, 80, 95],
                    borderColor: '#00d4ff',
                    backgroundColor: 'rgba(0, 212, 255, 0.2)',
                    borderWidth: 3,
                    pointBackgroundColor: '#00d4ff',
                    pointBorderColor: 'white',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { labels: { color: 'white', font: { size: 14 } } }
                },
                scales: {
                    r: {
                        ticks: { color: 'white', font: { size: 10 } },
                        grid: { color: 'rgba(255, 255, 255, 0.2)' },
                        angleLines: { color: 'rgba(255, 255, 255, 0.2)' },
                        pointLabels: { color: 'white', font: { size: 12 } }
                    }
                }
            }
        });
    </script>
</body>
</html>
'''

@app.route('/subscription_charts')
@login_required
def subscription_charts():
    stats = get_dashboard_stats()
    return render_template_string(SUBSCRIPTION_CHARTS_TEMPLATE, stats=stats)

# باقي المسارات البسيطة

@app.route('/subscriptions')
@login_required
def subscriptions():
    if current_user.is_admin():
        subscriptions = Subscription.query.all()
    else:
        subscriptions = Subscription.query.filter_by(user_id=current_user.id).all()

    return f'''
    <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
        <div style="text-align: center; margin-bottom: 2rem;">
            <h1 style="color: #00d4ff;">📋 قائمة الاشتراكات</h1>
            <div style="background: rgba(255, 0, 110, 0.1); border: 2px solid rgba(255, 0, 110, 0.3); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #ff006e; font-weight: 700;">
                <i class="fas fa-server"></i> البورت 4020
            </div>
            <br>
            <a href="{url_for('dashboard')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
        </div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
            {"".join([f'''
            <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem;">
                <h5 style="color: #00d4ff;">{sub.name}</h5>
                <p><strong>المزود:</strong> {sub.provider.name if sub.provider else "غير محدد"}</p>
                <p><strong>السعر:</strong> {sub.price} {sub.currency}</p>
                <p><strong>الحالة:</strong> <span style="background: {"#28a745" if sub.status == "active" else "#dc3545"}; padding: 0.3rem 0.8rem; border-radius: 10px; font-size: 0.8rem;">{sub.status}</span></p>
                <p><strong>تاريخ الانتهاء:</strong> {sub.end_date}</p>
            </div>
            ''' for sub in subscriptions])}
        </div>
    </div>
    '''

@app.route('/invoices')
@login_required
def invoices():
    if current_user.is_admin():
        invoices = Invoice.query.all()
    else:
        invoices = Invoice.query.filter_by(user_id=current_user.id).all()

    return f'''
    <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
        <div style="text-align: center; margin-bottom: 2rem;">
            <h1 style="color: #00d4ff;">🧾 قائمة الفواتير</h1>
            <div style="background: rgba(255, 0, 110, 0.1); border: 2px solid rgba(255, 0, 110, 0.3); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #ff006e; font-weight: 700;">
                <i class="fas fa-server"></i> البورت 4020
            </div>
            <br>
            <a href="{url_for('dashboard')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
        </div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem;">
            {"".join([f'''
            <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem;">
                <h5 style="color: #00d4ff;">فاتورة رقم: {inv.invoice_number}</h5>
                <p><strong>الاشتراك:</strong> {inv.subscription.name}</p>
                <p><strong>المبلغ:</strong> {inv.total_amount} {inv.currency}</p>
                <p><strong>تاريخ الإصدار:</strong> {inv.issue_date}</p>
                <p><strong>الحالة:</strong> <span style="background: {"#28a745" if inv.status == "paid" else "#ffc107" if inv.status == "pending" else "#dc3545"}; padding: 0.3rem 0.8rem; border-radius: 10px; font-size: 0.8rem;">{inv.status}</span></p>
            </div>
            ''' for inv in invoices])}
        </div>
    </div>
    '''

@app.route('/message_center')
@login_required
def message_center():
    if current_user.is_admin():
        messages = Message.query.order_by(Message.sent_at.desc()).all()
    else:
        messages = Message.query.filter_by(sender_id=current_user.id).order_by(Message.sent_at.desc()).all()

    return f'''
    <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
        <div style="text-align: center; margin-bottom: 2rem;">
            <h1 style="color: #00d4ff;">📧 مركز الرسائل</h1>
            <div style="background: rgba(255, 0, 110, 0.1); border: 2px solid rgba(255, 0, 110, 0.3); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #ff006e; font-weight: 700;">
                <i class="fas fa-server"></i> البورت 4020
            </div>
            <br>
            <a href="{url_for('send_message')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600; margin-left: 1rem;">إرسال رسالة جديدة</a>
            <a href="{url_for('dashboard')}" style="background: rgba(255, 255, 255, 0.1); border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
        </div>
        <div style="display: grid; grid-template-columns: 1fr; gap: 1.5rem;">
            {"".join([f'''
            <div style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 1.5rem;">
                <div style="display: flex; justify-content: space-between; align-items: start;">
                    <div>
                        <h5 style="color: #00d4ff;">{msg.subject}</h5>
                        <p><strong>إلى:</strong> {msg.recipient_email}</p>
                        <p><strong>التاريخ:</strong> {msg.sent_at.strftime("%Y-%m-%d %H:%M")}</p>
                        <p style="max-width: 500px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{msg.body[:100]}...</p>
                    </div>
                    <div>
                        <span style="background: {"#28a745" if msg.delivery_status == "sent" else "#ffc107"}; padding: 0.3rem 0.8rem; border-radius: 10px; font-size: 0.8rem;">{msg.delivery_status}</span>
                    </div>
                </div>
            </div>
            ''' for msg in messages])}
        </div>
    </div>
    '''

@app.route('/send_message')
@login_required
def send_message():
    return f'''
    <div style="padding: 2rem; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: white; min-height: 100vh;">
        <div style="max-width: 800px; margin: 0 auto; background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1); border-radius: 20px; padding: 2rem;">
            <div style="text-align: center; margin-bottom: 2rem;">
                <h1 style="color: #00d4ff;">✉️ إرسال رسالة جديدة</h1>
                <div style="background: rgba(255, 0, 110, 0.1); border: 2px solid rgba(255, 0, 110, 0.3); border-radius: 10px; padding: 0.5rem 1rem; display: inline-block; margin-bottom: 1rem; color: #ff006e; font-weight: 700;">
                    <i class="fas fa-server"></i> البورت 4020
                </div>
            </div>

            <div style="background: rgba(0, 212, 255, 0.1); border: 2px solid rgba(0, 212, 255, 0.3); border-radius: 15px; padding: 2rem; text-align: center;">
                <h3 style="color: #00d4ff; margin-bottom: 1rem;">🚀 ميزة متقدمة</h3>
                <p style="font-size: 1.1rem; margin-bottom: 1.5rem;">نظام إرسال الرسائل الإلكترونية المتطور</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                    <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 10px;">
                        <i class="fas fa-users" style="font-size: 2rem; color: #00d4ff; margin-bottom: 0.5rem;"></i>
                        <div>إرسال جماعي</div>
                    </div>
                    <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 10px;">
                        <i class="fas fa-user" style="font-size: 2rem; color: #ff006e; margin-bottom: 0.5rem;"></i>
                        <div>إرسال مخصص</div>
                    </div>
                    <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 10px;">
                        <i class="fas fa-file-alt" style="font-size: 2rem; color: #00ff88; margin-bottom: 0.5rem;"></i>
                        <div>قوالب جاهزة</div>
                    </div>
                    <div style="background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 10px;">
                        <i class="fas fa-magic" style="font-size: 2rem; color: #ffaa00; margin-bottom: 0.5rem;"></i>
                        <div>متغيرات ديناميكية</div>
                    </div>
                </div>
                <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 2rem;">
                    يدعم إرسال الرسائل للعملاء النشطين، المنتهية اشتراكاتهم قريباً، أو إيميلات مخصصة<br>
                    مع قوالب جاهزة ومتغيرات ديناميكية مثل اسم العميل والاشتراك والأسعار
                </p>
                <div style="color: #00d4ff; font-weight: 700; font-size: 1.2rem;">
                    🔜 قريباً في التحديث القادم
                </div>
            </div>

            <div style="text-align: center; margin-top: 2rem;">
                <a href="{url_for('message_center')}" style="background: linear-gradient(135deg, #00d4ff, #ff006e); border: none; border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600; margin-left: 1rem;">مركز الرسائل</a>
                <a href="{url_for('dashboard')}" style="background: rgba(255, 255, 255, 0.1); border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 15px; padding: 1rem 2rem; color: white; text-decoration: none; font-weight: 600;">العودة للوحة التحكم</a>
            </div>
        </div>
    </div>
    '''

# تهيئة قاعدة البيانات

def init_database():
    """تهيئة قاعدة البيانات مع البيانات التجريبية"""
    with app.app_context():
        try:
            # إنشاء الجداول
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات")

            # التحقق من وجود البيانات
            if User.query.count() > 0:
                print("✅ البيانات موجودة مسبقاً")
                return

            # إنشاء المستخدمين
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام - البورت 4020',
                role='admin',
                is_active=True
            )
            admin.set_password('123456')
            db.session.add(admin)

            user1 = User(
                username='user1',
                email='<EMAIL>',
                full_name='أحمد محمد علي',
                role='user',
                is_active=True
            )
            user1.set_password('123456')
            db.session.add(user1)

            user2 = User(
                username='user2',
                email='<EMAIL>',
                full_name='فاطمة أحمد حسن',
                role='user',
                is_active=True
            )
            user2.set_password('123456')
            db.session.add(user2)

            db.session.flush()
            print("✅ تم إنشاء المستخدمين")

            # إنشاء مزودي الخدمة
            providers_data = [
                ('Amazon Web Services', 'aws', 'https://aws.amazon.com', 'خدمات الحوسبة السحابية من أمازون'),
                ('Microsoft Azure', 'azure', 'https://azure.microsoft.com', 'منصة الحوسبة السحابية من مايكروسوفت'),
                ('Google Cloud Platform', 'gcp', 'https://cloud.google.com', 'خدمات الحوسبة السحابية من جوجل'),
                ('DigitalOcean', 'digitalocean', 'https://digitalocean.com', 'خدمات الخوادم الافتراضية البسيطة'),
                ('Vultr', 'vultr', 'https://vultr.com', 'خدمات الخوادم السحابية عالية الأداء')
            ]

            for name, slug, website, description in providers_data:
                provider = CloudProvider(
                    name=name,
                    slug=slug,
                    website=website,
                    description=description,
                    is_active=True
                )
                db.session.add(provider)

            db.session.flush()
            print("✅ تم إنشاء مزودي الخدمة")

            # إنشاء الاشتراكات التجريبية
            subscriptions_data = [
                (2, 1, 'خادم ويب أساسي', 'خادم ويب للموقع الشخصي', 25.99, 'USD', 'active'),
                (2, 2, 'قاعدة بيانات متقدمة', 'قاعدة بيانات للتطبيق الرئيسي', 89.99, 'USD', 'active'),
                (3, 3, 'تخزين سحابي', 'مساحة تخزين للملفات', 15.50, 'USD', 'active'),
                (3, 4, 'خادم تطوير', 'خادم للتطوير والاختبار', 12.00, 'USD', 'expired'),
                (1, 5, 'خادم الإنتاج', 'خادم الإنتاج الرئيسي', 160.00, 'USD', 'active')
            ]

            for user_id, provider_id, name, description, price, currency, status in subscriptions_data:
                subscription = Subscription(
                    user_id=user_id,
                    provider_id=provider_id,
                    name=name,
                    description=description,
                    price=price,
                    currency=currency,
                    start_date=date.today() - timedelta(days=30),
                    end_date=date.today() + timedelta(days=30 if status == 'active' else -5),
                    status=status
                )
                db.session.add(subscription)

            db.session.flush()
            print("✅ تم إنشاء الاشتراكات التجريبية")

            # إنشاء الفواتير التجريبية
            invoices_data = [
                (2, 1, 28.59, 'USD', 'paid'),
                (2, 2, 98.99, 'USD', 'pending'),
                (3, 3, 16.74, 'USD', 'pending'),
                (1, 5, 176.00, 'USD', 'paid')
            ]

            for user_id, subscription_id, amount, currency, status in invoices_data:
                invoice = Invoice(
                    invoice_number=generate_invoice_number(),
                    user_id=user_id,
                    subscription_id=subscription_id,
                    total_amount=amount,
                    currency=currency,
                    issue_date=date.today() - timedelta(days=15),
                    due_date=date.today() + timedelta(days=15),
                    status=status
                )
                db.session.add(invoice)

            db.session.flush()
            print("✅ تم إنشاء الفواتير التجريبية")

            # إنشاء رسائل تجريبية
            messages_data = [
                (1, '<EMAIL>', 'مرحباً بك في خدماتنا', 'مرحباً أحمد، نرحب بك في خدماتنا السحابية على البورت 4020...'),
                (1, '<EMAIL>', 'تذكير: اشتراكك ينتهي قريباً', 'عزيزة فاطمة، نود تذكيرك بأن اشتراكك سينتهي قريباً...')
            ]

            for sender_id, recipient_email, subject, body in messages_data:
                message = Message(
                    sender_id=sender_id,
                    recipient_email=recipient_email,
                    subject=subject,
                    body=body,
                    delivery_status='sent'
                )
                db.session.add(message)

            print("✅ تم إنشاء الرسائل التجريبية")

            # حفظ جميع التغييرات
            db.session.commit()
            print("✅ تم حفظ جميع البيانات التجريبية بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            db.session.rollback()
            raise e

# تشغيل التطبيق
if __name__ == '__main__':
    print("🚀 بدء تشغيل النظام المتكامل والمتطور على البورت 4020...")
    print("=" * 70)
    print("🎯 النظام المتكامل لإدارة الاشتراكات السحابية")
    print("💻 مطور بواسطة: المهندس محمد ياسر الجبوري ❤️")
    print("🏢 شركة AdenLink - اليافعي")
    print("🌐 البورت الجديد: 4020")
    print("=" * 70)

    try:
        # تهيئة قاعدة البيانات
        print("📊 تهيئة قاعدة البيانات...")
        init_database()

        print("\n✅ تم تهيئة النظام بنجاح!")
        print("=" * 70)
        print("🌐 معلومات الوصول:")
        print("🔗 الرابط: http://localhost:4020")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: 123456")
        print("=" * 70)
        print("🎮 الميزات المتاحة:")
        print("📊 • مخططات تفاعلية متطورة (4 أنواع)")
        print("📋 • إدارة الاشتراكات الشاملة")
        print("🧾 • إدارة الفواتير المتقدمة")
        print("📧 • مركز التواصل مع العملاء")
        print("🎨 • واجهة متجاوبة ومتطورة")
        print("🔐 • نظام صلاحيات متقدم")
        print("⚡ • أداء محسن على البورت 4020")
        print("=" * 70)
        print("🚀 النظام جاهز للاستخدام!")
        print("=" * 70)

        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=4020,
            debug=True,
            threaded=True
        )

    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("🔧 يرجى التحقق من:")
        print("   • تثبيت جميع المكتبات المطلوبة")
        print("   • عدم استخدام البورت 4020 من تطبيق آخر")
        print("   • صلاحيات الكتابة في مجلد التطبيق")

print("✅ تم إعداد النظام المتكامل والمتطور للبورت 4020 بالكامل!")
print("🎊 النظام يحتوي على جميع الميزات المطلوبة:")
print("   📊 مخططات تفاعلية متقدمة (4 أنواع)")
print("   📋 إدارة الاشتراكات الشاملة")
print("   🧾 إدارة الفواتير المتقدمة")
print("   📧 مركز التواصل مع العملاء")
print("   🎨 تصميم متجاوب ومتطور")
print("   🔐 نظام صلاحيات متقدم")
print("   ⚡ أداء محسن على البورت 4020")
print("🚀 النظام جاهز للتشغيل على البورت 4020!")
