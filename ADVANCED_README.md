# 🚀 نظام إدارة الاشتراكات المتقدم
## AdenLink - العراق

### 📋 نظرة عامة
نظام إدارة اشتراكات متكامل ومتطور مصمم خصيصاً لإدارة الخوادم السحابية والاستضافة مع واجهة مستخدم عصرية وميزات متقدمة.

---

## ✨ الميزات الرئيسية

### 🎛️ **لوحة التحكم المتقدمة**
- إحصائيات مباشرة ومتحركة
- مخططات بيانية تفاعلية
- تنبيهات ذكية
- واجهة مستخدم عصرية ومتجاوبة

### 🔧 **إدارة الاشتراكات**
- ✅ مخطط الاشتراكات التفاعلي
- ✅ قائمة الاشتراكات الشاملة
- ✅ إدارة طرق الدفع
- ✅ إضافة اشتراكات جديدة
- ✅ تحليلات الاشتراكات المتقدمة
- ✅ تقارير مفصلة

### 💰 **إدارة الفواتير**
- ✅ قائمة الفواتير مع البحث والفلترة
- ✅ إنشاء فواتير جديدة تلقائياً
- ✅ تقارير الفواتير والمدفوعات
- ✅ كشف حساب العملاء (جديد)
- ✅ تتبع المدفوعات والمتأخرات

### 📧 **مركز التواصل**
- إرسال إشعارات للعملاء
- قوالب رسائل جاهزة
- تتبع حالة الرسائل

### 📊 **التقارير والإحصائيات**
- تقارير شاملة للاشتراكات
- إحصائيات الإيرادات
- تحليل أداء الخدمات
- تقارير قابلة للتصدير

### 👥 **إدارة المستخدمين**
- نظام صلاحيات متقدم
- أدوار مختلفة (مدير، موظف، مشاهد)
- تتبع نشاط المستخدمين

### ⚙️ **الإدارة المتقدمة**
- إعدادات النظام
- النسخ الاحتياطي والاستعادة
- تكامل مع APIs خارجية
- سجل الأنشطة

---

## 🛠️ متطلبات النظام

### البرمجيات المطلوبة:
- **Python 3.8+**
- **Flask 2.0+**
- **SQLAlchemy**
- **Flask-Login**

### المكتبات المطلوبة:
```bash
pip install flask flask-sqlalchemy flask-login werkzeug
```

---

## 🚀 طريقة التشغيل

### 1. التشغيل السريع:
```bash
# تشغيل مباشر
python advanced_subscription_manager.py

# أو استخدام ملف batch
start_advanced_system.bat
```

### 2. معلومات الوصول:
- **الرابط:** http://localhost:5090
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `123456`

---

## 🎨 واجهة المستخدم

### القائمة الجانبية التفاعلية:
- **لوحة المعلومات** - إحصائيات شاملة
- **إدارة الاشتراكات** - 6 أقسام فرعية
- **إدارة الفواتير** - 4 أقسام فرعية
- **مركز التواصل** - إرسال الإشعارات
- **التقارير العامة** - تقارير شاملة
- **إدارة المستخدمين** - التحكم بالصلاحيات
- **الإدارة المتقدمة** - إعدادات النظام

### الميزات التفاعلية:
- ✨ تأثيرات حركية سلسة
- 🎨 ألوان متدرجة جميلة
- 📱 تصميم متجاوب للجوال
- 🔍 بحث متقدم في القائمة
- 🔔 إشعارات مباشرة

---

## 📊 قاعدة البيانات

### الجداول الرئيسية:
- **Users** - المستخدمين والصلاحيات
- **Customers** - بيانات العملاء
- **Services** - الخدمات المتاحة
- **Subscriptions** - الاشتراكات
- **Invoices** - الفواتير
- **Payments** - المدفوعات
- **Notifications** - الإشعارات
- **ActivityLog** - سجل الأنشطة

### البيانات التجريبية:
يتم إنشاء بيانات تجريبية تلقائياً عند التشغيل الأول:
- 3 عملاء تجريبيين
- 4 خدمات مختلفة
- 3 اشتراكات نشطة
- 2 فاتورة معلقة

---

## 🔧 التخصيص والتطوير

### إضافة ميزات جديدة:
1. إضافة المسار في `advanced_subscription_manager.py`
2. إنشاء القالب المناسب
3. تحديث القائمة الجانبية
4. إضافة الصلاحيات المطلوبة

### تخصيص التصميم:
- تعديل متغيرات CSS في `:root`
- إضافة ألوان جديدة للتدرجات
- تخصيص الرسوم المتحركة

---

## 📁 هيكل الملفات

```
📦 نظام إدارة الاشتراكات
├── 📄 advanced_subscription_manager.py  # الملف الرئيسي
├── 📄 start_advanced_system.bat         # ملف التشغيل
├── 📄 ADVANCED_README.md                # دليل الاستخدام
├── 📄 fixed_system.py                   # النسخة السابقة
└── 📁 instance/
    └── 📄 advanced_subscriptions.db     # قاعدة البيانات
```

---

## 🆘 الدعم والمساعدة

### مشاكل شائعة:
1. **خطأ في المكتبات:** تأكد من تثبيت جميع المتطلبات
2. **مشكلة في المنفذ:** تأكد من أن المنفذ 5090 متاح
3. **مشكلة في قاعدة البيانات:** احذف ملف `.db` وأعد التشغيل

### للحصول على الدعم:
- راجع ملف README
- تحقق من رسائل الخطأ في الطرفية
- تأكد من متطلبات النظام

---

## 📈 التحديثات المستقبلية

### الميزات المخططة:
- [ ] تكامل مع بوابات الدفع
- [ ] تطبيق جوال
- [ ] API متقدم
- [ ] نظام تذاكر الدعم
- [ ] تقارير متقدمة مع مخططات
- [ ] نظام النسخ الاحتياطي التلقائي

---

## 👨‍💻 المطور

**تم تطوير النظام بواسطة:** فريق AdenLink التقني  
**الإصدار:** 2.0 المتقدم  
**تاريخ الإصدار:** 2024  
**الترخيص:** للاستخدام الداخلي

---

## 🎉 شكر خاص

شكراً لاستخدام نظام إدارة الاشتراكات المتقدم!  
نتطلع لتطوير المزيد من الميزات المبتكرة.

**AdenLink - العراق** 🇮🇶
