{% extends "base.html" %}

{% block title %}إضافة اشتراك جديد - نظام إدارة الاشتراكات{% endblock %}

{% block extra_css %}
<style>
.add-subscription-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.add-subscription-title {
    color: #00f5ff;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 0 20px #00f5ff;
    margin-bottom: 10px;
}

.form-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.form-section {
    margin-bottom: 40px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.section-title {
    color: #bf00ff;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #bf00ff;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-left: 10px;
    font-size: 1.1rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    width: 100%;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: #00f5ff;
    box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
    color: white;
    outline: none;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-select {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    padding: 12px 15px;
    backdrop-filter: blur(10px);
}

.form-select option {
    background: #1a1a1a;
    color: white;
}

.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.form-check-input {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    margin-left: 10px;
}

.form-check-input:checked {
    background: #00f5ff;
    border-color: #00f5ff;
}

.form-check-label {
    color: rgba(255, 255, 255, 0.8);
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-submit {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 15px;
    color: white;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-cancel {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    color: rgba(255, 255, 255, 0.8);
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-cancel:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-3px);
    text-decoration: none;
}

.error-message {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid rgba(255, 0, 0, 0.3);
    color: #ff6b6b;
    padding: 10px 15px;
    border-radius: 8px;
    margin-top: 5px;
    font-size: 0.9rem;
}

.help-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    margin-top: 5px;
}

@media (max-width: 768px) {
    .add-subscription-title {
        font-size: 2rem;
    }
    
    .form-container {
        padding: 25px 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-submit,
    .btn-cancel {
        width: 100%;
        max-width: 300px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="add-subscription-header">
        <h1 class="add-subscription-title">
            <i class="fas fa-plus-circle me-3"></i>
            إضافة اشتراك جديد
        </h1>
        <p style="color: rgba(255, 255, 255, 0.7); font-size: 1.1rem;">
            أضف اشتراك جديد مع جميع التفاصيل والمعلومات المطلوبة
        </p>
    </div>
    
    <!-- Form -->
    <div class="form-container">
        <form method="POST" novalidate>
            {{ form.hidden_tag() }}
            
            <!-- Basic Information -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    المعلومات الأساسية
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control", placeholder="مثال: Netflix Premium") }}
                        {% if form.name.errors %}
                            <div class="error-message">
                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.provider.label(class="form-label") }}
                        {{ form.provider(class="form-control", placeholder="مثال: Netflix") }}
                        {% if form.provider.errors %}
                            <div class="error-message">
                                {% for error in form.provider.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.category.label(class="form-label") }}
                        {{ form.category(class="form-select") }}
                        {% if form.category.errors %}
                            <div class="error-message">
                                {% for error in form.category.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.priority.label(class="form-label") }}
                        {{ form.priority(class="form-select") }}
                        {% if form.priority.errors %}
                            <div class="error-message">
                                {% for error in form.priority.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-group">
                    {{ form.description.label(class="form-label") }}
                    {{ form.description(class="form-control", rows="3", placeholder="وصف مختصر للاشتراك...") }}
                    {% if form.description.errors %}
                        <div class="error-message">
                            {% for error in form.description.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Server Information -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-server"></i>
                    معلومات السيرفر (اختيارية)
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.server_name.label(class="form-label") }}
                        {{ form.server_name(class="form-control", placeholder="اسم السيرفر") }}
                    </div>
                    
                    <div class="form-group">
                        {{ form.server_ip.label(class="form-label") }}
                        {{ form.server_ip(class="form-control", placeholder="***********") }}
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.port.label(class="form-label") }}
                        {{ form.port(class="form-control", placeholder="8080") }}
                    </div>
                    
                    <div class="form-group">
                        {{ form.api_key.label(class="form-label") }}
                        {{ form.api_key(class="form-control", placeholder="مفتاح API") }}
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.username.label(class="form-label") }}
                        {{ form.username(class="form-control", placeholder="اسم المستخدم") }}
                    </div>
                    
                    <div class="form-group">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control", placeholder="كلمة المرور") }}
                    </div>
                </div>
            </div>
            
            <!-- Subscription Details -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-credit-card"></i>
                    تفاصيل الاشتراك
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.subscription_type.label(class="form-label") }}
                        {{ form.subscription_type(class="form-select") }}
                        {% if form.subscription_type.errors %}
                            <div class="error-message">
                                {% for error in form.subscription_type.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.price.label(class="form-label") }}
                        {{ form.price(class="form-control", placeholder="0.00", step="0.01") }}
                        {% if form.price.errors %}
                            <div class="error-message">
                                {% for error in form.price.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.currency.label(class="form-label") }}
                        {{ form.currency(class="form-select") }}
                        {% if form.currency.errors %}
                            <div class="error-message">
                                {% for error in form.currency.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.start_date.label(class="form-label") }}
                        {{ form.start_date(class="form-control") }}
                        {% if form.start_date.errors %}
                            <div class="error-message">
                                {% for error in form.start_date.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.end_date.label(class="form-label") }}
                        {{ form.end_date(class="form-control") }}
                        {% if form.end_date.errors %}
                            <div class="error-message">
                                {% for error in form.end_date.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-check">
                    {{ form.auto_renewal(class="form-check-input") }}
                    {{ form.auto_renewal.label(class="form-check-label") }}
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-tags"></i>
                    معلومات إضافية
                </h3>
                
                <div class="form-group">
                    {{ form.tags.label(class="form-label") }}
                    {{ form.tags(class="form-control", placeholder="علامة1, علامة2, علامة3") }}
                    <div class="help-text">أدخل العلامات مفصولة بفواصل</div>
                </div>
                
                <div class="form-group">
                    {{ form.notes.label(class="form-label") }}
                    {{ form.notes(class="form-control", rows="4", placeholder="ملاحظات إضافية...") }}
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="form-actions">
                <button type="submit" class="btn-submit crystal-btn ripple">
                    <i class="fas fa-save me-2"></i>
                    حفظ الاشتراك
                </button>
                
                <a href="{{ url_for('subscriptions') }}" class="btn-cancel crystal-btn">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الظهور التدريجي للأقسام
    const sections = document.querySelectorAll('.form-section');
    sections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            section.style.transition = 'all 0.6s ease';
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, index * 200);
    });
    
    // تحديث تاريخ النهاية تلقائياً عند تغيير نوع الاشتراك
    const subscriptionType = document.getElementById('subscription_type');
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    function updateEndDate() {
        if (startDate.value && subscriptionType.value) {
            const start = new Date(startDate.value);
            let end = new Date(start);
            
            if (subscriptionType.value === 'monthly') {
                end.setMonth(end.getMonth() + 1);
            } else if (subscriptionType.value === 'yearly') {
                end.setFullYear(end.getFullYear() + 1);
            }
            
            if (subscriptionType.value !== 'lifetime') {
                endDate.value = end.toISOString().split('T')[0];
            }
        }
    }
    
    if (subscriptionType && startDate && endDate) {
        subscriptionType.addEventListener('change', updateEndDate);
        startDate.addEventListener('change', updateEndDate);
    }
    
    // تعيين تاريخ اليوم كتاريخ افتراضي للبداية
    if (startDate && !startDate.value) {
        const today = new Date();
        startDate.value = today.toISOString().split('T')[0];
        updateEndDate();
    }
});
</script>
{% endblock %}
